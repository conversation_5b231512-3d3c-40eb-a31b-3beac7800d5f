/**
 * Jest setup file for MCP Client tests
 */

// Set test timeout
jest.setTimeout(10000);

// Setup fetch mock
require('jest-fetch-mock').enableMocks();

// Mock console methods to reduce noise in tests
const originalConsoleWarn = console.warn;
const originalConsoleError = console.error;

beforeEach(() => {
  // Mock console.warn and console.error to avoid noise in test output
  console.warn = jest.fn();
  console.error = jest.fn();
});

afterEach(() => {
  // Restore original console methods
  console.warn = originalConsoleWarn;
  console.error = originalConsoleError;
  
  // Clear all timers
  jest.clearAllTimers();
  
  // Clear all mocks
  jest.clearAllMocks();
});

// Global test utilities
global.testUtils = {
  // Helper to create mock fetch responses
  createMockResponse: (data, options = {}) => ({
    ok: options.ok !== false,
    status: options.status || 200,
    statusText: options.statusText || 'OK',
    json: jest.fn().mockResolvedValue(data),
    ...options
  }),
  
  // Helper to create mock error responses
  createMockErrorResponse: (status, message, code, details) => ({
    ok: false,
    status,
    statusText: message,
    json: jest.fn().mockResolvedValue({
      message,
      code,
      details
    })
  }),
  
  // Helper to wait for promises to resolve
  waitForPromises: () => new Promise(resolve => setImmediate(resolve)),
  
  // Helper to advance timers and wait for promises
  advanceTimersAndWait: async (ms) => {
    jest.advanceTimersByTime(ms);
    await global.testUtils.waitForPromises();
  }
};

// Mock environment variables
process.env.NODE_ENV = 'test';

// Suppress specific warnings in tests
const originalConsoleLog = console.log;
console.log = (...args) => {
  // Filter out specific log messages that are expected in tests
  const message = args.join(' ');
  if (message.includes('Circuit breaker OPENED') || 
      message.includes('MCP')) {
    return; // Suppress these messages in tests
  }
  originalConsoleLog.apply(console, args);
};
