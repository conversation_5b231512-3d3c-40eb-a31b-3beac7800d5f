{"/Users/<USER>/Projects/packages/jest-fetch-mock/tests/api.js": {"path": "/Users/<USER>/Projects/packages/jest-fetch-mock/tests/api.js", "statementMap": {"0": {"start": {"line": 4, "column": 2}, "end": {"line": 14, "column": 3}}, "1": {"start": {"line": 5, "column": 18}, "end": {"line": 7, "column": 5}}, "2": {"start": {"line": 6, "column": 6}, "end": {"line": 6, "column": 16}}, "3": {"start": {"line": 8, "column": 18}, "end": {"line": 8, "column": 71}}, "4": {"start": {"line": 8, "column": 60}, "end": {"line": 8, "column": 70}}, "5": {"start": {"line": 9, "column": 4}, "end": {"line": 9, "column": 38}}, "6": {"start": {"line": 10, "column": 9}, "end": {"line": 14, "column": 3}}, "7": {"start": {"line": 11, "column": 4}, "end": {"line": 11, "column": 63}}, "8": {"start": {"line": 11, "column": 52}, "end": {"line": 11, "column": 62}}, "9": {"start": {"line": 13, "column": 4}, "end": {"line": 13, "column": 62}}, "10": {"start": {"line": 13, "column": 51}, "end": {"line": 13, "column": 61}}, "11": {"start": {"line": 18, "column": 2}, "end": {"line": 22, "column": 3}}, "12": {"start": {"line": 19, "column": 4}, "end": {"line": 19, "column": 62}}, "13": {"start": {"line": 19, "column": 51}, "end": {"line": 19, "column": 61}}, "14": {"start": {"line": 21, "column": 4}, "end": {"line": 21, "column": 33}}}, "fnMap": {"0": {"name": "APIRequest", "decl": {"start": {"line": 3, "column": 22}, "end": {"line": 3, "column": 32}}, "loc": {"start": {"line": 3, "column": 38}, "end": {"line": 15, "column": 1}}, "line": 3}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 5, "column": 71}, "end": {"line": 5, "column": 72}}, "loc": {"start": {"line": 6, "column": 6}, "end": {"line": 6, "column": 16}}, "line": 6}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 8, "column": 53}, "end": {"line": 8, "column": 54}}, "loc": {"start": {"line": 8, "column": 60}, "end": {"line": 8, "column": 70}}, "line": 8}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 11, "column": 45}, "end": {"line": 11, "column": 46}}, "loc": {"start": {"line": 11, "column": 52}, "end": {"line": 11, "column": 62}}, "line": 11}, "4": {"name": "(anonymous_4)", "decl": {"start": {"line": 13, "column": 44}, "end": {"line": 13, "column": 45}}, "loc": {"start": {"line": 13, "column": 51}, "end": {"line": 13, "column": 61}}, "line": 13}, "5": {"name": "APIRequest2", "decl": {"start": {"line": 17, "column": 16}, "end": {"line": 17, "column": 27}}, "loc": {"start": {"line": 17, "column": 33}, "end": {"line": 23, "column": 1}}, "line": 17}, "6": {"name": "(anonymous_6)", "decl": {"start": {"line": 19, "column": 44}, "end": {"line": 19, "column": 45}}, "loc": {"start": {"line": 19, "column": 51}, "end": {"line": 19, "column": 61}}, "line": 19}}, "branchMap": {"0": {"loc": {"start": {"line": 4, "column": 2}, "end": {"line": 14, "column": 3}}, "type": "if", "locations": [{"start": {"line": 4, "column": 2}, "end": {"line": 14, "column": 3}}, {"start": {"line": 4, "column": 2}, "end": {"line": 14, "column": 3}}], "line": 4}, "1": {"loc": {"start": {"line": 10, "column": 9}, "end": {"line": 14, "column": 3}}, "type": "if", "locations": [{"start": {"line": 10, "column": 9}, "end": {"line": 14, "column": 3}}, {"start": {"line": 10, "column": 9}, "end": {"line": 14, "column": 3}}], "line": 10}, "2": {"loc": {"start": {"line": 18, "column": 2}, "end": {"line": 22, "column": 3}}, "type": "if", "locations": [{"start": {"line": 18, "column": 2}, "end": {"line": 22, "column": 3}}, {"start": {"line": 18, "column": 2}, "end": {"line": 22, "column": 3}}], "line": 18}}, "s": {"0": 5, "1": 3, "2": 3, "3": 3, "4": 3, "5": 3, "6": 2, "7": 0, "8": 0, "9": 2, "10": 2, "11": 1, "12": 1, "13": 0, "14": 0}, "f": {"0": 5, "1": 3, "2": 3, "3": 0, "4": 2, "5": 1, "6": 0}, "b": {"0": [3, 2], "1": [0, 2], "2": [1, 0]}, "_coverageSchema": "332fd63041d2c1bcb487cc26dd0d5f7d97098a6c", "hash": "72c061d4a41c9dc7ea10b8a2d51eb77ba6f9e805"}}