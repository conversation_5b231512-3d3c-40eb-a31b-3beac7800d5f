{"version": 3, "file": "cross-fetch.js", "sources": ["../node_modules/whatwg-fetch/fetch.js"], "sourcesContent": ["/* eslint-disable no-prototype-builtins */\nvar g =\n  (typeof globalThis !== 'undefined' && globalThis) ||\n  (typeof self !== 'undefined' && self) ||\n  // eslint-disable-next-line no-undef\n  (typeof global !== 'undefined' && global) ||\n  {}\n\nvar support = {\n  searchParams: 'URLSearchParams' in g,\n  iterable: 'Symbol' in g && 'iterator' in Symbol,\n  blob:\n    'FileReader' in g &&\n    'Blob' in g &&\n    (function() {\n      try {\n        new Blob()\n        return true\n      } catch (e) {\n        return false\n      }\n    })(),\n  formData: 'FormData' in g,\n  arrayBuffer: 'ArrayBuffer' in g\n}\n\nfunction isDataView(obj) {\n  return obj && DataView.prototype.isPrototypeOf(obj)\n}\n\nif (support.arrayBuffer) {\n  var viewClasses = [\n    '[object Int8Array]',\n    '[object Uint8Array]',\n    '[object Uint8ClampedArray]',\n    '[object Int16Array]',\n    '[object Uint16Array]',\n    '[object Int32Array]',\n    '[object Uint32Array]',\n    '[object Float32Array]',\n    '[object Float64Array]'\n  ]\n\n  var isArrayBufferView =\n    ArrayBuffer.isView ||\n    function(obj) {\n      return obj && viewClasses.indexOf(Object.prototype.toString.call(obj)) > -1\n    }\n}\n\nfunction normalizeName(name) {\n  if (typeof name !== 'string') {\n    name = String(name)\n  }\n  if (/[^a-z0-9\\-#$%&'*+.^_`|~!]/i.test(name) || name === '') {\n    throw new TypeError('Invalid character in header field name: \"' + name + '\"')\n  }\n  return name.toLowerCase()\n}\n\nfunction normalizeValue(value) {\n  if (typeof value !== 'string') {\n    value = String(value)\n  }\n  return value\n}\n\n// Build a destructive iterator for the value list\nfunction iteratorFor(items) {\n  var iterator = {\n    next: function() {\n      var value = items.shift()\n      return {done: value === undefined, value: value}\n    }\n  }\n\n  if (support.iterable) {\n    iterator[Symbol.iterator] = function() {\n      return iterator\n    }\n  }\n\n  return iterator\n}\n\nexport function Headers(headers) {\n  this.map = {}\n\n  if (headers instanceof Headers) {\n    headers.forEach(function(value, name) {\n      this.append(name, value)\n    }, this)\n  } else if (Array.isArray(headers)) {\n    headers.forEach(function(header) {\n      if (header.length != 2) {\n        throw new TypeError('Headers constructor: expected name/value pair to be length 2, found' + header.length)\n      }\n      this.append(header[0], header[1])\n    }, this)\n  } else if (headers) {\n    Object.getOwnPropertyNames(headers).forEach(function(name) {\n      this.append(name, headers[name])\n    }, this)\n  }\n}\n\nHeaders.prototype.append = function(name, value) {\n  name = normalizeName(name)\n  value = normalizeValue(value)\n  var oldValue = this.map[name]\n  this.map[name] = oldValue ? oldValue + ', ' + value : value\n}\n\nHeaders.prototype['delete'] = function(name) {\n  delete this.map[normalizeName(name)]\n}\n\nHeaders.prototype.get = function(name) {\n  name = normalizeName(name)\n  return this.has(name) ? this.map[name] : null\n}\n\nHeaders.prototype.has = function(name) {\n  return this.map.hasOwnProperty(normalizeName(name))\n}\n\nHeaders.prototype.set = function(name, value) {\n  this.map[normalizeName(name)] = normalizeValue(value)\n}\n\nHeaders.prototype.forEach = function(callback, thisArg) {\n  for (var name in this.map) {\n    if (this.map.hasOwnProperty(name)) {\n      callback.call(thisArg, this.map[name], name, this)\n    }\n  }\n}\n\nHeaders.prototype.keys = function() {\n  var items = []\n  this.forEach(function(value, name) {\n    items.push(name)\n  })\n  return iteratorFor(items)\n}\n\nHeaders.prototype.values = function() {\n  var items = []\n  this.forEach(function(value) {\n    items.push(value)\n  })\n  return iteratorFor(items)\n}\n\nHeaders.prototype.entries = function() {\n  var items = []\n  this.forEach(function(value, name) {\n    items.push([name, value])\n  })\n  return iteratorFor(items)\n}\n\nif (support.iterable) {\n  Headers.prototype[Symbol.iterator] = Headers.prototype.entries\n}\n\nfunction consumed(body) {\n  if (body._noBody) return\n  if (body.bodyUsed) {\n    return Promise.reject(new TypeError('Already read'))\n  }\n  body.bodyUsed = true\n}\n\nfunction fileReaderReady(reader) {\n  return new Promise(function(resolve, reject) {\n    reader.onload = function() {\n      resolve(reader.result)\n    }\n    reader.onerror = function() {\n      reject(reader.error)\n    }\n  })\n}\n\nfunction readBlobAsArrayBuffer(blob) {\n  var reader = new FileReader()\n  var promise = fileReaderReady(reader)\n  reader.readAsArrayBuffer(blob)\n  return promise\n}\n\nfunction readBlobAsText(blob) {\n  var reader = new FileReader()\n  var promise = fileReaderReady(reader)\n  var match = /charset=([A-Za-z0-9_-]+)/.exec(blob.type)\n  var encoding = match ? match[1] : 'utf-8'\n  reader.readAsText(blob, encoding)\n  return promise\n}\n\nfunction readArrayBufferAsText(buf) {\n  var view = new Uint8Array(buf)\n  var chars = new Array(view.length)\n\n  for (var i = 0; i < view.length; i++) {\n    chars[i] = String.fromCharCode(view[i])\n  }\n  return chars.join('')\n}\n\nfunction bufferClone(buf) {\n  if (buf.slice) {\n    return buf.slice(0)\n  } else {\n    var view = new Uint8Array(buf.byteLength)\n    view.set(new Uint8Array(buf))\n    return view.buffer\n  }\n}\n\nfunction Body() {\n  this.bodyUsed = false\n\n  this._initBody = function(body) {\n    /*\n      fetch-mock wraps the Response object in an ES6 Proxy to\n      provide useful test harness features such as flush. However, on\n      ES5 browsers without fetch or Proxy support pollyfills must be used;\n      the proxy-pollyfill is unable to proxy an attribute unless it exists\n      on the object before the Proxy is created. This change ensures\n      Response.bodyUsed exists on the instance, while maintaining the\n      semantic of setting Request.bodyUsed in the constructor before\n      _initBody is called.\n    */\n    // eslint-disable-next-line no-self-assign\n    this.bodyUsed = this.bodyUsed\n    this._bodyInit = body\n    if (!body) {\n      this._noBody = true;\n      this._bodyText = ''\n    } else if (typeof body === 'string') {\n      this._bodyText = body\n    } else if (support.blob && Blob.prototype.isPrototypeOf(body)) {\n      this._bodyBlob = body\n    } else if (support.formData && FormData.prototype.isPrototypeOf(body)) {\n      this._bodyFormData = body\n    } else if (support.searchParams && URLSearchParams.prototype.isPrototypeOf(body)) {\n      this._bodyText = body.toString()\n    } else if (support.arrayBuffer && support.blob && isDataView(body)) {\n      this._bodyArrayBuffer = bufferClone(body.buffer)\n      // IE 10-11 can't handle a DataView body.\n      this._bodyInit = new Blob([this._bodyArrayBuffer])\n    } else if (support.arrayBuffer && (ArrayBuffer.prototype.isPrototypeOf(body) || isArrayBufferView(body))) {\n      this._bodyArrayBuffer = bufferClone(body)\n    } else {\n      this._bodyText = body = Object.prototype.toString.call(body)\n    }\n\n    if (!this.headers.get('content-type')) {\n      if (typeof body === 'string') {\n        this.headers.set('content-type', 'text/plain;charset=UTF-8')\n      } else if (this._bodyBlob && this._bodyBlob.type) {\n        this.headers.set('content-type', this._bodyBlob.type)\n      } else if (support.searchParams && URLSearchParams.prototype.isPrototypeOf(body)) {\n        this.headers.set('content-type', 'application/x-www-form-urlencoded;charset=UTF-8')\n      }\n    }\n  }\n\n  if (support.blob) {\n    this.blob = function() {\n      var rejected = consumed(this)\n      if (rejected) {\n        return rejected\n      }\n\n      if (this._bodyBlob) {\n        return Promise.resolve(this._bodyBlob)\n      } else if (this._bodyArrayBuffer) {\n        return Promise.resolve(new Blob([this._bodyArrayBuffer]))\n      } else if (this._bodyFormData) {\n        throw new Error('could not read FormData body as blob')\n      } else {\n        return Promise.resolve(new Blob([this._bodyText]))\n      }\n    }\n  }\n\n  this.arrayBuffer = function() {\n    if (this._bodyArrayBuffer) {\n      var isConsumed = consumed(this)\n      if (isConsumed) {\n        return isConsumed\n      } else if (ArrayBuffer.isView(this._bodyArrayBuffer)) {\n        return Promise.resolve(\n          this._bodyArrayBuffer.buffer.slice(\n            this._bodyArrayBuffer.byteOffset,\n            this._bodyArrayBuffer.byteOffset + this._bodyArrayBuffer.byteLength\n          )\n        )\n      } else {\n        return Promise.resolve(this._bodyArrayBuffer)\n      }\n    } else if (support.blob) {\n      return this.blob().then(readBlobAsArrayBuffer)\n    } else {\n      throw new Error('could not read as ArrayBuffer')\n    }\n  }\n\n  this.text = function() {\n    var rejected = consumed(this)\n    if (rejected) {\n      return rejected\n    }\n\n    if (this._bodyBlob) {\n      return readBlobAsText(this._bodyBlob)\n    } else if (this._bodyArrayBuffer) {\n      return Promise.resolve(readArrayBufferAsText(this._bodyArrayBuffer))\n    } else if (this._bodyFormData) {\n      throw new Error('could not read FormData body as text')\n    } else {\n      return Promise.resolve(this._bodyText)\n    }\n  }\n\n  if (support.formData) {\n    this.formData = function() {\n      return this.text().then(decode)\n    }\n  }\n\n  this.json = function() {\n    return this.text().then(JSON.parse)\n  }\n\n  return this\n}\n\n// HTTP methods whose capitalization should be normalized\nvar methods = ['CONNECT', 'DELETE', 'GET', 'HEAD', 'OPTIONS', 'PATCH', 'POST', 'PUT', 'TRACE']\n\nfunction normalizeMethod(method) {\n  var upcased = method.toUpperCase()\n  return methods.indexOf(upcased) > -1 ? upcased : method\n}\n\nexport function Request(input, options) {\n  if (!(this instanceof Request)) {\n    throw new TypeError('Please use the \"new\" operator, this DOM object constructor cannot be called as a function.')\n  }\n\n  options = options || {}\n  var body = options.body\n\n  if (input instanceof Request) {\n    if (input.bodyUsed) {\n      throw new TypeError('Already read')\n    }\n    this.url = input.url\n    this.credentials = input.credentials\n    if (!options.headers) {\n      this.headers = new Headers(input.headers)\n    }\n    this.method = input.method\n    this.mode = input.mode\n    this.signal = input.signal\n    if (!body && input._bodyInit != null) {\n      body = input._bodyInit\n      input.bodyUsed = true\n    }\n  } else {\n    this.url = String(input)\n  }\n\n  this.credentials = options.credentials || this.credentials || 'same-origin'\n  if (options.headers || !this.headers) {\n    this.headers = new Headers(options.headers)\n  }\n  this.method = normalizeMethod(options.method || this.method || 'GET')\n  this.mode = options.mode || this.mode || null\n  this.signal = options.signal || this.signal || (function () {\n    if ('AbortController' in g) {\n      var ctrl = new AbortController();\n      return ctrl.signal;\n    }\n  }());\n  this.referrer = null\n\n  if ((this.method === 'GET' || this.method === 'HEAD') && body) {\n    throw new TypeError('Body not allowed for GET or HEAD requests')\n  }\n  this._initBody(body)\n\n  if (this.method === 'GET' || this.method === 'HEAD') {\n    if (options.cache === 'no-store' || options.cache === 'no-cache') {\n      // Search for a '_' parameter in the query string\n      var reParamSearch = /([?&])_=[^&]*/\n      if (reParamSearch.test(this.url)) {\n        // If it already exists then set the value with the current time\n        this.url = this.url.replace(reParamSearch, '$1_=' + new Date().getTime())\n      } else {\n        // Otherwise add a new '_' parameter to the end with the current time\n        var reQueryString = /\\?/\n        this.url += (reQueryString.test(this.url) ? '&' : '?') + '_=' + new Date().getTime()\n      }\n    }\n  }\n}\n\nRequest.prototype.clone = function() {\n  return new Request(this, {body: this._bodyInit})\n}\n\nfunction decode(body) {\n  var form = new FormData()\n  body\n    .trim()\n    .split('&')\n    .forEach(function(bytes) {\n      if (bytes) {\n        var split = bytes.split('=')\n        var name = split.shift().replace(/\\+/g, ' ')\n        var value = split.join('=').replace(/\\+/g, ' ')\n        form.append(decodeURIComponent(name), decodeURIComponent(value))\n      }\n    })\n  return form\n}\n\nfunction parseHeaders(rawHeaders) {\n  var headers = new Headers()\n  // Replace instances of \\r\\n and \\n followed by at least one space or horizontal tab with a space\n  // https://tools.ietf.org/html/rfc7230#section-3.2\n  var preProcessedHeaders = rawHeaders.replace(/\\r?\\n[\\t ]+/g, ' ')\n  // Avoiding split via regex to work around a common IE11 bug with the core-js 3.6.0 regex polyfill\n  // https://github.com/github/fetch/issues/748\n  // https://github.com/zloirock/core-js/issues/751\n  preProcessedHeaders\n    .split('\\r')\n    .map(function(header) {\n      return header.indexOf('\\n') === 0 ? header.substr(1, header.length) : header\n    })\n    .forEach(function(line) {\n      var parts = line.split(':')\n      var key = parts.shift().trim()\n      if (key) {\n        var value = parts.join(':').trim()\n        try {\n          headers.append(key, value)\n        } catch (error) {\n          console.warn('Response ' + error.message)\n        }\n      }\n    })\n  return headers\n}\n\nBody.call(Request.prototype)\n\nexport function Response(bodyInit, options) {\n  if (!(this instanceof Response)) {\n    throw new TypeError('Please use the \"new\" operator, this DOM object constructor cannot be called as a function.')\n  }\n  if (!options) {\n    options = {}\n  }\n\n  this.type = 'default'\n  this.status = options.status === undefined ? 200 : options.status\n  if (this.status < 200 || this.status > 599) {\n    throw new RangeError(\"Failed to construct 'Response': The status provided (0) is outside the range [200, 599].\")\n  }\n  this.ok = this.status >= 200 && this.status < 300\n  this.statusText = options.statusText === undefined ? '' : '' + options.statusText\n  this.headers = new Headers(options.headers)\n  this.url = options.url || ''\n  this._initBody(bodyInit)\n}\n\nBody.call(Response.prototype)\n\nResponse.prototype.clone = function() {\n  return new Response(this._bodyInit, {\n    status: this.status,\n    statusText: this.statusText,\n    headers: new Headers(this.headers),\n    url: this.url\n  })\n}\n\nResponse.error = function() {\n  var response = new Response(null, {status: 200, statusText: ''})\n  response.ok = false\n  response.status = 0\n  response.type = 'error'\n  return response\n}\n\nvar redirectStatuses = [301, 302, 303, 307, 308]\n\nResponse.redirect = function(url, status) {\n  if (redirectStatuses.indexOf(status) === -1) {\n    throw new RangeError('Invalid status code')\n  }\n\n  return new Response(null, {status: status, headers: {location: url}})\n}\n\nexport var DOMException = g.DOMException\ntry {\n  new DOMException()\n} catch (err) {\n  DOMException = function(message, name) {\n    this.message = message\n    this.name = name\n    var error = Error(message)\n    this.stack = error.stack\n  }\n  DOMException.prototype = Object.create(Error.prototype)\n  DOMException.prototype.constructor = DOMException\n}\n\nexport function fetch(input, init) {\n  return new Promise(function(resolve, reject) {\n    var request = new Request(input, init)\n\n    if (request.signal && request.signal.aborted) {\n      return reject(new DOMException('Aborted', 'AbortError'))\n    }\n\n    var xhr = new XMLHttpRequest()\n\n    function abortXhr() {\n      xhr.abort()\n    }\n\n    xhr.onload = function() {\n      var options = {\n        statusText: xhr.statusText,\n        headers: parseHeaders(xhr.getAllResponseHeaders() || '')\n      }\n      // This check if specifically for when a user fetches a file locally from the file system\n      // Only if the status is out of a normal range\n      if (request.url.indexOf('file://') === 0 && (xhr.status < 200 || xhr.status > 599)) {\n        options.status = 200;\n      } else {\n        options.status = xhr.status;\n      }\n      options.url = 'responseURL' in xhr ? xhr.responseURL : options.headers.get('X-Request-URL')\n      var body = 'response' in xhr ? xhr.response : xhr.responseText\n      setTimeout(function() {\n        resolve(new Response(body, options))\n      }, 0)\n    }\n\n    xhr.onerror = function() {\n      setTimeout(function() {\n        reject(new TypeError('Network request failed'))\n      }, 0)\n    }\n\n    xhr.ontimeout = function() {\n      setTimeout(function() {\n        reject(new TypeError('Network request timed out'))\n      }, 0)\n    }\n\n    xhr.onabort = function() {\n      setTimeout(function() {\n        reject(new DOMException('Aborted', 'AbortError'))\n      }, 0)\n    }\n\n    function fixUrl(url) {\n      try {\n        return url === '' && g.location.href ? g.location.href : url\n      } catch (e) {\n        return url\n      }\n    }\n\n    xhr.open(request.method, fixUrl(request.url), true)\n\n    if (request.credentials === 'include') {\n      xhr.withCredentials = true\n    } else if (request.credentials === 'omit') {\n      xhr.withCredentials = false\n    }\n\n    if ('responseType' in xhr) {\n      if (support.blob) {\n        xhr.responseType = 'blob'\n      } else if (\n        support.arrayBuffer\n      ) {\n        xhr.responseType = 'arraybuffer'\n      }\n    }\n\n    if (init && typeof init.headers === 'object' && !(init.headers instanceof Headers || (g.Headers && init.headers instanceof g.Headers))) {\n      var names = [];\n      Object.getOwnPropertyNames(init.headers).forEach(function(name) {\n        names.push(normalizeName(name))\n        xhr.setRequestHeader(name, normalizeValue(init.headers[name]))\n      })\n      request.headers.forEach(function(value, name) {\n        if (names.indexOf(name) === -1) {\n          xhr.setRequestHeader(name, value)\n        }\n      })\n    } else {\n      request.headers.forEach(function(value, name) {\n        xhr.setRequestHeader(name, value)\n      })\n    }\n\n    if (request.signal) {\n      request.signal.addEventListener('abort', abortXhr)\n\n      xhr.onreadystatechange = function() {\n        // DONE (success or failure)\n        if (xhr.readyState === 4) {\n          request.signal.removeEventListener('abort', abortXhr)\n        }\n      }\n    }\n\n    xhr.send(typeof request._bodyInit === 'undefined' ? null : request._bodyInit)\n  })\n}\n\nfetch.polyfill = true\n\nif (!g.fetch) {\n  g.fetch = fetch\n  g.Headers = Headers\n  g.Request = Request\n  g.Response = Response\n}\n"], "names": ["g", "globalThis", "self", "global", "support", "Symbol", "Blob", "e", "viewClasses", "isArrayBuffer<PERSON>iew", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "obj", "indexOf", "Object", "prototype", "toString", "call", "normalizeName", "name", "String", "test", "TypeError", "toLowerCase", "normalizeValue", "value", "iteratorFor", "items", "iterator", "next", "shift", "done", "undefined", "Headers", "headers", "this", "map", "for<PERSON>ach", "append", "Array", "isArray", "header", "length", "getOwnPropertyNames", "consumed", "body", "_noBody", "bodyUsed", "Promise", "reject", "fileReaderReady", "reader", "resolve", "onload", "result", "onerror", "error", "readBlobAsArrayBuffer", "blob", "FileReader", "promise", "readAsA<PERSON>y<PERSON><PERSON>er", "bufferClone", "buf", "slice", "view", "Uint8Array", "byteLength", "set", "buffer", "Body", "_initBody", "_bodyInit", "_bodyText", "isPrototypeOf", "_bodyBlob", "FormData", "_bodyFormData", "URLSearchParams", "DataView", "_body<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "get", "type", "rejected", "Error", "arrayBuffer", "isConsumed", "byteOffset", "then", "text", "match", "encoding", "exec", "readAsText", "chars", "i", "fromCharCode", "join", "readArrayBufferAsText", "formData", "decode", "json", "JSON", "parse", "oldValue", "has", "hasOwnProperty", "callback", "thisArg", "keys", "push", "values", "entries", "methods", "Request", "input", "options", "method", "upcased", "url", "credentials", "mode", "signal", "toUpperCase", "AbortController", "referrer", "cache", "reParamSearch", "replace", "Date", "getTime", "form", "trim", "split", "bytes", "decodeURIComponent", "Response", "bodyInit", "status", "RangeError", "ok", "statusText", "clone", "response", "redirectStatuses", "redirect", "location", "DOMException", "err", "message", "stack", "create", "constructor", "fetch", "init", "request", "aborted", "xhr", "XMLHttpRequest", "abortXhr", "abort", "rawHeaders", "getAllResponseHeaders", "substr", "line", "parts", "key", "console", "warn", "responseURL", "responseText", "setTimeout", "ontimeout", "<PERSON>ab<PERSON>", "open", "href", "fixUrl", "withCredentials", "responseType", "names", "setRequestHeader", "addEventListener", "onreadystatechange", "readyState", "removeEventListener", "send", "polyfill"], "mappings": "0BACA,IAAIA,EACqB,oBAAfC,YAA8BA,iBACrB,IAATC,GAAwBA,GAEb,oBAAXC,QAA0BA,QAClC,GAEEC,EACY,oBAAqBJ,EADjCI,EAEQ,WAAYJ,GAAK,aAAcK,OAFvCD,EAIA,eAAgBJ,GAChB,SAAUA,GACV,WACE,IAEE,OADA,IAAIM,MACG,EACP,MAAOC,GACP,OAAO,EAEV,CAPD,GANAH,EAcQ,aAAcJ,EAdtBI,EAeW,gBAAiBJ,EAOhC,GAAII,EACF,IAAII,EAAc,CAChB,qBACA,sBACA,6BACA,sBACA,uBACA,sBACA,uBACA,wBACA,yBAGEC,EACFC,YAAYC,QACZ,SAASC,GACP,OAAOA,GAAOJ,EAAYK,QAAQC,OAAOC,UAAUC,SAASC,KAAKL,KAAS,GAIhF,SAASM,EAAcC,GAIrB,GAHoB,iBAATA,IACTA,EAAOC,OAAOD,IAEZ,6BAA6BE,KAAKF,IAAkB,KAATA,EAC7C,MAAM,IAAIG,UAAU,4CAA8CH,EAAO,KAE3E,OAAOA,EAAKI,aACd,CAEA,SAASC,EAAeC,GAItB,MAHqB,iBAAVA,IACTA,EAAQL,OAAOK,IAEVA,CACT,CAGA,SAASC,EAAYC,GACnB,IAAIC,EAAW,CACbC,KAAM,WACJ,IAAIJ,EAAQE,EAAMG,QAClB,MAAO,CAACC,UAAgBC,IAAVP,EAAqBA,MAAOA,KAU9C,OANIrB,IACFwB,EAASvB,OAAOuB,UAAY,WAC1B,OAAOA,IAIJA,CACT,CAEO,SAASK,EAAQC,GACtBC,KAAKC,IAAM,GAEPF,aAAmBD,EACrBC,EAAQG,SAAQ,SAASZ,EAAON,GAC9BgB,KAAKG,OAAOnB,EAAMM,KACjBU,MACMI,MAAMC,QAAQN,GACvBA,EAAQG,SAAQ,SAASI,GACvB,GAAqB,GAAjBA,EAAOC,OACT,MAAM,IAAIpB,UAAU,sEAAwEmB,EAAOC,QAErGP,KAAKG,OAAOG,EAAO,GAAIA,EAAO,MAC7BN,MACMD,GACTpB,OAAO6B,oBAAoBT,GAASG,SAAQ,SAASlB,GACnDgB,KAAKG,OAAOnB,EAAMe,EAAQf,MACzBgB,KAEP,CA8DA,SAASS,EAASC,GAChB,IAAIA,EAAKC,QACT,OAAID,EAAKE,SACAC,QAAQC,OAAO,IAAI3B,UAAU,sBAEtCuB,EAAKE,UAAW,EAClB,CAEA,SAASG,EAAgBC,GACvB,OAAO,IAAIH,SAAQ,SAASI,EAASH,GACnCE,EAAOE,OAAS,WACdD,EAAQD,EAAOG,SAEjBH,EAAOI,QAAU,WACfN,EAAOE,EAAOK,UAGpB,CAEA,SAASC,EAAsBC,GAC7B,IAAIP,EAAS,IAAIQ,WACbC,EAAUV,EAAgBC,GAE9B,OADAA,EAAOU,kBAAkBH,GAClBE,CACT,CAqBA,SAASE,EAAYC,GACnB,GAAIA,EAAIC,MACN,OAAOD,EAAIC,MAAM,GAEjB,IAAIC,EAAO,IAAIC,WAAWH,EAAII,YAE9B,OADAF,EAAKG,IAAI,IAAIF,WAAWH,IACjBE,EAAKI,MAEhB,CAEA,SAASC,IAqHP,OApHAnC,KAAKY,UAAW,EAEhBZ,KAAKoC,UAAY,SAAS1B,GAtM5B,IAAoBjC,EAkNhBuB,KAAKY,SAAWZ,KAAKY,SACrBZ,KAAKqC,UAAY3B,EACZA,EAGsB,iBAATA,EAChBV,KAAKsC,UAAY5B,EACRzC,GAAgBE,KAAKS,UAAU2D,cAAc7B,GACtDV,KAAKwC,UAAY9B,EACRzC,GAAoBwE,SAAS7D,UAAU2D,cAAc7B,GAC9DV,KAAK0C,cAAgBhC,EACZzC,GAAwB0E,gBAAgB/D,UAAU2D,cAAc7B,GACzEV,KAAKsC,UAAY5B,EAAK7B,WACbZ,GAAuBA,KA/NlBQ,EA+N6CiC,IA9NjDkC,SAAShE,UAAU2D,cAAc9D,KA+N3CuB,KAAK6C,iBAAmBlB,EAAYjB,EAAKwB,QAEzClC,KAAKqC,UAAY,IAAIlE,KAAK,CAAC6B,KAAK6C,oBACvB5E,IAAwBM,YAAYK,UAAU2D,cAAc7B,IAASpC,EAAkBoC,IAChGV,KAAK6C,iBAAmBlB,EAAYjB,GAEpCV,KAAKsC,UAAY5B,EAAO/B,OAAOC,UAAUC,SAASC,KAAK4B,IAjBvDV,KAAKW,SAAU,EACfX,KAAKsC,UAAY,IAmBdtC,KAAKD,QAAQ+C,IAAI,kBACA,iBAATpC,EACTV,KAAKD,QAAQkC,IAAI,eAAgB,4BACxBjC,KAAKwC,WAAaxC,KAAKwC,UAAUO,KAC1C/C,KAAKD,QAAQkC,IAAI,eAAgBjC,KAAKwC,UAAUO,MACvC9E,GAAwB0E,gBAAgB/D,UAAU2D,cAAc7B,IACzEV,KAAKD,QAAQkC,IAAI,eAAgB,qDAKnChE,IACF+B,KAAKuB,KAAO,WACV,IAAIyB,EAAWvC,EAAST,MACxB,GAAIgD,EACF,OAAOA,EAGT,GAAIhD,KAAKwC,UACP,OAAO3B,QAAQI,QAAQjB,KAAKwC,WACvB,GAAIxC,KAAK6C,iBACd,OAAOhC,QAAQI,QAAQ,IAAI9C,KAAK,CAAC6B,KAAK6C,oBACjC,GAAI7C,KAAK0C,cACd,MAAM,IAAIO,MAAM,wCAEhB,OAAOpC,QAAQI,QAAQ,IAAI9C,KAAK,CAAC6B,KAAKsC,eAK5CtC,KAAKkD,YAAc,WACjB,GAAIlD,KAAK6C,iBAAkB,CACzB,IAAIM,EAAa1C,EAAST,MAC1B,OAAImD,IAEO5E,YAAYC,OAAOwB,KAAK6C,kBAC1BhC,QAAQI,QACbjB,KAAK6C,iBAAiBX,OAAOL,MAC3B7B,KAAK6C,iBAAiBO,WACtBpD,KAAK6C,iBAAiBO,WAAapD,KAAK6C,iBAAiBb,aAItDnB,QAAQI,QAAQjB,KAAK6C,mBAEzB,GAAI5E,EACT,OAAO+B,KAAKuB,OAAO8B,KAAK/B,GAExB,MAAM,IAAI2B,MAAM,kCAIpBjD,KAAKsD,KAAO,WACV,IAxHoB/B,EAClBP,EACAS,EACA8B,EACAC,EAoHER,EAAWvC,EAAST,MACxB,GAAIgD,EACF,OAAOA,EAGT,GAAIhD,KAAKwC,UACP,OA9HkBjB,EA8HIvB,KAAKwC,UA7H3BxB,EAAS,IAAIQ,WACbC,EAAUV,EAAgBC,GAC1BuC,EAAQ,2BAA2BE,KAAKlC,EAAKwB,MAC7CS,EAAWD,EAAQA,EAAM,GAAK,QAClCvC,EAAO0C,WAAWnC,EAAMiC,GACjB/B,EAyHE,GAAIzB,KAAK6C,iBACd,OAAOhC,QAAQI,QAvHrB,SAA+BW,GAI7B,IAHA,IAAIE,EAAO,IAAIC,WAAWH,GACtB+B,EAAQ,IAAIvD,MAAM0B,EAAKvB,QAElBqD,EAAI,EAAGA,EAAI9B,EAAKvB,OAAQqD,IAC/BD,EAAMC,GAAK3E,OAAO4E,aAAa/B,EAAK8B,IAEtC,OAAOD,EAAMG,KAAK,GACpB,CA+G6BC,CAAsB/D,KAAK6C,mBAC7C,GAAI7C,KAAK0C,cACd,MAAM,IAAIO,MAAM,wCAEhB,OAAOpC,QAAQI,QAAQjB,KAAKsC,YAI5BrE,IACF+B,KAAKgE,SAAW,WACd,OAAOhE,KAAKsD,OAAOD,KAAKY,KAI5BjE,KAAKkE,KAAO,WACV,OAAOlE,KAAKsD,OAAOD,KAAKc,KAAKC,QAGxBpE,IACT,CAzOAF,EAAQlB,UAAUuB,OAAS,SAASnB,EAAMM,GACxCN,EAAOD,EAAcC,GACrBM,EAAQD,EAAeC,GACvB,IAAI+E,EAAWrE,KAAKC,IAAIjB,GACxBgB,KAAKC,IAAIjB,GAAQqF,EAAWA,EAAW,KAAO/E,EAAQA,CACxD,EAEAQ,EAAQlB,UAAkB,OAAI,SAASI,UAC9BgB,KAAKC,IAAIlB,EAAcC,GAChC,EAEAc,EAAQlB,UAAUkE,IAAM,SAAS9D,GAE/B,OADAA,EAAOD,EAAcC,GACdgB,KAAKsE,IAAItF,GAAQgB,KAAKC,IAAIjB,GAAQ,IAC3C,EAEAc,EAAQlB,UAAU0F,IAAM,SAAStF,GAC/B,OAAOgB,KAAKC,IAAIsE,eAAexF,EAAcC,GAC/C,EAEAc,EAAQlB,UAAUqD,IAAM,SAASjD,EAAMM,GACrCU,KAAKC,IAAIlB,EAAcC,IAASK,EAAeC,EACjD,EAEAQ,EAAQlB,UAAUsB,QAAU,SAASsE,EAAUC,GAC7C,IAAK,IAAIzF,KAAQgB,KAAKC,IAChBD,KAAKC,IAAIsE,eAAevF,IAC1BwF,EAAS1F,KAAK2F,EAASzE,KAAKC,IAAIjB,GAAOA,EAAMgB,KAGnD,EAEAF,EAAQlB,UAAU8F,KAAO,WACvB,IAAIlF,EAAQ,GAIZ,OAHAQ,KAAKE,SAAQ,SAASZ,EAAON,GAC3BQ,EAAMmF,KAAK3F,MAENO,EAAYC,EACrB,EAEAM,EAAQlB,UAAUgG,OAAS,WACzB,IAAIpF,EAAQ,GAIZ,OAHAQ,KAAKE,SAAQ,SAASZ,GACpBE,EAAMmF,KAAKrF,MAENC,EAAYC,EACrB,EAEAM,EAAQlB,UAAUiG,QAAU,WAC1B,IAAIrF,EAAQ,GAIZ,OAHAQ,KAAKE,SAAQ,SAASZ,EAAON,GAC3BQ,EAAMmF,KAAK,CAAC3F,EAAMM,OAEbC,EAAYC,EACrB,EAEIvB,IACF6B,EAAQlB,UAAUV,OAAOuB,UAAYK,EAAQlB,UAAUiG,SAmLzD,IAAIC,EAAU,CAAC,UAAW,SAAU,MAAO,OAAQ,UAAW,QAAS,OAAQ,MAAO,SAO/E,SAASC,EAAQC,EAAOC,GAC7B,KAAMjF,gBAAgB+E,GACpB,MAAM,IAAI5F,UAAU,8FAItB,IAXuB+F,EACnBC,EAUAzE,GADJuE,EAAUA,GAAW,IACFvE,KAEnB,GAAIsE,aAAiBD,EAAS,CAC5B,GAAIC,EAAMpE,SACR,MAAM,IAAIzB,UAAU,gBAEtBa,KAAKoF,IAAMJ,EAAMI,IACjBpF,KAAKqF,YAAcL,EAAMK,YACpBJ,EAAQlF,UACXC,KAAKD,QAAU,IAAID,EAAQkF,EAAMjF,UAEnCC,KAAKkF,OAASF,EAAME,OACpBlF,KAAKsF,KAAON,EAAMM,KAClBtF,KAAKuF,OAASP,EAAMO,OACf7E,GAA2B,MAAnBsE,EAAM3C,YACjB3B,EAAOsE,EAAM3C,UACb2C,EAAMpE,UAAW,QAGnBZ,KAAKoF,IAAMnG,OAAO+F,GAiBpB,GAdAhF,KAAKqF,YAAcJ,EAAQI,aAAerF,KAAKqF,aAAe,eAC1DJ,EAAQlF,SAAYC,KAAKD,UAC3BC,KAAKD,QAAU,IAAID,EAAQmF,EAAQlF,UAErCC,KAAKkF,QArCkBA,EAqCOD,EAAQC,QAAUlF,KAAKkF,QAAU,MApC3DC,EAAUD,EAAOM,cACdV,EAAQpG,QAAQyG,IAAY,EAAIA,EAAUD,GAoCjDlF,KAAKsF,KAAOL,EAAQK,MAAQtF,KAAKsF,MAAQ,KACzCtF,KAAKuF,OAASN,EAAQM,QAAUvF,KAAKuF,QAAW,WAC9C,GAAI,oBAAqB1H,EAEvB,OADW,IAAI4H,iBACHF,UAGhBvF,KAAK0F,SAAW,MAEK,QAAhB1F,KAAKkF,QAAoC,SAAhBlF,KAAKkF,SAAsBxE,EACvD,MAAM,IAAIvB,UAAU,6CAItB,GAFAa,KAAKoC,UAAU1B,KAEK,QAAhBV,KAAKkF,QAAoC,SAAhBlF,KAAKkF,QACV,aAAlBD,EAAQU,OAA0C,aAAlBV,EAAQU,OAAsB,CAEhE,IAAIC,EAAgB,gBACpB,GAAIA,EAAc1G,KAAKc,KAAKoF,KAE1BpF,KAAKoF,IAAMpF,KAAKoF,IAAIS,QAAQD,EAAe,QAAS,IAAIE,MAAOC,eAC1D,CAGL/F,KAAKoF,MADe,KACOlG,KAAKc,KAAKoF,KAAO,IAAM,KAAO,MAAO,IAAIU,MAAOC,WAInF,CAMA,SAAS9B,EAAOvD,GACd,IAAIsF,EAAO,IAAIvD,SAYf,OAXA/B,EACGuF,OACAC,MAAM,KACNhG,SAAQ,SAASiG,GAChB,GAAIA,EAAO,CACT,IAAID,EAAQC,EAAMD,MAAM,KACpBlH,EAAOkH,EAAMvG,QAAQkG,QAAQ,MAAO,KACpCvG,EAAQ4G,EAAMpC,KAAK,KAAK+B,QAAQ,MAAO,KAC3CG,EAAK7F,OAAOiG,mBAAmBpH,GAAOoH,mBAAmB9G,QAGxD0G,CACT,CAgCO,SAASK,EAASC,EAAUrB,GACjC,KAAMjF,gBAAgBqG,GACpB,MAAM,IAAIlH,UAAU,8FAQtB,GANK8F,IACHA,EAAU,IAGZjF,KAAK+C,KAAO,UACZ/C,KAAKuG,YAA4B1G,IAAnBoF,EAAQsB,OAAuB,IAAMtB,EAAQsB,OACvDvG,KAAKuG,OAAS,KAAOvG,KAAKuG,OAAS,IACrC,MAAM,IAAIC,WAAW,4FAEvBxG,KAAKyG,GAAKzG,KAAKuG,QAAU,KAAOvG,KAAKuG,OAAS,IAC9CvG,KAAK0G,gBAAoC7G,IAAvBoF,EAAQyB,WAA2B,GAAK,GAAKzB,EAAQyB,WACvE1G,KAAKD,QAAU,IAAID,EAAQmF,EAAQlF,SACnCC,KAAKoF,IAAMH,EAAQG,KAAO,GAC1BpF,KAAKoC,UAAUkE,EACjB,CApEAvB,EAAQnG,UAAU+H,MAAQ,WACxB,OAAO,IAAI5B,EAAQ/E,KAAM,CAACU,KAAMV,KAAKqC,WACvC,EA8CAF,EAAKrD,KAAKiG,EAAQnG,WAsBlBuD,EAAKrD,KAAKuH,EAASzH,WAEnByH,EAASzH,UAAU+H,MAAQ,WACzB,OAAO,IAAIN,EAASrG,KAAKqC,UAAW,CAClCkE,OAAQvG,KAAKuG,OACbG,WAAY1G,KAAK0G,WACjB3G,QAAS,IAAID,EAAQE,KAAKD,SAC1BqF,IAAKpF,KAAKoF,KAEd,EAEAiB,EAAShF,MAAQ,WACf,IAAIuF,EAAW,IAAIP,EAAS,KAAM,CAACE,OAAQ,IAAKG,WAAY,KAI5D,OAHAE,EAASH,IAAK,EACdG,EAASL,OAAS,EAClBK,EAAS7D,KAAO,QACT6D,CACT,EAEA,IAAIC,EAAmB,CAAC,IAAK,IAAK,IAAK,IAAK,KAE5CR,EAASS,SAAW,SAAS1B,EAAKmB,GAChC,IAA0C,IAAtCM,EAAiBnI,QAAQ6H,GAC3B,MAAM,IAAIC,WAAW,uBAGvB,OAAO,IAAIH,EAAS,KAAM,CAACE,OAAQA,EAAQxG,QAAS,CAACgH,SAAU3B,IACjE,iBAE0BvH,EAAEmJ,aAC5B,IACE,IAAIA,cACN,CAAE,MAAOC,GACPD,eAAe,SAASE,EAASlI,GAC/BgB,KAAKkH,QAAUA,EACflH,KAAKhB,KAAOA,EACZ,IAAIqC,EAAQ4B,MAAMiE,GAClBlH,KAAKmH,MAAQ9F,EAAM8F,OAErBH,eAAapI,UAAYD,OAAOyI,OAAOnE,MAAMrE,WAC7CoI,eAAapI,UAAUyI,YAAcL,cACvC,CAEO,SAASM,EAAMtC,EAAOuC,GAC3B,OAAO,IAAI1G,SAAQ,SAASI,EAASH,GACnC,IAAI0G,EAAU,IAAIzC,EAAQC,EAAOuC,GAEjC,GAAIC,EAAQjC,QAAUiC,EAAQjC,OAAOkC,QACnC,OAAO3G,EAAO,IAAIkG,eAAa,UAAW,eAG5C,IAAIU,EAAM,IAAIC,eAEd,SAASC,IACPF,EAAIG,QAkEN,GA/DAH,EAAIxG,OAAS,WACX,IA5GgB4G,EAChB/H,EA2GIkF,EAAU,CACZyB,WAAYgB,EAAIhB,WAChB3G,SA9Gc+H,EA8GQJ,EAAIK,yBAA2B,GA7GvDhI,EAAU,IAAID,EAGQgI,EAAWjC,QAAQ,eAAgB,KAK1DK,MAAM,MACNjG,KAAI,SAASK,GACZ,OAAgC,IAAzBA,EAAO5B,QAAQ,MAAc4B,EAAO0H,OAAO,EAAG1H,EAAOC,QAAUD,KAEvEJ,SAAQ,SAAS+H,GAChB,IAAIC,EAAQD,EAAK/B,MAAM,KACnBiC,EAAMD,EAAMvI,QAAQsG,OACxB,GAAIkC,EAAK,CACP,IAAI7I,EAAQ4I,EAAMpE,KAAK,KAAKmC,OAC5B,IACElG,EAAQI,OAAOgI,EAAK7I,GACpB,MAAO+B,GACP+G,QAAQC,KAAK,YAAchH,EAAM6F,cAIlCnH,IAyFoC,IAAnCyH,EAAQpC,IAAI1G,QAAQ,aAAqBgJ,EAAInB,OAAS,KAAOmB,EAAInB,OAAS,KAC5EtB,EAAQsB,OAAS,IAEjBtB,EAAQsB,OAASmB,EAAInB,OAEvBtB,EAAQG,IAAM,gBAAiBsC,EAAMA,EAAIY,YAAcrD,EAAQlF,QAAQ+C,IAAI,iBAC3E,IAAIpC,EAAO,aAAcgH,EAAMA,EAAId,SAAWc,EAAIa,aAClDC,YAAW,WACTvH,EAAQ,IAAIoF,EAAS3F,EAAMuE,MAC1B,IAGLyC,EAAItG,QAAU,WACZoH,YAAW,WACT1H,EAAO,IAAI3B,UAAU,6BACpB,IAGLuI,EAAIe,UAAY,WACdD,YAAW,WACT1H,EAAO,IAAI3B,UAAU,gCACpB,IAGLuI,EAAIgB,QAAU,WACZF,YAAW,WACT1H,EAAO,IAAIkG,eAAa,UAAW,iBAClC,IAWLU,EAAIiB,KAAKnB,EAAQtC,OARjB,SAAgBE,GACd,IACE,MAAe,KAARA,GAAcvH,EAAEkJ,SAAS6B,KAAO/K,EAAEkJ,SAAS6B,KAAOxD,EACzD,MAAOhH,GACP,OAAOgH,GAIcyD,CAAOrB,EAAQpC,MAAM,GAElB,YAAxBoC,EAAQnC,YACVqC,EAAIoB,iBAAkB,EACW,SAAxBtB,EAAQnC,cACjBqC,EAAIoB,iBAAkB,GAGpB,iBAAkBpB,IAChBzJ,EACFyJ,EAAIqB,aAAe,OAEnB9K,IAEAyJ,EAAIqB,aAAe,gBAInBxB,GAAgC,iBAAjBA,EAAKxH,WAA0BwH,EAAKxH,mBAAmBD,GAAYjC,EAAEiC,SAAWyH,EAAKxH,mBAAmBlC,EAAEiC,SAAW,CACtI,IAAIkJ,EAAQ,GACZrK,OAAO6B,oBAAoB+G,EAAKxH,SAASG,SAAQ,SAASlB,GACxDgK,EAAMrE,KAAK5F,EAAcC,IACzB0I,EAAIuB,iBAAiBjK,EAAMK,EAAekI,EAAKxH,QAAQf,QAEzDwI,EAAQzH,QAAQG,SAAQ,SAASZ,EAAON,IACT,IAAzBgK,EAAMtK,QAAQM,IAChB0I,EAAIuB,iBAAiBjK,EAAMM,WAI/BkI,EAAQzH,QAAQG,SAAQ,SAASZ,EAAON,GACtC0I,EAAIuB,iBAAiBjK,EAAMM,MAI3BkI,EAAQjC,SACViC,EAAQjC,OAAO2D,iBAAiB,QAAStB,GAEzCF,EAAIyB,mBAAqB,WAEA,IAAnBzB,EAAI0B,YACN5B,EAAQjC,OAAO8D,oBAAoB,QAASzB,KAKlDF,EAAI4B,UAAkC,IAAtB9B,EAAQnF,UAA4B,KAAOmF,EAAQnF,aAEvE,CAEAiF,EAAMiC,UAAW,EAEZ1L,EAAEyJ,QACLzJ,EAAEyJ,MAAQA,EACVzJ,EAAEiC,QAAUA,EACZjC,EAAEkH,QAAUA,EACZlH,EAAEwI,SAAWA"}