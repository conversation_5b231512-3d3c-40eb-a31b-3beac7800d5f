/**
 * MCP Rules Engine Client
 *
 * TypeScript wrapper for the MCP Rules Engine API that provides
 * deadline calculation and health check functionality.
 */
import { DeadlineResponse, HealthCheckResponse, McpClientConfig } from './types';
export * from './types';
export declare class McpClient {
    private readonly baseUrl;
    private readonly apiKey;
    private readonly timeout;
    private readonly maxRetries;
    private readonly retryDelay;
    private circuitBreakerState;
    private consecutiveFailures;
    private lastFailureTime;
    private readonly failureThreshold;
    private readonly recoveryTimeout;
    constructor(config: McpClientConfig);
    /**
     * Calculate deadlines for a given jurisdiction and trigger code
     */
    calculateDeadlines(jurisdiction: string, triggerCode: string, startDate: string, practiceArea?: string): Promise<DeadlineResponse>;
    /**
     * Perform health check on the MCP Rules Engine
     */
    healthCheck(): Promise<HealthCheckResponse>;
    /**
     * Make an HTTP request with retry logic, error handling, and circuit breaker
     */
    private makeRequest;
    /**
     * Safely parse JSON response, returning null if parsing fails
     */
    private safeParseJson;
    /**
     * Sleep for the specified number of milliseconds
     */
    private sleep;
    /**
     * Handle successful request - reset circuit breaker
     */
    private onSuccess;
    /**
     * Handle failed request - update circuit breaker state
     */
    private onFailure;
    /**
     * Get current circuit breaker state for monitoring
     */
    getCircuitBreakerState(): {
        state: 'closed' | 'open' | 'half-open';
        consecutiveFailures: number;
        lastFailureTime: number;
    };
}
//# sourceMappingURL=index.d.ts.map