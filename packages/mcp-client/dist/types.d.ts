/**
 * MCP Rules Engine API Types
 */
export interface DeadlineRequest {
    jurisdiction: string;
    triggerCode: string;
    startDate: string;
    practiceArea?: string;
}
export interface DeadlineResponse {
    deadlines: Deadline[];
    jurisdiction: string;
    triggerCode: string;
    startDate: string;
    practiceArea?: string;
}
export interface Deadline {
    id: string;
    name: string;
    description: string;
    dueDate: string;
    priority: 'high' | 'medium' | 'low';
    category: string;
    isStatutory: boolean;
    source?: string;
    notes?: string;
}
export interface HealthCheckResponse {
    status: 'healthy' | 'unhealthy';
    timestamp: string;
    version?: string;
    uptime?: number;
}
export interface McpClientConfig {
    baseUrl: string;
    apiKey: string;
    timeout?: number;
    maxRetries?: number;
    retryDelay?: number;
}
export interface ApiError {
    message: string;
    status: number;
    code?: string;
    details?: any;
}
export declare class McpApiError extends Error {
    readonly status: number;
    readonly code?: string;
    readonly details?: any;
    constructor(message: string, status: number, code?: string, details?: any);
}
//# sourceMappingURL=types.d.ts.map