/**
 * Jest configuration for MCP Rules Engine tests
 */

module.exports = {
  preset: 'ts-jest',
  testEnvironment: 'node',
  testMatch: ['**/tests/mcp.e2e.spec.ts'],
  collectCoverageFrom: [
    'packages/mcp-client/src/**/*.ts',
    'apps/backend/src/middleware/mcpMetrics.ts',
  ],
  coverageDirectory: 'coverage/mcp',
  coverageReporters: ['text', 'lcov', 'html'],
  coverageThreshold: {
    global: {
      branches: 80,
      functions: 80,
      lines: 80,
      statements: 80,
    },
  },
  moduleNameMapping: {
    '^(\\.{1,2}/.*)\\.js$': '$1',
  },
  transform: {
    '^.+\\.ts$': 'ts-jest',
  },
  setupFilesAfterEnv: ['<rootDir>/jest.setup.mcp.js'],
  moduleFileExtensions: ['ts', 'tsx', 'js', 'jsx', 'json', 'node'],
};
