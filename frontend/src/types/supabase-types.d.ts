// Type definitions for Supabase client
// This file provides type definitions for Supabase client to fix type errors

import { SupabaseClient, PostgrestError, SupabaseClientOptions } from '@supabase/supabase-js';

// Extend the createClientComponentClient function
declare module '@supabase/supabase-js' {
  export function createClientComponentClient<
    Database = unknown,
    SchemaName extends string & keyof Database = 'public' extends keyof Database
      ? 'public'
      : string & keyof Database,
  >(
    supabaseUrl?: string,
    supabaseKey?: string,
    options?: SupabaseClientOptions<SchemaName>,
  ): SupabaseClient<Database, SchemaName>;
}

// Add type guards for Supabase responses
export function isDataResponse<T>(
  response: { data: T; error: null } | { data: null; error: PostgrestError }
): response is { data: T; error: null } {
  return response.data !== null && response.error === null;
}

export function isErrorResponse<T>(
  response: { data: unknown; error: null } | { data: T | null; error: PostgrestError }
): response is { data: T | null; error: PostgrestError } {
  return response.error !== null;
}

// Type guard for checking if a value is a valid date string or number
export function isValidDateInput(value: unknown): value is string | number | Date {
  if (value instanceof Date) return true;
  if (typeof value === 'string' || typeof value === 'number') return true;
  return false;
}

// Safe date conversion helper
export function safeToDate(value: unknown): Date | null {
  if (value === null || value === undefined) return null;
  if (value instanceof Date) return value;

  if (typeof value === 'string' || typeof value === 'number') {
    try {
      const date = new Date(value);
      // Check if the date is valid
      if (!isNaN(date.getTime())) {
        return date;
      }
    } catch (_e) {
      console.error('Invalid date conversion:', e);
    }
  }

  return null;
}

// Safe property access helper
export function safeGet<T, K extends keyof T>(obj: T | null | undefined, key: K): T[K] | undefined {
  if (obj == null) return undefined;
  return obj[key];
}

// This empty export is necessary to make this a module
export {};
