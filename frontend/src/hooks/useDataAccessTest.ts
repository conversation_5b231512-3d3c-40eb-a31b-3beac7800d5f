'use client';

import { useState, useCallback } from 'react';

/**
 * Interface for test results from the data-access-test API
 */
export interface TestResult {
  success: boolean;
  count: number;
  data?: unknown[];
  error: string | null;
  timing?: number;
}

/**
 * Interface for the API response
 */
export interface TestApiResponse {
  success: boolean;
  message: string;
  testType: string;
  timestamp: string;
  user: {
    id: string;
    tenantId: string;
    role: string;
  };
  results: Record<string, TestResult>;
}

/**
 * Hook for testing the data access layer via the API
 */
export function useDataAccessTest() {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [results, setResults] = useState<TestApiResponse | null>(null);

  /**
   * Run the data access test
   *
   * @param options Test options
   * @returns The test results
   */
  const runTest = useCallback(async (options: {
    testType?: 'all' | 'clients' | 'cases' | 'tasks' | 'security' | 'audit' | 'schema';
    limit?: number;
  } = {}) => {
    const { testType = 'all', limit = 5 } = options;

    setIsLoading(true);
    setError(null);

    try {
      // Build the URL with query parameters
      const url = `/api/data-access-test?test=${testType}&limit=${limit}`;

      // Make the request
      const response = await fetch(url);

      if (!response.ok) {
        throw new Error(`API error: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      setResults(data);
      return data;
    } catch (_err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error';
      setError(errorMessage);
      console.error('Error running data access test:', err);
      return null;
    } finally {
      setIsLoading(false);
    }
  }, []);

  return {
    isLoading,
    error,
    results,
    runTest
  };
}
