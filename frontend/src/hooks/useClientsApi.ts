import { useState, useCallback } from 'react';
import { useAuthenticatedFetch } from './useAuthenticatedFetch';

export interface Client {
  id: string;
  name: string;
  email?: string;
  phone?: string;
  address?: string;
  created_at?: string;
  updated_at?: string;
  tenant_id: string;
}

// Define expected API response structure for /api/clients-test
interface ClientsTestApiResponse {
  success: boolean;
  data: {
    schemaClient?: Client[];
    schemaMethod?: Client[];
  };
}

// Define expected API response structure for /api/clients
interface ClientsApiResponse {
  clients?: Client[];
}

/**
 * Custom hook for working with clients with proper schema handling
 */
export function useClientsApi() {
  const { authedFetch } = useAuthenticatedFetch();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  /**
   * Get all clients with optional filtering
   */
  const getAllClients = useCallback(async (filters?: {
    search?: string,
    page?: number,
    limit?: number
  }) => {
    setIsLoading(true);
    setError(null);
    try {
      // First try the test endpoint which is more resilient
      try {
        console.log('Attempting to fetch clients from test endpoint first');
        const testResponse = await authedFetch<ClientsTestApiResponse>('/api/clients-test');

        if (testResponse?.success) {
          console.log('Clients test endpoint successful');

          // Use the data from the most successful method
          let clientsData: Client[] = [];
          // Explicitly check if schemaClient exists AND has items
          if (testResponse.data.schemaClient && testResponse.data.schemaClient.length > 0) {
            console.log('Using schema client data');
            clientsData = testResponse.data.schemaClient; // Assign directly, existence checked
          // Explicitly check if schemaMethod exists AND has items
          } else if (testResponse.data.schemaMethod && testResponse.data.schemaMethod.length > 0) {
            console.log('Using schema method data');
            clientsData = testResponse.data.schemaMethod; // Assign directly, existence checked
          }

          if (clientsData.length > 0) {
            return clientsData;
          }
        }
      } catch (testErr) {
        console.warn('Clients test endpoint failed:', testErr);
        // Continue to try the main endpoint
      }

      // Build query parameters for the main endpoint
      const params = new URLSearchParams();

      if (filters?.search) params.append('search', filters.search);
      if (filters?.page) params.append('page', filters.page.toString());
      if (filters?.limit) params.append('limit', filters.limit.toString());

      const queryString = params.toString() ? `?${params.toString()}` : '';

      // Use authenticated fetch with the API route
      console.log('Falling back to regular clients endpoint');
      const response = await authedFetch<ClientsApiResponse>(`/api/clients${queryString}`);
      return response?.clients || [];
    } catch (_err) {
      console.error('Error fetching clients:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch clients');

      // Return an empty array as fallback in case of error
      return [];
    } finally {
      setIsLoading(false);
    }
  }, [authedFetch]);

  /**
   * Get a single client by ID
   */
  const getClientById = useCallback(async (clientId: string) => {
    setIsLoading(true);
    setError(null);
    try {
      const response = await authedFetch(`/api/clients/${clientId}`);
      return response;
    } catch (_err) {
      console.error(`Error fetching client ${clientId}:`, err);
      setError(err instanceof Error ? err.message : `Failed to fetch client ${clientId}`);
      return null;
    } finally {
      setIsLoading(false);
    }
  }, [authedFetch]);

  /**
   * Create a new client
   */
  const createClient = useCallback(async (clientData: Partial<Client>) => {
    setIsLoading(true);
    setError(null);
    try {
      const response = await authedFetch('/api/clients', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(clientData),
      });

      return response;
    } catch (_err) {
      console.error('Error creating client:', err);
      setError(err instanceof Error ? err.message : 'Failed to create client');
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, [authedFetch]);

  /**
   * Update an existing client
   */
  const updateClient = useCallback(async (clientId: string, clientData: Partial<Client>) => {
    setIsLoading(true);
    setError(null);
    try {
      const response = await authedFetch(`/api/clients/${clientId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(clientData),
      });

      return response;
    } catch (_err) {
      console.error(`Error updating client ${clientId}:`, err);
      setError(err instanceof Error ? err.message : `Failed to update client ${clientId}`);
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, [authedFetch]);

  /**
   * Delete a client
   */
  const deleteClient = useCallback(async (clientId: string) => {
    setIsLoading(true);
    setError(null);
    try {
      const response = await authedFetch(`/api/clients/${clientId}`, {
        method: 'DELETE',
      });

      return response;
    } catch (_err) {
      console.error(`Error deleting client ${clientId}:`, err);
      setError(err instanceof Error ? err.message : `Failed to delete client ${clientId}`);
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, [authedFetch]);

  return {
    // Data and state
    isLoading,
    error,

    // CRUD operations
    getAllClients,
    getClientById,
    createClient,
    updateClient,
    deleteClient,
  };
}
