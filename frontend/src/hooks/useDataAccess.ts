'use client';

import { useState, useCallback } from 'react';
import { useSupabase } from '@/lib/supabase/provider';
import { useUser } from '@/contexts/UserContext';
import { createDataAccess, DataAccess } from '@/lib/data-access';

/**
 * Hook for using the data access layer in React components
 *
 * @returns The data access layer and loading/error state
 */
export function useDataAccess() {
  const { supabase } = useSupabase();
  const { tenantId } = useUser();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Create the data access layer
  const dataAccess = createDataAccess(supabase, tenantId || undefined);

  /**
   * Wrapper function for data access methods that handles loading and error state
   *
   * @param fn The function to wrap
   * @returns A wrapped function that handles loading and error state
   */
  const withLoadingAndError = useCallback(<T extends unknown[], R>(
    fn: (...args: T) => Promise<R>
  ) => {
    return async (...args: T): Promise<R | null> => {
      setIsLoading(true);
      setError(null);
      try {
        const result = await fn(...args);
        return result;
      } catch (_err) {
        console.error('Error in data access:', err);
        setError(err instanceof Error ? err.message : 'An error occurred');
        return null;
      } finally {
        setIsLoading(false);
      }
    };
  }, []);

  /**
   * Get clients with loading and error handling
   */
  const getClients = useCallback((options?: Parameters<DataAccess['clients']['getClients']>[0]) => {
    return withLoadingAndError(dataAccess.clients.getClients.bind(dataAccess.clients))(options || {});
  }, [dataAccess.clients, withLoadingAndError]);

  /**
   * Get a client by ID with loading and error handling
   */
  const getClientById = useCallback((id: string) => {
    return withLoadingAndError(dataAccess.clients.getClientWithRelations.bind(dataAccess.clients))(id);
  }, [dataAccess.clients, withLoadingAndError]);

  /**
   * Get cases with loading and error handling
   */
  const getCases = useCallback((options?: Parameters<DataAccess['cases']['getCases']>[0]) => {
    return withLoadingAndError(dataAccess.cases.getCases.bind(dataAccess.cases))(options || {});
  }, [dataAccess.cases, withLoadingAndError]);

  /**
   * Get a case by ID with loading and error handling
   */
  const getCaseById = useCallback((id: string) => {
    return withLoadingAndError(dataAccess.cases.getCaseWithRelations.bind(dataAccess.cases))(id);
  }, [dataAccess.cases, withLoadingAndError]);

  /**
   * Get tasks with loading and error handling
   */
  const getTasks = useCallback((options?: Parameters<DataAccess['tasks']['getTasks']>[0]) => {
    return withLoadingAndError(dataAccess.tasks.getTasks.bind(dataAccess.tasks))(options || {});
  }, [dataAccess.tasks, withLoadingAndError]);

  /**
   * Get a task by ID with loading and error handling
   */
  const getTaskById = useCallback((id: string) => {
    return withLoadingAndError(dataAccess.tasks.getTaskWithRelations.bind(dataAccess.tasks))(id);
  }, [dataAccess.tasks, withLoadingAndError]);

  /**
   * Get security events with loading and error handling
   */
  const getSecurityEvents = useCallback((options?: Parameters<DataAccess['securityEvents']['getEvents']>[0]) => {
    return withLoadingAndError(dataAccess.securityEvents.getEvents.bind(dataAccess.securityEvents))(options || {});
  }, [dataAccess.securityEvents, withLoadingAndError]);

  /**
   * Get auth audit logs with loading and error handling
   */
  const getAuthAuditLogs = useCallback((options?: Parameters<DataAccess['authAudit']['getAuditLogs']>[0]) => {
    return withLoadingAndError(dataAccess.authAudit.getAuditLogs.bind(dataAccess.authAudit))(options || {});
  }, [dataAccess.authAudit, withLoadingAndError]);

  return {
    // Raw data access layer
    dataAccess,

    // Loading and error state
    isLoading,
    error,

    // Wrapped methods
    getClients,
    getClientById,
    getCases,
    getCaseById,
    getTasks,
    getTaskById,
    getSecurityEvents,
    getAuthAuditLogs
  };
}
