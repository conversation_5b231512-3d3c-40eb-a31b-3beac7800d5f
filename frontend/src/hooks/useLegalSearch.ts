/**
 * Legal Search Hook - Replaces direct Pinecone calls with laws-API
 * 
 * This hook provides a clean interface for legal document search,
 * recommendations, and graph queries using the laws-API service.
 */

'use client';

import { useState, useCallback, useRef } from 'react';
import { useLawsApiClient } from '@/lib/laws-api/client';
import {
  SearchRequest,
  SearchResult,
  RecommendRequest,
  RecommendationResult,
  GraphRequest,
  GraphResult,
  PracticeArea,
  SortBy
} from '@/types/laws-api';
import { handleLawsApiError } from '@/lib/laws-api/error-handler';

export interface UseLegalSearchOptions {
  defaultJurisdiction?: string[];
  defaultPracticeArea?: PracticeArea[];
  defaultSortBy?: SortBy;
  defaultAuthorityMin?: number;
  enableAutoRetry?: boolean;
  cacheResults?: boolean;
}

export interface SearchState {
  isLoading: boolean;
  error: string | null;
  results: SearchResult[];
  hasMore: boolean;
  totalResults: number;
}

export interface RecommendationState {
  isLoading: boolean;
  error: string | null;
  recommendations: RecommendationResult[];
}

export interface GraphState {
  isLoading: boolean;
  error: string | null;
  graph: GraphResult | null;
}

export function useLegalSearch(options: UseLegalSearchOptions = {}) {
  const {
    defaultJurisdiction = ['texas'],
    defaultPracticeArea = [PracticeArea.PERSONAL_INJURY],
    defaultSortBy = SortBy.RELEVANCE,
    defaultAuthorityMin = 0,
    cacheResults = true
  } = options;

  const lawsApiClient = useLawsApiClient();
  
  // Search state
  const [searchState, setSearchState] = useState<SearchState>({
    isLoading: false,
    error: null,
    results: [],
    hasMore: false,
    totalResults: 0
  });

  // Recommendation state
  const [recommendationState, setRecommendationState] = useState<RecommendationState>({
    isLoading: false,
    error: null,
    recommendations: []
  });

  // Graph state
  const [graphState, setGraphState] = useState<GraphState>({
    isLoading: false,
    error: null,
    graph: null
  });

  // Cache for results (simple in-memory cache)
  const searchCache = useRef<Map<string, SearchResult[]>>(new Map());
  const recommendationCache = useRef<Map<string, RecommendationResult[]>>(new Map());

  /**
   * Search for legal documents
   */
  const search = useCallback(async (
    query: string,
    filters?: Partial<SearchRequest>
  ): Promise<SearchResult[]> => {
    const cacheKey = JSON.stringify({ query, filters });
    
    // Check cache first
    if (cacheResults && searchCache.current.has(cacheKey)) {
      const cachedResults = searchCache.current.get(cacheKey)!;
      setSearchState(prev => ({
        ...prev,
        results: cachedResults,
        totalResults: cachedResults.length,
        hasMore: false
      }));
      return cachedResults;
    }

    setSearchState(prev => ({ ...prev, isLoading: true, error: null }));

    try {
      const searchRequest: SearchRequest = {
        query,
        jurisdiction: defaultJurisdiction,
        limit: 20,
        offset: 0,
        // New v1 parameters
        practice_areas: filters?.practice_areas || defaultPracticeArea,
        sort_by: filters?.sort_by || defaultSortBy,
        authority_min: filters?.authority_min || defaultAuthorityMin,
        date_start: filters?.date_start,
        date_end: filters?.date_end,
        // Legacy filters for backward compatibility
        filters: {
          practice_area: defaultPracticeArea,
          ...filters?.filters
        },
        ...filters
      };

      const response = await lawsApiClient.search(searchRequest);
      
      if (!response.success) {
        throw new Error(response.message || 'Search failed');
      }

      const results = response.data;
      
      // Cache results
      if (cacheResults) {
        searchCache.current.set(cacheKey, results);
      }

      setSearchState({
        isLoading: false,
        error: null,
        results,
        hasMore: response.pagination?.has_next || false,
        totalResults: response.pagination?.total || results.length
      });

      return results;
    } catch (_error) {
      // Use enhanced error handler
      const lawsApiError = handleLawsApiError(error, {
        showToast: true,
        enableRetry: true,
        context: { operation: 'search', query }
      });

      setSearchState(prev => ({
        ...prev,
        isLoading: false,
        error: lawsApiError.message,
        results: [],
        hasMore: false,
        totalResults: 0
      }));

      throw lawsApiError;
    }
  }, [lawsApiClient, defaultJurisdiction, defaultPracticeArea, defaultSortBy, defaultAuthorityMin, cacheResults]);

  /**
   * Get recommendations based on document or content
   */
  const recommend = useCallback(async (
    request: Partial<RecommendRequest>
  ): Promise<RecommendationResult[]> => {
    const cacheKey = JSON.stringify(request);
    
    // Check cache first
    if (cacheResults && recommendationCache.current.has(cacheKey)) {
      const cachedResults = recommendationCache.current.get(cacheKey)!;
      setRecommendationState(prev => ({
        ...prev,
        recommendations: cachedResults
      }));
      return cachedResults;
    }

    setRecommendationState(prev => ({ ...prev, isLoading: true, error: null }));

    try {
      const recommendRequest: RecommendRequest = {
        jurisdiction: defaultJurisdiction,
        limit: 10,
        similarity_threshold: 0.7,
        ...request
      };

      const response = await lawsApiClient.recommend(recommendRequest);
      
      if (!response.success) {
        throw new Error(response.message || 'Recommendation failed');
      }

      const recommendations = response.data;
      
      // Cache results
      if (cacheResults) {
        recommendationCache.current.set(cacheKey, recommendations);
      }

      setRecommendationState({
        isLoading: false,
        error: null,
        recommendations
      });

      return recommendations;
    } catch (_error) {
      // Use enhanced error handler
      const lawsApiError = handleLawsApiError(error, {
        showToast: true,
        enableRetry: true,
        context: { operation: 'recommend', request }
      });

      setRecommendationState(prev => ({
        ...prev,
        isLoading: false,
        error: lawsApiError.message,
        recommendations: []
      }));

      throw lawsApiError;
    }
  }, [lawsApiClient, defaultJurisdiction, cacheResults]);

  /**
   * Query the legal knowledge graph
   */
  const queryGraph = useCallback(async (
    request: Partial<GraphRequest>
  ): Promise<GraphResult> => {
    setGraphState(prev => ({ ...prev, isLoading: true, error: null }));

    try {
      const graphRequest: GraphRequest = {
        depth: 2,
        limit: 50,
        ...request
      };

      const response = await lawsApiClient.graph(graphRequest);
      
      if (!response.success) {
        throw new Error(response.message || 'Graph query failed');
      }

      const graph = response.data;

      setGraphState({
        isLoading: false,
        error: null,
        graph
      });

      return graph;
    } catch (_error) {
      // Use enhanced error handler
      const lawsApiError = handleLawsApiError(error, {
        showToast: true,
        enableRetry: true,
        context: { operation: 'graph', request }
      });

      setGraphState(prev => ({
        ...prev,
        isLoading: false,
        error: lawsApiError.message,
        graph: null
      }));

      throw lawsApiError;
    }
  }, [lawsApiClient]);

  /**
   * Clear all cached results
   */
  const clearCache = useCallback(() => {
    searchCache.current.clear();
    recommendationCache.current.clear();
  }, []);

  /**
   * Reset all states
   */
  const reset = useCallback(() => {
    setSearchState({
      isLoading: false,
      error: null,
      results: [],
      hasMore: false,
      totalResults: 0
    });
    setRecommendationState({
      isLoading: false,
      error: null,
      recommendations: []
    });
    setGraphState({
      isLoading: false,
      error: null,
      graph: null
    });
  }, []);

  return {
    // Search functionality
    search,
    searchState,
    
    // Recommendation functionality
    recommend,
    recommendationState,
    
    // Graph functionality
    queryGraph,
    graphState,
    
    // Utility functions
    clearCache,
    reset,
    
    // Computed states
    isSearching: searchState.isLoading,
    isRecommending: recommendationState.isLoading,
    isQuerying: graphState.isLoading,
    hasSearchResults: searchState.results.length > 0,
    hasRecommendations: recommendationState.recommendations.length > 0,
    hasGraphData: !!graphState.graph
  };
}


