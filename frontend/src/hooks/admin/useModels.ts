'use client';

import useS<PERSON> from 'swr';
import { useState } from 'react';
import { useToast } from '@/components/ui/use-toast';
import { useAuthenticatedFetch, HttpError } from '@/hooks/useAuthenticatedFetch';
import { ModelProvider } from '@/app/api/admin/models/route';

// Define types for models
export interface LLMModel {
  id: string;
  provider: ModelProvider;
  name: string;
  description?: string;
  maxTokens: number;
  isDefault: boolean;
}

export interface UseModelsResult {
  models: LLMModel[];
  defaultModels: Record<string, string>;
  isLoading: boolean;
  error: Error | null;
  mutate: () => Promise<void>;
  setDefaultModel: (provider: ModelProvider, modelId: string) => Promise<boolean>;
}

/**
 * Custom hook for managing LLM models
 */
export function useModels(provider?: ModelProvider): UseModelsResult {
  const { authedFetch, isReady } = useAuthenticatedFetch();
  const { toast } = useToast();
  const [error, setError] = useState<Error | null>(null);

  // Fetch models using SWR
  const { data, error: swrError, isLoading, mutate } = useSWR(
    isReady ? `/api/admin/models${provider ? `?provider=${provider}` : ''}` : null,
    async (url: string) => {
      try {
        const response = await authedFetch<{ models: LLMModel[], defaultModels: Record<string, string> }>(url);
        return response;
      } catch (_err) {
        setError(err instanceof Error ? err : new Error('Failed to fetch models'));
        throw err;
      }
    }
  );

  // Set the default model for a provider
  const setDefaultModel = async (provider: ModelProvider, modelId: string): Promise<boolean> => {
    if (!isReady) {
      toast({ variant: 'destructive', title: 'Error', description: 'Authentication not ready' });
      return false;
    }

    try {
      const response = await authedFetch<{ success: boolean }>(
        '/api/admin/models',
        {
          method: 'PATCH',
          body: JSON.stringify({ provider, modelId }),
        }
      );

      if (response.success) {
        toast({ title: 'Success', description: `Default model for ${provider} updated successfully` });
        await mutate();
        return true;
      }
      return false;
    } catch (_err) {
      const errorMsg = err instanceof HttpError
        ? `API Error (${err.status}): ${err.message}`
        : (err instanceof Error ? err.message : 'Failed to update default model');
      
      toast({ variant: 'destructive', title: 'Error', description: errorMsg });
      setError(err instanceof Error ? err : new Error('Failed to update default model'));
      return false;
    }
  };

  return {
    models: data?.models || [],
    defaultModels: data?.defaultModels || {},
    isLoading,
    error: swrError || error,
    mutate: async () => { await mutate(); },
    setDefaultModel,
  };
}
