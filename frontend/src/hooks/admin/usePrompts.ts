'use client';

import useSWR from 'swr';
import { useState } from 'react';
import { useToast } from '@/components/ui/use-toast';
import { useAuthenticatedFetch, HttpError } from '@/hooks/useAuthenticatedFetch';

// Define types for prompts
export interface PromptVersion {
  id: number;
  prompt_id: number;
  version: number;
  content: string;
  metadata: Record<string, any> | null;
  created_at: string;
  created_by: string | null;
}

export interface Prompt {
  id: number;
  key: string;
  description: string | null;
  tags: string[] | null;
  created_at: string;
  updated_at: string;
  is_active: boolean;
  latestVersion?: PromptVersion;
  versions?: PromptVersion[];
}

export interface UsePromptsResult {
  prompts: Prompt[];
  isLoading: boolean;
  error: Error | null;
  mutate: () => Promise<void>;
  createPrompt: (promptData: CreatePromptData) => Promise<Prompt | null>;
  updatePrompt: (promptData: UpdatePromptData) => Promise<Prompt | null>;
  deletePrompt: (id: number) => Promise<boolean>;
  getPromptById: (id: number) => Promise<{ prompt: Prompt, versions: PromptVersion[] } | null>;
}

export interface CreatePromptData {
  key: string;
  description?: string;
  tags?: string[];
  content: string;
  metadata?: Record<string, any>;
}

export interface UpdatePromptData {
  id: number;
  key?: string;
  description?: string;
  tags?: string[];
  is_active?: boolean;
}

/**
 * Custom hook for managing prompts
 */
export function usePrompts(): UsePromptsResult {
  const { authedFetch, isReady } = useAuthenticatedFetch();
  const { toast } = useToast();
  const [error, setError] = useState<Error | null>(null);

  // Fetch prompts using SWR
  const { data, error: swrError, isLoading, mutate } = useSWR(
    isReady ? '/api/admin/prompts' : null,
    async (url: string) => {
      try {
        const response = await authedFetch<{ prompts: Prompt[] }>(url);
        return response.prompts;
      } catch (_err) {
        setError(err instanceof Error ? err : new Error('Failed to fetch prompts'));
        throw err;
      }
    }
  );

  // Create a new prompt
  const createPrompt = async (promptData: CreatePromptData): Promise<Prompt | null> => {
    if (!isReady) {
      toast({ variant: 'destructive', title: 'Error', description: 'Authentication not ready' });
      return null;
    }

    try {
      const response = await authedFetch<{ success: boolean; data: Prompt }>(
        '/api/admin/prompts',
        {
          method: 'POST',
          body: JSON.stringify(promptData),
        }
      );

      if (response.success) {
        toast({ title: 'Success', description: 'Prompt created successfully' });
        await mutate();
        return response.data;
      }
      return null;
    } catch (_err) {
      const errorMsg = err instanceof HttpError
        ? `API Error (${err.status}): ${err.message}`
        : (err instanceof Error ? err.message : 'Failed to create prompt');
      
      toast({ variant: 'destructive', title: 'Error', description: errorMsg });
      setError(err instanceof Error ? err : new Error('Failed to create prompt'));
      return null;
    }
  };

  // Update an existing prompt
  const updatePrompt = async (promptData: UpdatePromptData): Promise<Prompt | null> => {
    if (!isReady) {
      toast({ variant: 'destructive', title: 'Error', description: 'Authentication not ready' });
      return null;
    }

    try {
      const response = await authedFetch<{ success: boolean; data: Prompt }>(
        '/api/admin/prompts',
        {
          method: 'PATCH',
          body: JSON.stringify(promptData),
        }
      );

      if (response.success) {
        toast({ title: 'Success', description: 'Prompt updated successfully' });
        await mutate();
        return response.data;
      }
      return null;
    } catch (_err) {
      const errorMsg = err instanceof HttpError
        ? `API Error (${err.status}): ${err.message}`
        : (err instanceof Error ? err.message : 'Failed to update prompt');
      
      toast({ variant: 'destructive', title: 'Error', description: errorMsg });
      setError(err instanceof Error ? err : new Error('Failed to update prompt'));
      return null;
    }
  };

  // Delete a prompt
  const deletePrompt = async (id: number): Promise<boolean> => {
    if (!isReady) {
      toast({ variant: 'destructive', title: 'Error', description: 'Authentication not ready' });
      return false;
    }

    try {
      const response = await authedFetch<{ success: boolean }>(
        `/api/admin/prompts?id=${id}`,
        {
          method: 'DELETE',
        }
      );

      if (response.success) {
        toast({ title: 'Success', description: 'Prompt deleted successfully' });
        await mutate();
        return true;
      }
      return false;
    } catch (_err) {
      const errorMsg = err instanceof HttpError
        ? `API Error (${err.status}): ${err.message}`
        : (err instanceof Error ? err.message : 'Failed to delete prompt');
      
      toast({ variant: 'destructive', title: 'Error', description: errorMsg });
      setError(err instanceof Error ? err : new Error('Failed to delete prompt'));
      return false;
    }
  };

  // Get a prompt by ID with all versions
  const getPromptById = async (id: number): Promise<{ prompt: Prompt, versions: PromptVersion[] } | null> => {
    if (!isReady) {
      toast({ variant: 'destructive', title: 'Error', description: 'Authentication not ready' });
      return null;
    }

    try {
      const response = await authedFetch<{ prompt: Prompt, versions: PromptVersion[] }>(
        `/api/admin/prompts?id=${id}`
      );
      return response;
    } catch (_err) {
      const errorMsg = err instanceof HttpError
        ? `API Error (${err.status}): ${err.message}`
        : (err instanceof Error ? err.message : 'Failed to fetch prompt');
      
      toast({ variant: 'destructive', title: 'Error', description: errorMsg });
      setError(err instanceof Error ? err : new Error('Failed to fetch prompt'));
      return null;
    }
  };

  return {
    prompts: data || [],
    isLoading,
    error: swrError || error,
    mutate: async () => { await mutate(); },
    createPrompt,
    updatePrompt,
    deletePrompt,
    getPromptById,
  };
}
