import { useState, useCallback } from 'react';
import { useAuthenticatedFetch } from './useAuthenticatedFetch';
import { CaseStatus } from '@/types/domain/tenants/Case';

export interface Case {
  id: string;
  title: string;
  description?: string | null;
  sensitive?: boolean;
  client_id: string;
  status?: string;
  rejection_reason?: string | null;
  created_at?: string;
  updated_at?: string;
  tenant_id: string;
}

// Define expected API response structure for /api/cases-test
interface CasesTestApiResponse {
  success: boolean;
  data?: Case[];
}

// Define expected API response structure for /api/cases
interface CasesApiResponse {
  cases?: Case[];
}

export interface CaseWithRelations extends Case {
  client?: {
    id: string;
    name: string;
    email?: string;
  };
}

/**
 * Custom hook for working with cases with proper schema handling
 */
export function useCasesApi() {
  const { authedFetch } = useAuthenticatedFetch();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  /**
   * Get all cases with optional filtering
   */
  const getAllCases = useCallback(async (filters?: {
    status?: CaseStatus,
    client_id?: string,
    search?: string,
    page?: number,
    limit?: number
  }) => {
    setIsLoading(true);
    setError(null);
    try {
      // First try the test endpoint which is more resilient
      try {
        const testResponse = await authedFetch<CasesTestApiResponse>('/api/cases-test');
        console.log('Cases test endpoint response:', testResponse);

        // If test endpoint works but returns no data, return empty array early
        if (testResponse?.success && (!testResponse.data || testResponse.data.length === 0)) {
          console.log('No cases found in test endpoint');
          return [];
        }
      } catch (testErr) {
        console.warn('Cases test endpoint failed:', testErr);
        // Continue to try the main endpoint
      }

      // Build query parameters
      const params = new URLSearchParams();

      if (filters?.status) params.append('status', filters.status);
      if (filters?.client_id) params.append('client_id', filters.client_id);
      if (filters?.search) params.append('search', filters.search);
      if (filters?.page) params.append('page', filters.page.toString());
      if (filters?.limit) params.append('limit', filters.limit.toString());

      const queryString = params.toString() ? `?${params.toString()}` : '';

      // Use authenticated fetch with the API route
      const response = await authedFetch<CasesApiResponse>(`/api/cases${queryString}`);
      return response?.cases || [];
    } catch (_err) {
      console.error('Error fetching cases:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch cases');

      // Return an empty array as fallback in case of error
      return [];
    } finally {
      setIsLoading(false);
    }
  }, [authedFetch]);

  /**
   * Get a single case by ID
   */
  const getCaseById = useCallback(async (caseId: string, includeRelations = false) => {
    setIsLoading(true);
    setError(null);
    try {
      const params = new URLSearchParams();
      if (includeRelations) {
        params.append('include', 'relations');
      }

      const queryString = params.toString() ? `?${params.toString()}` : '';
      const response = await authedFetch(`/api/cases/${caseId}${queryString}`);
      return response;
    } catch (_err) {
      console.error(`Error fetching case ${caseId}:`, err);
      setError(err instanceof Error ? err.message : `Failed to fetch case ${caseId}`);
      return null;
    } finally {
      setIsLoading(false);
    }
  }, [authedFetch]);

  /**
   * Create a new case
   */
  const createCase = useCallback(async (caseData: Partial<Case>) => {
    setIsLoading(true);
    setError(null);
    try {
      const response = await authedFetch('/api/cases', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(caseData),
      });

      return response;
    } catch (_err) {
      console.error('Error creating case:', err);
      setError(err instanceof Error ? err.message : 'Failed to create case');
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, [authedFetch]);

  /**
   * Update an existing case
   */
  const updateCase = useCallback(async (caseId: string, caseData: Partial<Case>) => {
    setIsLoading(true);
    setError(null);
    try {
      const response = await authedFetch(`/api/cases/${caseId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(caseData),
      });

      return response;
    } catch (_err) {
      console.error(`Error updating case ${caseId}:`, err);
      setError(err instanceof Error ? err.message : `Failed to update case ${caseId}`);
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, [authedFetch]);

  /**
   * Delete a case
   */
  const deleteCase = useCallback(async (caseId: string) => {
    setIsLoading(true);
    setError(null);
    try {
      const response = await authedFetch(`/api/cases/${caseId}`, {
        method: 'DELETE',
      });

      return response;
    } catch (_err) {
      console.error(`Error deleting case ${caseId}:`, err);
      setError(err instanceof Error ? err.message : `Failed to delete case ${caseId}`);
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, [authedFetch]);

  return {
    // Data and state
    isLoading,
    error,

    // CRUD operations
    getAllCases,
    getCaseById,
    createCase,
    updateCase,
    deleteCase,
  };
}
