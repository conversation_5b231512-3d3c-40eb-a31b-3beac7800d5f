'use client';

import { useState, useEffect, useCallback } from 'react';
import useSWR from 'swr';
import { useAuthenticatedFetch } from '@/hooks/useAuthenticatedFetch';
import { useRbac } from '@/hooks/useRbac';
import { useToast } from '@/components/ui/use-toast';
import { QueueStats } from './useQueueStats';

export interface SystemQueueStats {
  tenants: QueueStats[];
  totals: {
    queued: number;
    processing: number;
    completed: number;
    failed: number;
    total_today: number;
    active_tenants: number;
  };
  worker_health: {
    status: 'healthy' | 'degraded' | 'down';
    last_heartbeat: string;
    workers_active: number;
    workers_total: number;
  };
  last_updated: string;
}

export interface UseSystemQueueStatsResult {
  data: SystemQueueStats | null;
  isLoading: boolean;
  error: Error | null;
  mutate: () => Promise<SystemQueueStats | undefined>;
  clearTenantQueue: (tenantId: string) => Promise<void>;
  startWorker: () => Promise<void>;
  stopWorker: () => Promise<void>;
}

/**
 * Hook for fetching system-wide queue statistics (super-admin only)
 */
export function useSystemQueueStats(): UseSystemQueueStatsResult {
  const { authedFetch, isReady } = useAuthenticatedFetch();
  const { isSuperAdmin } = useRbac();
  const { toast } = useToast();
  const [error, setError] = useState<Error | null>(null);
  const [, setWsConnected] = useState(false);

  // Only allow super-admins to use this hook
  const canAccess = isSuperAdmin();

  // Get polling interval from environment or default to 5 seconds
  const pollingInterval = parseInt(process.env.NEXT_PUBLIC_QUEUE_POLLING_INTERVAL || '5000');

  // Fetch system queue stats using SWR with polling
  const { data, error: swrError, isLoading, mutate } = useSWR(
    isReady && canAccess ? '/api/v1/queue/all' : null,
    async (url: string) => {
      try {
        const response = await authedFetch<SystemQueueStats>(url);
        setError(null);
        return response;
      } catch (_err) {
        const errorObj = err instanceof Error ? err : new Error('Failed to fetch system queue stats');
        setError(errorObj);
        throw errorObj;
      }
    },
    {
      refreshInterval: pollingInterval,
      revalidateOnFocus: true,
      revalidateOnReconnect: true,
      dedupingInterval: 1000,
    }
  );

  // WebSocket connection for real-time updates
  useEffect(() => {
    if (!isReady || !canAccess) return;

    let ws: WebSocket | null = null;
    let reconnectTimeout: NodeJS.Timeout;
    let reconnectAttempts = 0;
    const maxReconnectAttempts = 5;

    const connectWebSocket = () => {
      try {
        const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
        const wsUrl = `${protocol}//${window.location.host}/api/v1/queue/ws/system`;
        
        ws = new WebSocket(wsUrl);

        ws.onopen = () => {
          console.log('System Queue WebSocket connected');
          setWsConnected(true);
          reconnectAttempts = 0;
        };

        ws.onmessage = (event) => {
          try {
            const message = JSON.parse(event.data);
            if (message.type === 'system_queue_stats' && message.data) {
              mutate(message.data, false);
            }
          } catch (_err) {
            console.warn('Failed to parse WebSocket message:', err);
          }
        };

        ws.onclose = () => {
          console.log('System Queue WebSocket disconnected');
          setWsConnected(false);
          
          if (reconnectAttempts < maxReconnectAttempts) {
            const delay = Math.min(1000 * Math.pow(2, reconnectAttempts), 30000);
            reconnectTimeout = setTimeout(() => {
              reconnectAttempts++;
              connectWebSocket();
            }, delay);
          }
        };

        ws.onerror = (error) => {
          console.warn('System Queue WebSocket error:', error);
          setWsConnected(false);
        };
      } catch (_err) {
        console.warn('Failed to connect to system queue WebSocket:', err);
        setWsConnected(false);
      }
    };

    if (data) {
      connectWebSocket();
    }

    return () => {
      if (reconnectTimeout) {
        clearTimeout(reconnectTimeout);
      }
      if (ws) {
        ws.close();
      }
    };
  }, [isReady, canAccess, data, mutate]);

  // Clear specific tenant queue
  const clearTenantQueue = useCallback(async (tenantId: string) => {
    if (!isReady || !canAccess) {
      throw new Error('Unauthorized');
    }

    try {
      await authedFetch(`/api/v1/queue/clear?tenant_id=${tenantId}`, {
        method: 'POST',
      });

      await mutate();
      
      toast({
        title: 'Tenant Queue Cleared',
        description: `Queue for tenant ${tenantId} has been cleared successfully.`,
      });
    } catch (_err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to clear tenant queue';
      toast({
        title: 'Error',
        description: errorMessage,
        variant: 'destructive',
      });
      throw err;
    }
  }, [authedFetch, isReady, canAccess, mutate, toast]);

  // Start worker
  const startWorker = useCallback(async () => {
    if (!isReady || !canAccess) {
      throw new Error('Unauthorized');
    }

    try {
      await authedFetch('/api/v1/queue/worker/start', {
        method: 'POST',
      });

      await mutate();
      
      toast({
        title: 'Worker Started',
        description: 'Queue worker has been started successfully.',
      });
    } catch (_err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to start worker';
      toast({
        title: 'Error',
        description: errorMessage,
        variant: 'destructive',
      });
      throw err;
    }
  }, [authedFetch, isReady, canAccess, mutate, toast]);

  // Stop worker
  const stopWorker = useCallback(async () => {
    if (!isReady || !canAccess) {
      throw new Error('Unauthorized');
    }

    try {
      await authedFetch('/api/v1/queue/worker/stop', {
        method: 'POST',
      });

      await mutate();
      
      toast({
        title: 'Worker Stopped',
        description: 'Queue worker has been stopped successfully.',
      });
    } catch (_err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to stop worker';
      toast({
        title: 'Error',
        description: errorMessage,
        variant: 'destructive',
      });
      throw err;
    }
  }, [authedFetch, isReady, canAccess, mutate, toast]);

  return {
    data: data || null,
    isLoading,
    error: error || swrError,
    mutate,
    clearTenantQueue,
    startWorker,
    stopWorker,
  };
}
