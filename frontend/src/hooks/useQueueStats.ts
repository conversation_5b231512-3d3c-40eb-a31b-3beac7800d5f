'use client';

import { useState, useEffect, useCallback } from 'react';
import useSWR from 'swr';
import { useAuthenticatedFetch } from '@/hooks/useAuthenticatedFetch';
import { useToast } from '@/components/ui/use-toast';

export interface QueueStats {
  tenant_id: string;
  queued: number;
  processing: number;
  completed: number;
  failed: number;
  total_today: number;
  avg_processing_time_seconds: number;
  last_updated: string;
  worker_status: 'healthy' | 'degraded' | 'down';
}

export interface UseQueueStatsResult {
  data: QueueStats | null;
  isLoading: boolean;
  error: Error | null;
  mutate: () => Promise<QueueStats | undefined>;
  clearQueue: () => Promise<void>;
}

/**
 * Hook for fetching tenant queue statistics with real-time updates
 */
export function useQueueStats(): UseQueueStatsResult {
  const { authedFetch, isReady } = useAuthenticatedFetch();
  const { toast } = useToast();
  const [error, setError] = useState<Error | null>(null);
  const [, setWsConnected] = useState(false);

  // Get polling interval from environment or default to 5 seconds
  const pollingInterval = parseInt(process.env.NEXT_PUBLIC_QUEUE_POLLING_INTERVAL || '5000');

  // Fetch queue stats using SWR with polling
  const { data, error: swrError, isLoading, mutate } = useSWR(
    isReady ? '/api/v1/queue/' : null,
    async (url: string) => {
      try {
        const response = await authedFetch<QueueStats>(url);
        setError(null);
        return response;
      } catch (_err) {
        const errorObj = err instanceof Error ? err : new Error('Failed to fetch queue stats');
        setError(errorObj);
        throw errorObj;
      }
    },
    {
      refreshInterval: pollingInterval,
      revalidateOnFocus: true,
      revalidateOnReconnect: true,
      dedupingInterval: 1000, // Prevent duplicate requests within 1 second
    }
  );

  // WebSocket connection for real-time updates
  useEffect(() => {
    if (!isReady || !data?.tenant_id) return;

    let ws: WebSocket | null = null;
    let reconnectTimeout: NodeJS.Timeout;
    let reconnectAttempts = 0;
    const maxReconnectAttempts = 5;

    const connectWebSocket = () => {
      try {
        // Use wss in production, ws in development
        const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
        const wsUrl = `${protocol}//${window.location.host}/api/v1/queue/ws`;
        
        ws = new WebSocket(wsUrl);

        ws.onopen = () => {
          console.log('Queue WebSocket connected');
          setWsConnected(true);
          reconnectAttempts = 0;
        };

        ws.onmessage = (event) => {
          try {
            const message = JSON.parse(event.data);
            if (message.type === 'queue_stats' && message.data) {
              // Update SWR cache with new data
              mutate(message.data, false);
            }
          } catch (_err) {
            console.warn('Failed to parse WebSocket message:', err);
          }
        };

        ws.onclose = () => {
          console.log('Queue WebSocket disconnected');
          setWsConnected(false);
          
          // Attempt to reconnect with exponential backoff
          if (reconnectAttempts < maxReconnectAttempts) {
            const delay = Math.min(1000 * Math.pow(2, reconnectAttempts), 30000);
            reconnectTimeout = setTimeout(() => {
              reconnectAttempts++;
              connectWebSocket();
            }, delay);
          }
        };

        ws.onerror = (error) => {
          console.warn('Queue WebSocket error:', error);
          setWsConnected(false);
        };
      } catch (_err) {
        console.warn('Failed to connect to queue WebSocket:', err);
        setWsConnected(false);
      }
    };

    // Only attempt WebSocket connection if SWR polling is working
    if (data) {
      connectWebSocket();
    }

    return () => {
      if (reconnectTimeout) {
        clearTimeout(reconnectTimeout);
      }
      if (ws) {
        ws.close();
      }
    };
  }, [isReady, data?.tenant_id, mutate, data]);

  // Clear queue function
  const clearQueue = useCallback(async () => {
    if (!isReady) {
      throw new Error('Not authenticated');
    }

    try {
      await authedFetch('/api/v1/queue/clear', {
        method: 'POST',
      });

      // Refresh data after clearing
      await mutate();
      
      toast({
        title: 'Queue Cleared',
        description: 'All queued items have been cleared successfully.',
      });
    } catch (_err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to clear queue';
      toast({
        title: 'Error',
        description: errorMessage,
        variant: 'destructive',
      });
      throw err;
    }
  }, [authedFetch, isReady, mutate, toast]);

  return {
    data: data || null,
    isLoading,
    error: error || swrError,
    mutate,
    clearQueue,
  };
}
