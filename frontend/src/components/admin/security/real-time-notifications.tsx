'use client'

import { useState, useEffect } from 'react'
import { useSupabase } from '@/lib/supabase/provider'
import { toast } from 'sonner'
import { Bell, AlertTriangle, Shield, Key, Activity } from 'lucide-react'
import { Button } from '@/components/ui/button'
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover'
import { Badge } from '@/components/ui/badge'

interface SecurityNotification {
  id: string
  type: 'alert' | 'warning' | 'info'
  title: string
  message: string
  timestamp: string
  read: boolean
}

export function RealTimeSecurityNotifications() {
  const [notifications, setNotifications] = useState<SecurityNotification[]>([])
  const [unreadCount, setUnreadCount] = useState(0)
  const [open, setOpen] = useState(false)
  const { supabase } = useSupabase()

  useEffect(() => {
    // Load initial notifications
    fetchNotifications()

    // Set up real-time subscription
    const channel = supabase
      .channel('security-notifications')
      .on(
        'postgres_changes',
        {
          event: 'INSERT',
          schema: 'security',
          table: 'events',
          filter: 'event_category=eq.suspicious',
        },
        (payload: any) => {
          handleNewSecurityEvent(payload.new)
        }
      )
      .on(
        'postgres_changes',
        {
          event: 'INSERT',
          schema: 'security',
          table: 'alerts',
        },
        (payload: any) => {
          handleNewSecurityAlert(payload.new)
        }
      )
      .subscribe()

    return () => {
      supabase.removeChannel(channel)
    }
  }, [])

  useEffect(() => {
    // Update unread count whenever notifications change
    setUnreadCount(notifications.filter(n => !n.read).length)
  }, [notifications])

  const fetchNotifications = async () => {
    try {
      // In a real implementation, this would fetch from an API
      // For now, we'll use mock data
      const mockNotifications: SecurityNotification[] = [
        {
          id: '1',
          type: 'alert',
          title: 'Suspicious Login Detected',
          message: 'Unusual login location detected <NAME_EMAIL> from Moscow, Russia',
          timestamp: new Date(Date.now() - 15 * 60 * 1000).toISOString(),
          read: false
        },
        {
          id: '2',
          type: 'warning',
          title: 'Multiple Failed Login Attempts',
          message: '5 failed login attempts <NAME_EMAIL>',
          timestamp: new Date(Date.now() - 45 * 60 * 1000).toISOString(),
          read: false
        },
        {
          id: '3',
          type: 'info',
          title: 'User Role Changed',
          message: 'User <EMAIL> role changed from staff to admin',
          timestamp: new Date(Date.now() - 120 * 60 * 1000).toISOString(),
          read: true
        }
      ]

      setNotifications(mockNotifications)
    } catch (_error) {
      console.error('Error fetching notifications:', error)
    }
  }

  const handleNewSecurityEvent = (event: any) => {
    // Create a notification from the security event
    const newNotification: SecurityNotification = {
      id: event.id,
      type: 'warning',
      title: 'Security Event Detected',
      message: `${event.event_type} detected for user ${event.user_id}`,
      timestamp: event.created_at,
      read: false
    }

    // Add to notifications
    setNotifications(prev => [newNotification, ...prev])

    // Show toast
    toast.warning(newNotification.title, {
      description: newNotification.message,
      action: {
        label: 'View',
        onClick: () => setOpen(true)
      }
    })
  }

  const handleNewSecurityAlert = (alert: any) => {
    // Create a notification from the security alert
    const newNotification: SecurityNotification = {
      id: alert.id,
      type: 'alert',
      title: alert.title || 'Security Alert',
      message: alert.message || 'A security alert has been triggered',
      timestamp: alert.created_at,
      read: false
    }

    // Add to notifications
    setNotifications(prev => [newNotification, ...prev])

    // Show toast
    toast.error(newNotification.title, {
      description: newNotification.message,
      action: {
        label: 'View',
        onClick: () => setOpen(true)
      }
    })
  }

  const markAsRead = (id: string) => {
    setNotifications(prev =>
      prev.map(notification =>
        notification.id === id
          ? { ...notification, read: true }
          : notification
      )
    )
  }

  const markAllAsRead = () => {
    setNotifications(prev =>
      prev.map(notification => ({ ...notification, read: true }))
    )
  }

  const formatTime = (timestamp: string) => {
    const date = new Date(timestamp)
    const now = new Date()
    const diffMs = now.getTime() - date.getTime()
    const diffMins = Math.floor(diffMs / 60000)

    if (diffMins < 1) return 'Just now'
    if (diffMins < 60) return `${diffMins}m ago`

    const diffHours = Math.floor(diffMins / 60)
    if (diffHours < 24) return `${diffHours}h ago`

    return date.toLocaleDateString()
  }

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'alert':
        return <AlertTriangle className="h-5 w-5 text-red-500" />
      case 'warning':
        return <Shield className="h-5 w-5 text-yellow-500" />
      case 'info':
        return <Activity className="h-5 w-5 text-blue-500" />
      default:
        return <Bell className="h-5 w-5 text-gray-500" />
    }
  }

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button variant="ghost" size="icon" className="relative">
          <Bell className="h-5 w-5" />
          {unreadCount > 0 && (
            <Badge className="absolute -top-1 -right-1 h-5 w-5 flex items-center justify-center p-0">
              {unreadCount}
            </Badge>
          )}
        </Button>
      </PopoverTrigger>
      <PopoverContent align="end" className="w-80 p-0">
        <div className="flex items-center justify-between p-4 border-b">
          <h3 className="font-semibold">Security Notifications</h3>
          {unreadCount > 0 && (
            <Button variant="ghost" size="sm" onClick={markAllAsRead}>
              Mark all as read
            </Button>
          )}
        </div>
        <div className="max-h-80 overflow-y-auto">
          {notifications.length === 0 ? (
            <div className="p-4 text-center text-sm text-muted-foreground">
              No notifications
            </div>
          ) : (
            notifications.map(notification => (
              <div
                key={notification.id}
                className={`p-4 border-b last:border-b-0 ${
                  notification.read ? 'bg-background' : 'bg-muted/30'
                }`}
                onClick={() => markAsRead(notification.id)}
              >
                <div className="flex items-start gap-3">
                  <div className="mt-0.5">
                    {getNotificationIcon(notification.type)}
                  </div>
                  <div className="flex-1 space-y-1">
                    <div className="flex items-center justify-between">
                      <p className="font-medium text-sm">{notification.title}</p>
                      <span className="text-xs text-muted-foreground">
                        {formatTime(notification.timestamp)}
                      </span>
                    </div>
                    <p className="text-sm text-muted-foreground">
                      {notification.message}
                    </p>
                  </div>
                </div>
              </div>
            ))
          )}
        </div>
        <div className="p-2 border-t">
          <Button variant="outline" size="sm" className="w-full" asChild>
            <a href="/admin/security/alerts">View All Alerts</a>
          </Button>
        </div>
      </PopoverContent>
    </Popover>
  )
}
