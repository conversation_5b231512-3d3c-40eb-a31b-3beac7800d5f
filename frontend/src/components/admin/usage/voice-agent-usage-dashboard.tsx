'use client';

import React, { useState } from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Bar<PERSON>hart, Phone } from 'lucide-react';
import { useAuthenticatedFetch, HttpError } from '@/hooks/useAuthenticatedFetch';
import { useToast } from '@/components/ui/use-toast';

export interface VoiceAgentUsageData {
  id: string;
  tenant_id: string;
  usage_type: string;
  usage_count: number;
  resource_size_bytes: number | null;
  period_start: string;
  period_end: string;
  created_at: string | null;
}

interface VoiceAgentUsageDashboardProps {
  initialVoiceAgentUsageData: VoiceAgentUsageData[];
}

export default function VoiceAgentUsageDashboard({ initialVoiceAgentUsageData }: VoiceAgentUsageDashboardProps) {
  const [voiceAgentUsageData, setVoiceAgentUsageData] = useState<VoiceAgentUsageData[]>(initialVoiceAgentUsageData);
  const [isLoading, setIsLoading] = useState(false);
  const { authedFetch, isReady, error: fetchHookError } = useAuthenticatedFetch();
  const { toast } = useToast();

  // Calculate total minutes by type
  const totalInboundSeconds = voiceAgentUsageData
    .filter(item => item.usage_type === 'voice_agent_inbound')
    .reduce((sum, item) => sum + item.usage_count, 0);

  const totalOutboundSeconds = voiceAgentUsageData
    .filter(item => item.usage_type === 'voice_agent_outbound')
    .reduce((sum, item) => sum + item.usage_count, 0);

  const totalSeconds = totalInboundSeconds + totalOutboundSeconds;

  // Convert seconds to minutes
  const secondsToMinutes = (seconds: number) => {
    return (seconds / 60).toFixed(1);
  };

  // Format date
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  const handleRefresh = async () => {
    if (!isReady) {
      toast({ variant: 'destructive', title: 'Error', description: `Authentication context not ready. ${fetchHookError || ''}` });
      return;
    }
    setIsLoading(true);

    try {
      const data = await authedFetch<any>('/api/admin/usage?usageType=voice_agent');

      setVoiceAgentUsageData(data.usage || []);
      toast({ title: 'Success', description: 'Voice agent usage data refreshed.' });
    } catch (_error) {
      console.error('Error fetching voice agent usage data:', error);
      const errorMsg = error instanceof HttpError
        ? `API Error (${error.status}): ${error.message}`
        : (error instanceof Error ? error.message : 'Failed to fetch voice agent usage data');
      toast({ variant: 'destructive', title: 'Error', description: errorMsg });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="space-y-4">
      <div className="flex justify-end">
        <Button onClick={handleRefresh} disabled={isLoading}>
          {isLoading ? 'Refreshing...' : 'Refresh Data'}
        </Button>
      </div>

      <div className="grid gap-4 md:grid-cols-3">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between pb-2">
            <CardTitle className="text-sm font-medium">Total Voice Usage</CardTitle>
            <Phone className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{secondsToMinutes(totalSeconds)} min</div>
            <p className="text-xs text-muted-foreground mt-1">
              Combined inbound and outbound minutes
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between pb-2">
            <CardTitle className="text-sm font-medium">Inbound Calls</CardTitle>
            <Phone className="h-4 w-4 text-blue-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{secondsToMinutes(totalInboundSeconds)} min</div>
            <p className="text-xs text-muted-foreground mt-1">
              Client calls handled by AI
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between pb-2">
            <CardTitle className="text-sm font-medium">Outbound Calls</CardTitle>
            <Phone className="h-4 w-4 text-green-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{secondsToMinutes(totalOutboundSeconds)} min</div>
            <p className="text-xs text-muted-foreground mt-1">
              AI-initiated calls to clients
            </p>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Voice Agent Usage Distribution</CardTitle>
          <CardDescription>
            Inbound vs. Outbound call minutes
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div>
              <div className="flex justify-between text-sm mb-1">
                <span>Inbound Calls</span>
                <span>{totalSeconds > 0 ? Math.round((totalInboundSeconds / totalSeconds) * 100) : 0}%</span>
              </div>
              <Progress value={totalSeconds > 0 ? (totalInboundSeconds / totalSeconds) * 100 : 0} className="h-2 bg-blue-100" indicatorClassName="bg-blue-500" />
            </div>
            <div>
              <div className="flex justify-between text-sm mb-1">
                <span>Outbound Calls</span>
                <span>{totalSeconds > 0 ? Math.round((totalOutboundSeconds / totalSeconds) * 100) : 0}%</span>
              </div>
              <Progress value={totalSeconds > 0 ? (totalOutboundSeconds / totalSeconds) * 100 : 0} className="h-2 bg-green-100" indicatorClassName="bg-green-500" />
            </div>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Recent Voice Agent Usage</CardTitle>
          <CardDescription>
            Latest voice agent usage records
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Type</TableHead>
                  <TableHead>Duration</TableHead>
                  <TableHead>Period</TableHead>
                  <TableHead>Created</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {voiceAgentUsageData.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={4} className="text-center py-4 text-muted-foreground">
                      No voice agent usage data available
                    </TableCell>
                  </TableRow>
                ) : (
                  voiceAgentUsageData.map((item) => (
                    <TableRow key={item.id}>
                      <TableCell>
                        {item.usage_type === 'voice_agent_inbound'
                          ? 'Inbound Calls'
                          : item.usage_type === 'voice_agent_outbound'
                          ? 'Outbound Calls'
                          : item.usage_type}
                      </TableCell>
                      <TableCell>{secondsToMinutes(item.usage_count)} min</TableCell>
                      <TableCell>
                        {formatDate(item.period_start)} - {formatDate(item.period_end)}
                      </TableCell>
                      <TableCell>
                        {item.created_at ? new Date(item.created_at).toLocaleString() : 'N/A'}
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
