'use client';

import React, { useState } from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { BarChart, FileText, MessageSquare, Phone } from 'lucide-react';
import { useAuthenticatedFetch, HttpError } from '@/hooks/useAuthenticatedFetch';
import { useToast } from '@/components/ui/use-toast';

export interface UsageData {
  usage_type: string;
  sum: number;
}

interface UsageOverviewDashboardProps {
  initialUsageData: UsageData[];
}

export default function UsageOverviewDashboard({ initialUsageData }: UsageOverviewDashboardProps) {
  const [usageData, setUsageData] = useState<UsageData[]>(initialUsageData);
  const [timeRange, setTimeRange] = useState('30d');
  const [isLoading, setIsLoading] = useState(false);
  const { authedFetch, isReady, error: fetchHookError } = useAuthenticatedFetch();
  const { toast } = useToast();

  // Helper function to format usage type
  const formatUsageType = (type: string) => {
    return type
      .split('_')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
  };

  // Helper function to get icon for usage type
  const getUsageTypeIcon = (type: string) => {
    if (type.includes('document')) {
      return <FileText className="h-8 w-8 text-blue-500" />;
    } else if (type.includes('token')) {
      return <MessageSquare className="h-8 w-8 text-green-500" />;
    } else if (type.includes('voice')) {
      return <Phone className="h-8 w-8 text-purple-500" />;
    } else {
      return <BarChart className="h-8 w-8 text-gray-500" />;
    }
  };

  // Helper function to format usage value
  const formatUsageValue = (type: string, value: number) => {
    if (type.includes('voice_agent')) {
      // Convert seconds to minutes
      return `${(value / 60).toFixed(1)} minutes`;
    } else if (type.includes('token')) {
      // Format large numbers with K, M, etc.
      return value >= 1000000
        ? `${(value / 1000000).toFixed(1)}M`
        : value >= 1000
        ? `${(value / 1000).toFixed(1)}K`
        : value.toString();
    } else {
      return value.toString();
    }
  };

  const handleRefresh = async () => {
    if (!isReady) {
      toast({ variant: 'destructive', title: 'Error', description: `Authentication context not ready. ${fetchHookError || ''}` });
      return;
    }
    setIsLoading(true);

    try {
      const data = await authedFetch<any>(`/api/admin/usage?timeRange=${timeRange}`);

      setUsageData(data.usage || []);
      toast({ title: 'Success', description: 'Usage data refreshed.' });
    } catch (_error) {
      console.error('Error fetching usage data:', error);
      const errorMsg = error instanceof HttpError
        ? `API Error (${error.status}): ${error.message}`
        : (error instanceof Error ? error.message : 'Failed to fetch usage data');
      toast({ variant: 'destructive', title: 'Error', description: errorMsg });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <Label htmlFor="timeRange">Time Range:</Label>
          <Select
            value={timeRange}
            onValueChange={setTimeRange}
          >
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Select time range" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="7d">Last 7 days</SelectItem>
              <SelectItem value="30d">Last 30 days</SelectItem>
              <SelectItem value="90d">Last 90 days</SelectItem>
              <SelectItem value="1y">Last year</SelectItem>
            </SelectContent>
          </Select>
        </div>
        <Button onClick={handleRefresh} disabled={isLoading}>
          {isLoading ? 'Refreshing...' : 'Refresh Data'}
        </Button>
      </div>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        {usageData.length === 0 ? (
          <div className="col-span-full text-center py-8 text-muted-foreground">
            No usage data available
          </div>
        ) : (
          usageData.map((item, index) => (
            <Card key={index}>
              <CardHeader className="flex flex-row items-center justify-between pb-2">
                <CardTitle className="text-sm font-medium">
                  {formatUsageType(item.usage_type)}
                </CardTitle>
                {getUsageTypeIcon(item.usage_type)}
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {formatUsageValue(item.usage_type, item.sum)}
                </div>
                <p className="text-xs text-muted-foreground mt-1">
                  Total usage for selected period
                </p>
              </CardContent>
            </Card>
          ))
        )}
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Usage Trends</CardTitle>
          <CardDescription>
            Resource usage over time
          </CardDescription>
        </CardHeader>
        <CardContent className="h-[300px] flex items-center justify-center text-muted-foreground">
          <BarChart className="h-16 w-16" />
          <span className="ml-2">Usage trend chart will be displayed here</span>
        </CardContent>
      </Card>
    </div>
  );
}
