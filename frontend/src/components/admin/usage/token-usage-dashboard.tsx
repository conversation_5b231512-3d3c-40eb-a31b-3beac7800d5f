'use client';

import React, { useState } from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { BarChart, MessageSquare } from 'lucide-react';
import { useAuthenticatedFetch, HttpError } from '@/hooks/useAuthenticatedFetch';
import { useToast } from '@/components/ui/use-toast';

export interface TokenUsageData {
  id: string;
  tenant_id: string;
  usage_type: string;
  usage_count: number;
  resource_size_bytes: number | null;
  period_start: string;
  period_end: string;
  created_at: string | null;
}

interface TokenUsageDashboardProps {
  initialTokenUsageData: TokenUsageData[];
}

export default function TokenUsageDashboard({ initialTokenUsageData }: TokenUsageDashboardProps) {
  const [tokenUsageData, setTokenUsageData] = useState<TokenUsageData[]>(initialTokenUsageData);
  const [isLoading, setIsLoading] = useState(false);
  const { authedFetch, isReady, error: fetchHookError } = useAuthenticatedFetch();
  const { toast } = useToast();

  // Calculate total tokens by type
  const totalInputTokens = tokenUsageData
    .filter(item => item.usage_type === 'ai_tokens_input')
    .reduce((sum, item) => sum + item.usage_count, 0);

  const totalOutputTokens = tokenUsageData
    .filter(item => item.usage_type === 'ai_tokens_output')
    .reduce((sum, item) => sum + item.usage_count, 0);

  const totalTokens = totalInputTokens + totalOutputTokens;

  // Format large numbers
  const formatNumber = (value: number) => {
    return value >= 1000000
      ? `${(value / 1000000).toFixed(1)}M`
      : value >= 1000
      ? `${(value / 1000).toFixed(1)}K`
      : value.toString();
  };

  // Format date
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  const handleRefresh = async () => {
    if (!isReady) {
      toast({ variant: 'destructive', title: 'Error', description: `Authentication context not ready. ${fetchHookError || ''}` });
      return;
    }
    setIsLoading(true);

    try {
      const data = await authedFetch<any>('/api/admin/usage?usageType=ai_tokens');

      setTokenUsageData(data.usage || []);
      toast({ title: 'Success', description: 'Token usage data refreshed.' });
    } catch (_error) {
      console.error('Error fetching token usage data:', error);
      const errorMsg = error instanceof HttpError
        ? `API Error (${error.status}): ${error.message}`
        : (error instanceof Error ? error.message : 'Failed to fetch token usage data');
      toast({ variant: 'destructive', title: 'Error', description: errorMsg });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="space-y-4">
      <div className="flex justify-end">
        <Button onClick={handleRefresh} disabled={isLoading}>
          {isLoading ? 'Refreshing...' : 'Refresh Data'}
        </Button>
      </div>

      <div className="grid gap-4 md:grid-cols-3">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between pb-2">
            <CardTitle className="text-sm font-medium">Total Tokens</CardTitle>
            <MessageSquare className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatNumber(totalTokens)}</div>
            <p className="text-xs text-muted-foreground mt-1">
              Combined input and output tokens
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between pb-2">
            <CardTitle className="text-sm font-medium">Input Tokens</CardTitle>
            <MessageSquare className="h-4 w-4 text-blue-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatNumber(totalInputTokens)}</div>
            <p className="text-xs text-muted-foreground mt-1">
              Tokens sent to AI models
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between pb-2">
            <CardTitle className="text-sm font-medium">Output Tokens</CardTitle>
            <MessageSquare className="h-4 w-4 text-green-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatNumber(totalOutputTokens)}</div>
            <p className="text-xs text-muted-foreground mt-1">
              Tokens generated by AI models
            </p>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Token Usage Distribution</CardTitle>
          <CardDescription>
            Input vs. Output token usage
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div>
              <div className="flex justify-between text-sm mb-1">
                <span>Input Tokens</span>
                <span>{totalInputTokens > 0 ? Math.round((totalInputTokens / totalTokens) * 100) : 0}%</span>
              </div>
              <Progress value={totalInputTokens > 0 ? (totalInputTokens / totalTokens) * 100 : 0} className="h-2 bg-blue-100" indicatorClassName="bg-blue-500" />
            </div>
            <div>
              <div className="flex justify-between text-sm mb-1">
                <span>Output Tokens</span>
                <span>{totalOutputTokens > 0 ? Math.round((totalOutputTokens / totalTokens) * 100) : 0}%</span>
              </div>
              <Progress value={totalOutputTokens > 0 ? (totalOutputTokens / totalTokens) * 100 : 0} className="h-2 bg-green-100" indicatorClassName="bg-green-500" />
            </div>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Recent Token Usage</CardTitle>
          <CardDescription>
            Latest token usage records
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Type</TableHead>
                  <TableHead>Tokens</TableHead>
                  <TableHead>Period</TableHead>
                  <TableHead>Created</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {tokenUsageData.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={4} className="text-center py-4 text-muted-foreground">
                      No token usage data available
                    </TableCell>
                  </TableRow>
                ) : (
                  tokenUsageData.map((item) => (
                    <TableRow key={item.id}>
                      <TableCell>
                        {item.usage_type === 'ai_tokens_input'
                          ? 'Input Tokens'
                          : item.usage_type === 'ai_tokens_output'
                          ? 'Output Tokens'
                          : item.usage_type === 'ai_tokens_total'
                          ? 'Total Tokens'
                          : item.usage_type}
                      </TableCell>
                      <TableCell>{formatNumber(item.usage_count)}</TableCell>
                      <TableCell>
                        {formatDate(item.period_start)} - {formatDate(item.period_end)}
                      </TableCell>
                      <TableCell>
                        {item.created_at ? new Date(item.created_at).toLocaleString() : 'N/A'}
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
