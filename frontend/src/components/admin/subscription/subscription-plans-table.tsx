'use client';

import React, { useState } from 'react';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { Edit, MoreHorizontal, Plus, Trash } from 'lucide-react';
import { toast } from 'sonner';
import { useAuthenticatedFetch, HttpError } from '@/hooks/useAuthenticatedFetch';
import { SubscriptionPlan } from '@/lib/supabase/subscription.types';

interface SubscriptionPlansTableProps {
  initialPlans: SubscriptionPlan[];
}

export default function SubscriptionPlansTable({ initialPlans }: SubscriptionPlansTableProps) {
  const [plans, setPlans] = useState<SubscriptionPlan[]>(initialPlans);
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [currentPlan, setCurrentPlan] = useState<SubscriptionPlan | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const { authedFetch, isReady, error: fetchHookError } = useAuthenticatedFetch();

  // Form state for new/edit plan
  const [formData, setFormData] = useState({
    name: '',
    code: '',
    description: '',
    isActive: true,
    isPublic: true,
    basePriceMonthly: 0,
    basePriceYearly: 0,
    features: '{}',
  });

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value,
    });
  };

  const handleSwitchChange = (name: string, checked: boolean) => {
    setFormData({
      ...formData,
      [name]: checked,
    });
  };

  const handleAddPlan = async () => {
    if (!isReady) {
      toast.error(`Authentication context not ready. ${fetchHookError || ''}`);
      return;
    }
    setIsLoading(true);

    try {
      // Parse features JSON
      const featuresObj = JSON.parse(formData.features);

      const { plan } = await authedFetch<any>('/api/admin/subscription', {
        method: 'POST',
        body: JSON.stringify({
          action: 'createPlan',
          data: {
            name: formData.name,
            code: formData.code,
            description: formData.description || null,
            isActive: formData.isActive,
            isPublic: formData.isPublic,
            basePriceMonthly: parseFloat(formData.basePriceMonthly.toString()),
            basePriceYearly: parseFloat(formData.basePriceYearly.toString()),
            features: featuresObj,
          },
        }),
      });

      // Update local state
      setPlans([...plans, plan]);

      // Reset form and close dialog
      setFormData({
        name: '',
        code: '',
        description: '',
        isActive: true,
        isPublic: true,
        basePriceMonthly: 0,
        basePriceYearly: 0,
        features: '{}',
      });
      setIsAddDialogOpen(false);

      toast.success('Subscription plan created successfully');
    } catch (_error) {
      console.error('Error creating subscription plan:', error);
      const errorMsg = error instanceof HttpError
        ? `API Error (${error.status}): ${error.message}`
        : (error instanceof Error ? error.message : 'Failed to create subscription plan');
      toast.error(errorMsg);
    } finally {
      setIsLoading(false);
    }
  };

  const handleEditPlan = async () => {
    if (!currentPlan) return;
    if (!isReady) {
      toast.error(`Authentication context not ready. ${fetchHookError || ''}`);
      return;
    }

    setIsLoading(true);

    try {
      // Parse features JSON
      const featuresObj = JSON.parse(formData.features);

      const { plan: updatedPlan } = await authedFetch<any>('/api/admin/subscription', {
        method: 'PUT',
        body: JSON.stringify({
          action: 'updatePlan',
          planId: currentPlan.id,
          data: {
            name: formData.name,
            description: formData.description || null,
            isActive: formData.isActive,
            isPublic: formData.isPublic,
            basePriceMonthly: parseFloat(formData.basePriceMonthly.toString()),
            basePriceYearly: parseFloat(formData.basePriceYearly.toString()),
            features: featuresObj,
          },
        }),
      });

      // Update local state
      setPlans(plans.map(p => p.id === updatedPlan.id ? updatedPlan : p));

      // Reset form and close dialog
      setCurrentPlan(null);
      setIsEditDialogOpen(false);

      toast.success('Subscription plan updated successfully');
    } catch (_error) {
      console.error('Error updating subscription plan:', error);
      const errorMsg = error instanceof HttpError
        ? `API Error (${error.status}): ${error.message}`
        : (error instanceof Error ? error.message : 'Failed to update subscription plan');
      toast.error(errorMsg);
    } finally {
      setIsLoading(false);
    }
  };

  const handleDeletePlan = async () => {
    if (!currentPlan) return;
    if (!isReady) {
      toast.error(`Authentication context not ready. ${fetchHookError || ''}`);
      return;
    }

    setIsLoading(true);

    try {
      await authedFetch<any>('/api/admin/subscription', {
        method: 'PUT',
        body: JSON.stringify({
          action: 'deactivatePlan',
          planId: currentPlan.id,
        }),
      });

      // Update local state (mark as inactive)
      setPlans(plans.map(p => p.id === currentPlan.id ? { ...p, is_active: false } : p));

      // Reset form and close dialog
      setCurrentPlan(null);
      setIsDeleteDialogOpen(false);

      toast.success('Subscription plan deactivated successfully');
    } catch (_error) {
      console.error('Error deactivating subscription plan:', error);
      const errorMsg = error instanceof HttpError
        ? `API Error (${error.status}): ${error.message}`
        : (error instanceof Error ? error.message : 'Failed to deactivate subscription plan');
      toast.error(errorMsg);
    } finally {
      setIsLoading(false);
    }
  };

  const openEditDialog = (plan: SubscriptionPlan) => {
    setCurrentPlan(plan);
    setFormData({
      name: plan.name,
      code: plan.code,
      description: plan.description || '',
      isActive: plan.is_active ?? false,
      isPublic: plan.is_public ?? false,
      basePriceMonthly: plan.base_price_monthly,
      basePriceYearly: plan.base_price_yearly,
      features: JSON.stringify(plan.features, null, 2),
    });
    setIsEditDialogOpen(true);
  };

  const openDeleteDialog = (plan: SubscriptionPlan) => {
    setCurrentPlan(plan);
    setIsDeleteDialogOpen(true);
  };

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <div>
          <h3 className="text-lg font-medium">Subscription Plans</h3>
          <p className="text-sm text-muted-foreground">
            Manage the subscription plans available to tenants
          </p>
        </div>
        <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="mr-2 h-4 w-4" />
              Add Plan
            </Button>
          </DialogTrigger>
          <DialogContent className="sm:max-w-[600px]">
            <DialogHeader>
              <DialogTitle>Add Subscription Plan</DialogTitle>
              <DialogDescription>
                Create a new subscription plan for tenants
              </DialogDescription>
            </DialogHeader>
            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="name">Plan Name</Label>
                  <Input
                    id="name"
                    name="name"
                    value={formData.name}
                    onChange={handleInputChange}
                    placeholder="e.g. Basic Plan"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="code">Plan Code</Label>
                  <Input
                    id="code"
                    name="code"
                    value={formData.code}
                    onChange={handleInputChange}
                    placeholder="e.g. basic"
                  />
                </div>
              </div>
              <div className="space-y-2">
                <Label htmlFor="description">Description</Label>
                <Textarea
                  id="description"
                  name="description"
                  value={formData.description}
                  onChange={handleInputChange}
                  placeholder="Describe the plan features and benefits"
                  rows={2}
                />
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="basePriceMonthly">Monthly Price ($)</Label>
                  <Input
                    id="basePriceMonthly"
                    name="basePriceMonthly"
                    type="number"
                    value={formData.basePriceMonthly}
                    onChange={handleInputChange}
                    placeholder="99.00"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="basePriceYearly">Yearly Price ($)</Label>
                  <Input
                    id="basePriceYearly"
                    name="basePriceYearly"
                    type="number"
                    value={formData.basePriceYearly}
                    onChange={handleInputChange}
                    placeholder="999.00"
                  />
                </div>
              </div>
              <div className="space-y-2">
                <Label htmlFor="features">Features (JSON)</Label>
                <Textarea
                  id="features"
                  name="features"
                  value={formData.features}
                  onChange={handleInputChange}
                  placeholder='{"hasFeatureX": true, "maxUsers": 5}'
                  rows={5}
                />
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div className="flex items-center space-x-2">
                  <Switch
                    id="isActive"
                    checked={formData.isActive}
                    onCheckedChange={(checked) => handleSwitchChange('isActive', checked)}
                  />
                  <Label htmlFor="isActive">Active</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Switch
                    id="isPublic"
                    checked={formData.isPublic}
                    onCheckedChange={(checked) => handleSwitchChange('isPublic', checked)}
                  />
                  <Label htmlFor="isPublic">Public</Label>
                </div>
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setIsAddDialogOpen(false)}>
                Cancel
              </Button>
              <Button onClick={handleAddPlan} disabled={isLoading}>
                {isLoading ? 'Creating...' : 'Create Plan'}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>

      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Name</TableHead>
              <TableHead>Code</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Monthly Price</TableHead>
              <TableHead>Yearly Price</TableHead>
              <TableHead>Features</TableHead>
              <TableHead className="w-[80px]">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {plans.length === 0 ? (
              <TableRow>
                <TableCell colSpan={7} className="text-center py-4 text-muted-foreground">
                  No subscription plans found
                </TableCell>
              </TableRow>
            ) : (
              plans.map((plan) => (
                <TableRow key={plan.id}>
                  <TableCell className="font-medium">{plan.name}</TableCell>
                  <TableCell>{plan.code}</TableCell>
                  <TableCell>
                    <div className="flex space-x-2">
                      {plan.is_active ? (
                        <Badge variant="default">Active</Badge>
                      ) : (
                        <Badge variant="secondary">Inactive</Badge>
                      )}
                      {plan.is_public ? (
                        <Badge variant="outline">Public</Badge>
                      ) : (
                        <Badge variant="outline">Private</Badge>
                      )}
                    </div>
                  </TableCell>
                  <TableCell>${plan.base_price_monthly.toFixed(2)}</TableCell>
                  <TableCell>${plan.base_price_yearly.toFixed(2)}</TableCell>
                  <TableCell>
                    <div className="max-w-[200px] truncate">
                      {plan.features && Object.keys(plan.features).length > 0
                        ? Object.keys(plan.features).join(', ')
                        : 'No features'}
                    </div>
                  </TableCell>
                  <TableCell>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="icon">
                          <MoreHorizontal className="h-4 w-4" />
                          <span className="sr-only">Open menu</span>
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuLabel>Actions</DropdownMenuLabel>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem onClick={() => openEditDialog(plan)}>
                          <Edit className="mr-2 h-4 w-4" />
                          Edit
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          onClick={() => openDeleteDialog(plan)}
                          className="text-destructive focus:text-destructive"
                        >
                          <Trash className="mr-2 h-4 w-4" />
                          Deactivate
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>

      {/* Edit Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>Edit Subscription Plan</DialogTitle>
            <DialogDescription>
              Update the subscription plan details
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="edit-name">Plan Name</Label>
                <Input
                  id="edit-name"
                  name="name"
                  value={formData.name}
                  onChange={handleInputChange}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="edit-code">Plan Code</Label>
                <Input
                  id="edit-code"
                  name="code"
                  value={formData.code}
                  onChange={handleInputChange}
                  disabled
                />
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="edit-description">Description</Label>
              <Textarea
                id="edit-description"
                name="description"
                value={formData.description}
                onChange={handleInputChange}
                rows={2}
              />
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="edit-basePriceMonthly">Monthly Price ($)</Label>
                <Input
                  id="edit-basePriceMonthly"
                  name="basePriceMonthly"
                  type="number"
                  value={formData.basePriceMonthly}
                  onChange={handleInputChange}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="edit-basePriceYearly">Yearly Price ($)</Label>
                <Input
                  id="edit-basePriceYearly"
                  name="basePriceYearly"
                  type="number"
                  value={formData.basePriceYearly}
                  onChange={handleInputChange}
                />
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="edit-features">Features (JSON)</Label>
              <Textarea
                id="edit-features"
                name="features"
                value={formData.features}
                onChange={handleInputChange}
                rows={5}
              />
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="flex items-center space-x-2">
                <Switch
                  id="edit-isActive"
                  checked={formData.isActive}
                  onCheckedChange={(checked) => handleSwitchChange('isActive', checked)}
                />
                <Label htmlFor="edit-isActive">Active</Label>
              </div>
              <div className="flex items-center space-x-2">
                <Switch
                  id="edit-isPublic"
                  checked={formData.isPublic}
                  onCheckedChange={(checked) => handleSwitchChange('isPublic', checked)}
                />
                <Label htmlFor="edit-isPublic">Public</Label>
              </div>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsEditDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleEditPlan} disabled={isLoading}>
              {isLoading ? 'Saving...' : 'Save Changes'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Delete Dialog */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Deactivate Subscription Plan</DialogTitle>
            <DialogDescription>
              Are you sure you want to deactivate the "{currentPlan?.name}" plan? This will hide it from new subscribers.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsDeleteDialogOpen(false)}>
              Cancel
            </Button>
            <Button variant="destructive" onClick={handleDeletePlan} disabled={isLoading}>
              {isLoading ? 'Deactivating...' : 'Deactivate Plan'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
