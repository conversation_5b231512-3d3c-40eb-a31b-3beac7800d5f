'use client';

import React, { useState } from 'react';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import {
  Edit,
  MoreHorizontal,
  Plus,
  Trash,
} from 'lucide-react';
import { toast } from 'sonner';
import { useAuthenticatedFetch, HttpError } from '@/hooks/useAuthenticatedFetch';
import { useToast } from '@/components/ui/use-toast';
import { SubscriptionAddon } from '@/lib/supabase/subscription.types';

interface SubscriptionAddonsTableProps {
  initialAddons: SubscriptionAddon[];
}

export default function SubscriptionAddonsTable({ initialAddons }: SubscriptionAddonsTableProps) {
  const [addons, setAddons] = useState<SubscriptionAddon[]>(initialAddons);
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [currentAddon, setCurrentAddon] = useState<SubscriptionAddon | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const { authedFetch, isReady, error: fetchHookError } = useAuthenticatedFetch();
  const { toast: showToast } = useToast();

  // Form state for new/edit addon
  const [formData, setFormData] = useState({
    name: '',
    code: '',
    description: '',
    category: '',
    isActive: true,
    priceMonthly: 0,
    priceYearly: 0,
    features: '{}',
  });

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value,
    });
  };

  const handleSwitchChange = (name: string, checked: boolean) => {
    setFormData({
      ...formData,
      [name]: checked,
    });
  };

  const handleAddAddon = async () => {
    if (!isReady) {
      showToast({ variant: 'destructive', title: 'Error', description: `Authentication context not ready. ${fetchHookError || ''}` });
      return;
    }
    setIsLoading(true);

    try {
      // Parse features JSON
      const featuresObj = JSON.parse(formData.features);

      const { addon } = await authedFetch<any>('/api/admin/subscription', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'createAddon',
          data: {
            name: formData.name,
            code: formData.code,
            description: formData.description || null,
            category: formData.category,
            isActive: formData.isActive,
            priceMonthly: parseFloat(formData.priceMonthly.toString()),
            priceYearly: parseFloat(formData.priceYearly.toString()),
            features: featuresObj,
          },
        }),
      });

      // Update local state
      setAddons([...addons, addon]);

      // Reset form and close dialog
      setFormData({
        name: '',
        code: '',
        description: '',
        category: '',
        isActive: true,
        priceMonthly: 0,
        priceYearly: 0,
        features: '{}',
      });
      setIsAddDialogOpen(false);

      toast.success('Subscription addon created successfully');
    } catch (_error) {
      console.error('Error creating subscription addon:', error);
      const errorMsg = error instanceof HttpError
        ? `API Error (${error.status}): ${error.message}`
        : (error instanceof Error ? error.message : 'Failed to create subscription addon');
      showToast({ variant: 'destructive', title: 'Error', description: errorMsg });
    } finally {
      setIsLoading(false);
    }
  };

  const handleEditAddon = async () => {
    if (!currentAddon) return;
    if (!isReady) {
      showToast({ variant: 'destructive', title: 'Error', description: `Authentication context not ready. ${fetchHookError || ''}` });
      return;
    }

    setIsLoading(true);

    try {
      // Parse features JSON
      const featuresObj = JSON.parse(formData.features);

      const { addon: updatedAddon } = await authedFetch<any>('/api/admin/subscription', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'updateAddon',
          addonId: currentAddon.id,
          data: {
            name: formData.name,
            description: formData.description || null,
            category: formData.category,
            isActive: formData.isActive,
            priceMonthly: parseFloat(formData.priceMonthly.toString()),
            priceYearly: parseFloat(formData.priceYearly.toString()),
            features: featuresObj,
          },
        }),
      });

      // Update local state
      setAddons(addons.map(a => a.id === updatedAddon.id ? updatedAddon : a));

      // Reset form and close dialog
      setCurrentAddon(null);
      setIsEditDialogOpen(false);

      toast.success('Subscription addon updated successfully');
    } catch (_error) {
      console.error('Error updating subscription addon:', error);
      const errorMsg = error instanceof HttpError
        ? `API Error (${error.status}): ${error.message}`
        : (error instanceof Error ? error.message : 'Failed to update subscription addon');
      showToast({ variant: 'destructive', title: 'Error', description: errorMsg });
    } finally {
      setIsLoading(false);
    }
  };

  const handleDeleteAddon = async () => {
    if (!currentAddon) return;
    if (!isReady) {
      showToast({ variant: 'destructive', title: 'Error', description: `Authentication context not ready. ${fetchHookError || ''}` });
      return;
    }

    setIsLoading(true);

    try {
      await authedFetch<any>('/api/admin/subscription', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'deactivateAddon',
          addonId: currentAddon.id,
        }),
      });

      // Update local state (mark as inactive)
      setAddons(addons.map(a => a.id === currentAddon.id ? { ...a, is_active: false } : a));

      // Reset form and close dialog
      setCurrentAddon(null);
      setIsDeleteDialogOpen(false);

      toast.success('Subscription addon deactivated successfully');
    } catch (_error) {
      console.error('Error deactivating subscription addon:', error);
      const errorMsg = error instanceof HttpError
        ? `API Error (${error.status}): ${error.message}`
        : (error instanceof Error ? error.message : 'Failed to deactivate subscription addon');
      showToast({ variant: 'destructive', title: 'Error', description: errorMsg });
    } finally {
      setIsLoading(false);
    }
  };

  const openEditDialog = (addon: SubscriptionAddon) => {
    setCurrentAddon(addon);
    setFormData({
      name: addon.name,
      code: addon.code,
      description: addon.description || '',
      category: addon.category,
      isActive: addon.is_active ?? false,
      priceMonthly: addon.price_monthly,
      priceYearly: addon.price_yearly,
      features: JSON.stringify(addon.features, null, 2),
    });
    setIsEditDialogOpen(true);
  };

  const openDeleteDialog = (addon: SubscriptionAddon) => {
    setCurrentAddon(addon);
    setIsDeleteDialogOpen(true);
  };

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <div>
          <h3 className="text-lg font-medium">Subscription Addons</h3>
          <p className="text-sm text-muted-foreground">
            Manage the subscription addons available to tenants
          </p>
        </div>
        <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="mr-2 h-4 w-4" />
              Add Addon
            </Button>
          </DialogTrigger>
          <DialogContent className="sm:max-w-[600px]">
            <DialogHeader>
              <DialogTitle>Add Subscription Addon</DialogTitle>
              <DialogDescription>
                Create a new subscription addon for tenants
              </DialogDescription>
            </DialogHeader>
            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="name">Addon Name</Label>
                  <Input
                    id="name"
                    name="name"
                    value={formData.name}
                    onChange={handleInputChange}
                    placeholder="e.g. Extra Users"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="code">Addon Code</Label>
                  <Input
                    id="code"
                    name="code"
                    value={formData.code}
                    onChange={handleInputChange}
                    placeholder="e.g. extra_users"
                  />
                </div>
              </div>
              <div className="space-y-2">
                <Label htmlFor="description">Description</Label>
                <Textarea
                  id="description"
                  name="description"
                  value={formData.description}
                  onChange={handleInputChange}
                  placeholder="Describe the addon features and benefits"
                  rows={2}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="category">Category</Label>
                <Input
                  id="category"
                  name="category"
                  value={formData.category}
                  onChange={handleInputChange}
                  placeholder="e.g. users, storage, features"
                />
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="priceMonthly">Monthly Price ($)</Label>
                  <Input
                    id="priceMonthly"
                    name="priceMonthly"
                    type="number"
                    value={formData.priceMonthly}
                    onChange={handleInputChange}
                    placeholder="19.00"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="priceYearly">Yearly Price ($)</Label>
                  <Input
                    id="priceYearly"
                    name="priceYearly"
                    type="number"
                    value={formData.priceYearly}
                    onChange={handleInputChange}
                    placeholder="190.00"
                  />
                </div>
              </div>
              <div className="space-y-2">
                <Label htmlFor="features">Features (JSON)</Label>
                <Textarea
                  id="features"
                  name="features"
                  value={formData.features}
                  onChange={handleInputChange}
                  placeholder='{"additionalUsers": 5, "hasFeatureX": true}'
                  rows={5}
                />
              </div>
              <div className="flex items-center space-x-2">
                <Switch
                  id="isActive"
                  checked={formData.isActive}
                  onCheckedChange={(checked) => handleSwitchChange('isActive', checked)}
                />
                <Label htmlFor="isActive">Active</Label>
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setIsAddDialogOpen(false)}>
                Cancel
              </Button>
              <Button onClick={handleAddAddon} disabled={isLoading}>
                {isLoading ? 'Creating...' : 'Create Addon'}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>

      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Name</TableHead>
              <TableHead>Code</TableHead>
              <TableHead>Category</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Monthly Price</TableHead>
              <TableHead>Yearly Price</TableHead>
              <TableHead>Features</TableHead>
              <TableHead className="w-[80px]">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {addons.length === 0 ? (
              <TableRow>
                <TableCell colSpan={8} className="text-center py-4 text-muted-foreground">
                  No subscription addons found
                </TableCell>
              </TableRow>
            ) : (
              addons.map((addon) => (
                <TableRow key={addon.id}>
                  <TableCell className="font-medium">{addon.name}</TableCell>
                  <TableCell>{addon.code}</TableCell>
                  <TableCell>
                    <Badge variant="outline">{addon.category}</Badge>
                  </TableCell>
                  <TableCell>
                    {addon.is_active ? (
                      <Badge variant="default">Active</Badge>
                    ) : (
                      <Badge variant="secondary">Inactive</Badge>
                    )}
                  </TableCell>
                  <TableCell>${addon.price_monthly.toFixed(2)}</TableCell>
                  <TableCell>${addon.price_yearly.toFixed(2)}</TableCell>
                  <TableCell>
                    <div className="max-w-[200px] truncate">
                      {addon.features && Object.keys(addon.features).length > 0
                        ? Object.keys(addon.features).join(', ')
                        : 'No features'}
                    </div>
                  </TableCell>
                  <TableCell>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="icon">
                          <MoreHorizontal className="h-4 w-4" />
                          <span className="sr-only">Open menu</span>
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuLabel>Actions</DropdownMenuLabel>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem onClick={() => openEditDialog(addon)}>
                          <Edit className="mr-2 h-4 w-4" />
                          Edit
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          onClick={() => openDeleteDialog(addon)}
                          className="text-destructive focus:text-destructive"
                        >
                          <Trash className="mr-2 h-4 w-4" />
                          Deactivate
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>

      {/* Edit Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>Edit Subscription Addon</DialogTitle>
            <DialogDescription>
              Update the subscription addon details
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="edit-name">Addon Name</Label>
                <Input
                  id="edit-name"
                  name="name"
                  value={formData.name}
                  onChange={handleInputChange}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="edit-code">Addon Code</Label>
                <Input
                  id="edit-code"
                  name="code"
                  value={formData.code}
                  onChange={handleInputChange}
                  disabled
                />
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="edit-description">Description</Label>
              <Textarea
                id="edit-description"
                name="description"
                value={formData.description}
                onChange={handleInputChange}
                rows={2}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="edit-category">Category</Label>
              <Input
                id="edit-category"
                name="category"
                value={formData.category}
                onChange={handleInputChange}
              />
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="edit-priceMonthly">Monthly Price ($)</Label>
                <Input
                  id="edit-priceMonthly"
                  name="priceMonthly"
                  type="number"
                  value={formData.priceMonthly}
                  onChange={handleInputChange}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="edit-priceYearly">Yearly Price ($)</Label>
                <Input
                  id="edit-priceYearly"
                  name="priceYearly"
                  type="number"
                  value={formData.priceYearly}
                  onChange={handleInputChange}
                />
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="edit-features">Features (JSON)</Label>
              <Textarea
                id="edit-features"
                name="features"
                value={formData.features}
                onChange={handleInputChange}
                rows={5}
              />
            </div>
            <div className="flex items-center space-x-2">
              <Switch
                id="edit-isActive"
                checked={formData.isActive}
                onCheckedChange={(checked) => handleSwitchChange('isActive', checked)}
              />
              <Label htmlFor="edit-isActive">Active</Label>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsEditDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleEditAddon} disabled={isLoading}>
              {isLoading ? 'Saving...' : 'Save Changes'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Delete Dialog */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Deactivate Subscription Addon</DialogTitle>
            <DialogDescription>
              Are you sure you want to deactivate the "{currentAddon?.name}" addon? This will hide it from new subscribers.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsDeleteDialogOpen(false)}>
              Cancel
            </Button>
            <Button variant="destructive" onClick={handleDeleteAddon} disabled={isLoading}>
              {isLoading ? 'Deactivating...' : 'Deactivate Addon'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
