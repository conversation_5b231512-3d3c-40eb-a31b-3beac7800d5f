import { useState, useEffect } from 'react'
import { format } from 'date-fns'
import { Search, Filter } from 'lucide-react'
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { Input } from '@/components/ui/input'
import { Button } from '@/components/ui/button'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover'
import { Calendar } from '@/components/ui/calendar'
import { useTasksApi } from '@/hooks/useTasksApi'
import { toast } from 'sonner'

interface TaskHistoryProps {
  taskId: string
}

export type HistoryEntry = {
  id: string
  task_id: string
  changed_by: { id: string; email: string }
  changed_at: string
  change_type: 'created' | 'updated' | 'status_changed' | 'assigned' | 'deleted'
  previous_values: any
  new_values: any
}

const CHANGE_TYPE_LABELS = {
  created: 'Created',
  updated: 'Updated',
  status_changed: 'Status Changed',
  assigned: 'Assigned',
  deleted: 'Deleted',
}

export function TaskHistory({ taskId }: TaskHistoryProps) {
  const [history, setHistory] = useState<HistoryEntry[]>([])
  // Use schema-aware tasks hook
  const { getTaskHistory, isLoading: historyLoading } = useTasksApi()

  const [isLoading, setIsLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedDate, setSelectedDate] = useState<Date>()
  const [selectedType, setSelectedType] = useState<string>('')
  const [filteredHistory, setFilteredHistory] = useState<HistoryEntry[]>([])

  // Fetch task history using schema-aware hook
  useEffect(() => {
    const fetchHistory = async () => {
      try {
        setIsLoading(true)
        // Use schema-aware getTaskHistory method instead of direct db call
        const data = await getTaskHistory(taskId)
        setHistory(data || [])
        setFilteredHistory(data || [])
      } catch (_error) {
        console.error('Error fetching task history:', error)
        toast.error('Failed to fetch task history')
      } finally {
        setIsLoading(false)
      }
    }

    fetchHistory()
  }, [taskId])

  // Set up real-time subscription for history updates
  useEffect(() => {
    // @ts-expect-error - db variable not defined
    const channel = db.supabase
      .channel('task_history_changes')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'task_history',
          filter: `task_id=eq.${taskId}`,
        },
        (payload: any) => {
          if (payload.eventType === 'INSERT') {
            setHistory((current) => [payload.new as HistoryEntry, ...current])
            setFilteredHistory((current) => [payload.new as HistoryEntry, ...current])
          }
        }
      )
      .subscribe()

    return () => {
      channel.unsubscribe()
    }
  }, [taskId])

  // Apply filters
  useEffect(() => {
    let filtered = [...history]

    // Apply search filter
    if (searchQuery) {
      const query = searchQuery.toLowerCase()
      filtered = filtered.filter((entry) => {
        const changedByEmail = entry.changed_by?.email?.toLowerCase() || ''
        const changeType = CHANGE_TYPE_LABELS[entry.change_type].toLowerCase()
        const changes = JSON.stringify({ ...entry.previous_values, ...entry.new_values }).toLowerCase()

        return changedByEmail.includes(query) ||
               changeType.includes(query) ||
               changes.includes(query)
      })
    }

    // Apply date filter
    if (selectedDate) {
      filtered = filtered.filter((entry) => {
        const entryDate = new Date(entry.changed_at)
        return (
          entryDate.getFullYear() === selectedDate.getFullYear() &&
          entryDate.getMonth() === selectedDate.getMonth() &&
          entryDate.getDate() === selectedDate.getDate()
        )
      })
    }

    // Apply type filter
    if (selectedType) {
      filtered = filtered.filter((entry) => entry.change_type === selectedType)
    }

    setFilteredHistory(filtered)
  }, [history, searchQuery, selectedDate, selectedType])

  const getChangeSummary = (entry: HistoryEntry) => {
    switch (entry.change_type) {
      case 'created':
        return 'Task was created'
      case 'deleted':
        return 'Task was deleted'
      case 'status_changed':
        return `Status changed from "${entry.previous_values?.status || 'none'}" to "${entry.new_values?.status}"`
      case 'assigned':
        const prevAssignee = entry.previous_values?.assigned_to || 'unassigned'
        const newAssignee = entry.new_values?.assigned_to || 'unassigned'
        return `Assignment changed from ${prevAssignee} to ${newAssignee}`
      case 'updated':
        const changes = []
        for (const [key, value] of Object.entries(entry.new_values || {})) {
          if (entry.previous_values?.[key] !== value) {
            changes.push(`${key}: "${entry.previous_values?.[key] || 'none'}" → "${value}"`)
          }
        }
        return changes.join(', ')
      default:
        return 'Unknown change'
    }
  }

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Task History</CardTitle>
          <CardDescription>Loading history...</CardDescription>
        </CardHeader>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Task History</CardTitle>
        <CardDescription>Track all changes made to this task</CardDescription>
        <div className="flex gap-4 mt-4">
          <div className="flex-1">
            <Input
              placeholder="Search history..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full"
              // @ts-expect-error - icon prop not in type
              icon={<Search className="h-4 w-4" />}
            />
          </div>
          <Popover>
            <PopoverTrigger asChild>
              <Button variant="outline">
                <Filter className="h-4 w-4 mr-2" />
                Filter
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-80">
              <div className="space-y-4">
                <div className="space-y-2">
                  <h4 className="font-medium">Change Type</h4>
                  <Select
                    value={selectedType}
                    onValueChange={setSelectedType}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="">All Types</SelectItem>
                      {Object.entries(CHANGE_TYPE_LABELS).map(([value, label]) => (
                        <SelectItem key={value} value={value}>
                          {label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <h4 className="font-medium">Date</h4>
                  <Calendar
                    mode="single"
                    selected={selectedDate}
                    onSelect={setSelectedDate}
                    className="rounded-md border"
                  />
                </div>
              </div>
            </PopoverContent>
          </Popover>
        </div>
      </CardHeader>
      <CardContent>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Date</TableHead>
              <TableHead>User</TableHead>
              <TableHead>Type</TableHead>
              <TableHead>Changes</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredHistory.length === 0 ? (
              <TableRow>
                <TableCell colSpan={4} className="text-center">
                  No history entries found
                </TableCell>
              </TableRow>
            ) : (
              filteredHistory.map((entry) => (
                <TableRow key={entry.id}>
                  <TableCell>
                    {format(new Date(entry.changed_at), 'MMM dd, yyyy HH:mm')}
                  </TableCell>
                  <TableCell>{entry.changed_by.email}</TableCell>
                  <TableCell>{CHANGE_TYPE_LABELS[entry.change_type]}</TableCell>
                  <TableCell>{getChangeSummary(entry)}</TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </CardContent>
    </Card>
  )
}
