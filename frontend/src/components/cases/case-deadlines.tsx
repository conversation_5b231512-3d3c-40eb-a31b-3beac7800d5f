'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import {
  Calendar,
  Clock,
  Plus,
  AlertCircle,
  CheckCircle,
  MoreHorizontal,
  Pencil,
  FileText,
  Trash2,
  Bell
} from 'lucide-react'

import { Button } from '@/components/ui/button'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog'
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { DatePicker } from '@/components/ui/date-picker'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { useToast } from '@/components/ui/use-toast'
import { useSupabase } from '@/lib/supabase/provider'
import { useAuthenticatedFetch, HttpError } from '@/hooks/useAuthenticatedFetch';
import { zodResolver } from '@hookform/resolvers/zod'
import { useForm } from 'react-hook-form'
import * as z from 'zod'

// Deadline form schema
const deadlineFormSchema = z.object({
  title: z.string().min(3, {
    message: "Title must be at least 3 characters.",
  }),
  description: z.string().optional(),
  dueDate: z.date({
    required_error: "Due date is required",
  }),
  priority: z.enum(['low', 'medium', 'high']),
  type: z.string().min(1, {
    message: "Please select a deadline type",
  }),
  reminderDays: z.coerce.number().int().min(0),
  notes: z.string().optional(),
});

// Deadline types
const DEADLINE_TYPES = [
  { id: 'court', label: 'Court Deadline' },
  { id: 'filing', label: 'Filing Deadline' },
  { id: 'discovery', label: 'Discovery' },
  { id: 'client', label: 'Client Meeting' },
  { id: 'deposition', label: 'Deposition' },
  { id: 'mediation', label: 'Mediation' },
  { id: 'hearing', label: 'Hearing' },
  { id: 'trial', label: 'Trial' },
  { id: 'settlement', label: 'Settlement Conference' },
  { id: 'other', label: 'Other' },
];

interface CaseDeadlinesProps {
  caseId: string;
  caseName: string;
}

// Define expected API response structure for deadline operations
interface DeadlineApiResponse {
  success?: boolean;
  data?: Deadline; // Expect the created/updated deadline back
  error?: string;
}

// Define expected API response structure for delete operation
interface DeleteApiResponse {
  success?: boolean;
  error?: string;
}

// This is just an interface to define our deadline data structure
interface Deadline {
  id: string;
  title: string;
  description?: string;
  dueDate: string;
  type: string;
  priority: 'low' | 'medium' | 'high';
  reminderDays: number;
  notes?: string;
  completed: boolean;
  created_at?: string;
  updated_at?: string;
}

export function CaseDeadlines({ caseId, caseName }: CaseDeadlinesProps) {
  const router = useRouter()
  const { supabase } = useSupabase()
  const { toast } = useToast()
  const { authedFetch, isReady, error: fetchHookError } = useAuthenticatedFetch();

  // State
  const [deadlines, setDeadlines] = useState<Deadline[]>([])
  const [loading, setLoading] = useState(true)
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false)
  const [selectedDeadline, setSelectedDeadline] = useState<Deadline | null>(null)
  const [isDeleting, setIsDeleting] = useState(false)

  // Initialize form for adding/editing deadlines
  const form = useForm<z.infer<typeof deadlineFormSchema>>({
    resolver: zodResolver(deadlineFormSchema),
    defaultValues: {
      title: '',
      description: '',
      priority: 'medium',
      type: '',
      reminderDays: 7,
      notes: '',
    },
  })

  // Effect to fetch deadlines on mount and when hook is ready
  useEffect(() => {
    if (isReady && caseId) {
      fetchDeadlines();
    } else if (fetchHookError) {
      toast({ variant: 'destructive', title: 'Auth Error', description: `Failed to initialize authenticated fetch: ${fetchHookError}` });
      setLoading(false);
    } else if (!caseId) {
       toast({ variant: 'destructive', title: 'Error', description: 'Case ID is missing.' });
       setLoading(false);
    }
  }, [caseId, isReady, fetchHookError]);

  // Fetch deadlines function
  const fetchDeadlines = async () => {
    setLoading(true)
    if (!isReady) {
      toast({ variant: 'destructive', title: 'Error', description: 'Authentication context not ready.' });
      setLoading(false);
      return;
    }
    try {
      const data = await authedFetch<{ deadlines: Deadline[] }>(`/api/cases/${caseId}/deadlines`);

      setDeadlines(data.deadlines || []);
    } catch (_error) {
      console.error('Fetch Deadlines Error:', error);
      const errorMsg = error instanceof HttpError
        ? `API Error (${error.status}): ${error.message}`
        : (error instanceof Error ? error.message : 'Failed to fetch deadlines');
      toast({ variant: 'destructive', title: 'Error', description: errorMsg });
    } finally {
      setLoading(false)
    }
  }

  // Load deadlines when component mounts or caseId changes
  useEffect(() => {
    if (caseId) {
      fetchDeadlines()
    }
  }, [caseId])

  // Format date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    })
  }

  // Calculate days from now
  const getDaysFromNow = (dateString: string) => {
    const now = new Date()
    now.setHours(0, 0, 0, 0) // Remove time portion for accurate day calculation

    const targetDate = new Date(dateString)
    targetDate.setHours(0, 0, 0, 0)

    const diffTime = targetDate.getTime() - now.getTime()
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))

    return diffDays
  }

  // Get deadline status text and color
  const getDeadlineStatus = (deadline: Deadline) => {
    if (deadline.completed) {
      return {
        text: 'Completed',
        component: <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200 flex items-center gap-1"><CheckCircle className="h-3 w-3" /> Completed</Badge>
      }
    }

    const daysLeft = getDaysFromNow(deadline.dueDate)

    if (daysLeft < 0) {
      return {
        text: 'Overdue',
        component: <Badge variant="outline" className="bg-red-50 text-red-700 border-red-200 flex items-center gap-1"><AlertCircle className="h-3 w-3" /> Overdue</Badge>
      }
    } else if (daysLeft === 0) {
      return {
        text: 'Due Today',
        component: <Badge variant="outline" className="bg-amber-50 text-amber-700 border-amber-200 flex items-center gap-1"><Clock className="h-3 w-3" /> Today</Badge>
      }
    } else if (daysLeft <= 7) {
      return {
        text: 'Upcoming',
        component: <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200 flex items-center gap-1"><Clock className="h-3 w-3" /> {daysLeft} days</Badge>
      }
    } else {
      return {
        text: 'Future',
        component: <Badge variant="outline" className="bg-gray-50 text-gray-700 border-gray-200">{daysLeft} days</Badge>
      }
    }
  }

  // Render priority indicator
  const renderPriority = (priority: string) => {
    switch(priority) {
      case 'high':
        return <div className="flex items-center"><div className="w-2 h-2 rounded-full bg-red-500 mr-2"></div>High</div>
      case 'medium':
        return <div className="flex items-center"><div className="w-2 h-2 rounded-full bg-yellow-500 mr-2"></div>Medium</div>
      case 'low':
        return <div className="flex items-center"><div className="w-2 h-2 rounded-full bg-blue-500 mr-2"></div>Low</div>
      default:
        return <div className="flex items-center"><div className="w-2 h-2 rounded-full bg-gray-500 mr-2"></div>Normal</div>
    }
  }

  // Handle opening add dialog
  const handleAddDeadline = () => {
    setSelectedDeadline(null)
    form.reset({
      title: '',
      description: '',
      dueDate: undefined,
      priority: 'medium',
      type: '',
      reminderDays: 7,
      notes: '',
    })
    setIsAddDialogOpen(true)
  }

  // Handle opening edit dialog
  const handleEditDeadline = (deadline: Deadline) => {
    setSelectedDeadline(deadline)
    form.reset({
      title: deadline.title,
      description: deadline.description || '',
      dueDate: new Date(deadline.dueDate),
      priority: deadline.priority,
      type: deadline.type,
      reminderDays: deadline.reminderDays,
      notes: deadline.notes || '',
    })
    setIsAddDialogOpen(true)
  }

  // Handle form submission
  const onSubmit = async (formData: z.infer<typeof deadlineFormSchema>) => {
    if (!isReady) {
      toast({ variant: 'destructive', title: 'Error', description: 'Authentication context not ready.' });
      return;
    }

    const method = selectedDeadline ? 'PUT' : 'POST'
    const url = selectedDeadline
      ? `/api/cases/${caseId}/deadlines/${selectedDeadline.id}`
      : `/api/cases/${caseId}/deadlines`

    const payload = {
      ...formData,
      dueDate: formData.dueDate.toISOString(), // Ensure date is ISO string
    };

    console.log(`Submitting deadline: Method=${method}, URL=${url}, Payload=`, payload);

    try {
      let result: DeadlineApiResponse;
      if (selectedDeadline) {
        // Update existing deadline
        result = await authedFetch<DeadlineApiResponse>(
          `/api/cases/${caseId}/deadlines/${selectedDeadline.id}`,
          {
            method: 'PUT',
            body: JSON.stringify(payload),
          }
        )
      } else {
        // Create new deadline
        result = await authedFetch<DeadlineApiResponse>(
          `/api/cases/${caseId}/deadlines`,
          {
            method: 'POST',
            body: JSON.stringify(payload),
          }
        )
      }

      // Check for API error in the result
      if (result.error) {
        throw new Error(result.error || `Failed to ${selectedDeadline ? 'update' : 'add'} deadline`);
      }

      toast({
        title: `Deadline ${selectedDeadline ? 'Updated' : 'Added'}`,
        description: `"${result.data?.title}" has been successfully ${selectedDeadline ? 'updated' : 'added'}.`,
        variant: 'success',
      })

      fetchDeadlines() // Refresh the list
      setIsAddDialogOpen(false)
      form.reset()
      setSelectedDeadline(null)
    } catch (error: unknown) { // Catch errors from authedFetch or thrown above
      console.error(`Error ${selectedDeadline ? 'updating' : 'adding'} deadline:`, error instanceof Error ? error.message : error)
      toast({
        title: `Error ${selectedDeadline ? 'updating' : 'adding'} deadline`,
        description: error instanceof Error ? error.message : 'An unexpected error occurred.',
        variant: 'destructive',
      })
    }
  }

  // Handle marking deadline as complete/incomplete
  const handleToggleComplete = async (deadline: Deadline) => {
    try {
      const newStatus = !deadline.completed;

      // API Call: Update completion status
      const result = await authedFetch<DeadlineApiResponse>(
        `/api/cases/${caseId}/deadlines/${deadline.id}`,
        {
          method: 'PUT',
          body: JSON.stringify({ completed: newStatus }),
        }
      )

      // Check for API error in the result
      if (result.error) {
        throw new Error(result.error || 'Failed to update deadline status');
      }

      toast({
        title: `Deadline Status Updated`,
        description: `"${deadline.title}" marked as ${newStatus ? 'complete' : 'incomplete'}.`,
        variant: 'success',
      })

      fetchDeadlines() // Refresh the list
    } catch (error: unknown) { // Catch errors from authedFetch or thrown above
      console.error('Error toggling deadline status:', error instanceof Error ? error.message : error)
      toast({
        title: 'Error Updating Status',
        description: error instanceof Error ? error.message : 'An unexpected error occurred.',
        variant: 'destructive',
      })
    }
  }

  // Handle deleting a deadline
  const handleDeleteDeadline = async (deadline: Deadline) => {
    setIsDeleting(true);
    try {
      // API Call: Delete
      const result = await authedFetch<DeleteApiResponse>(
        `/api/cases/${caseId}/deadlines/${deadline.id}`,
        { method: 'DELETE' }
      )

      // Check for API error in the result
      if (result.error) {
        throw new Error(result.error || 'Failed to delete deadline');
      }

      toast({
        title: 'Deadline Deleted',
        description: `"${deadline.title}" has been successfully deleted.`,
        variant: 'success',
      })

      fetchDeadlines() // Refresh the list
    } catch (error: unknown) { // Catch errors from authedFetch or thrown above
      console.error('Error deleting deadline:', error instanceof Error ? error.message : error)
      toast({
        title: 'Error Deleting Deadline',
        description: error instanceof Error ? error.message : 'An unexpected error occurred.',
        variant: 'destructive',
      })
    } finally {
      setIsDeleting(false);
    }
  }

  // Get deadline type label
  const getDeadlineTypeLabel = (typeId: string) => {
    const type = DEADLINE_TYPES.find(t => t.id === typeId)
    return type ? type.label : 'Unknown'
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader className="pb-3">
          <div className="flex justify-between items-center">
            <div>
              <CardTitle>Case Deadlines</CardTitle>
              <CardDescription>
                Important dates and deadlines for this case
              </CardDescription>
            </div>
            <Button onClick={handleAddDeadline}>
              <Plus className="mr-2 h-4 w-4" />
              Add Deadline
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Deadline</TableHead>
                <TableHead>Type</TableHead>
                <TableHead>Due Date</TableHead>
                <TableHead>Priority</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {loading ? (
                <TableRow>
                  <TableCell colSpan={6} className="text-center py-8">
                    <div className="flex justify-center items-center">
                      <Clock className="h-5 w-5 mr-2 animate-spin" />
                      Loading deadlines...
                    </div>
                  </TableCell>
                </TableRow>
              ) : deadlines.length > 0 ? (
                deadlines.map((deadline) => (
                  <TableRow
                    key={deadline.id}
                    className={deadline.completed ? 'opacity-60' : ''}
                  >
                    <TableCell>
                      <div className="font-medium">{deadline.title}</div>
                      {deadline.description && (
                        <div className="text-sm text-muted-foreground">{deadline.description}</div>
                      )}
                    </TableCell>
                    <TableCell>{getDeadlineTypeLabel(deadline.type)}</TableCell>
                    <TableCell>
                      <div className="flex items-center">
                        <Calendar className="h-4 w-4 mr-2 text-muted-foreground" />
                        {formatDate(deadline.dueDate)}
                      </div>
                    </TableCell>
                    <TableCell>{renderPriority(deadline.priority)}</TableCell>
                    <TableCell>{getDeadlineStatus(deadline).component}</TableCell>
                    <TableCell>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                            <span className="sr-only">Open menu</span>
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem onClick={() => handleEditDeadline(deadline)}>
                            <Pencil className="mr-2 h-4 w-4" />
                            Edit Deadline
                          </DropdownMenuItem>
                          <DropdownMenuItem onClick={() => handleToggleComplete(deadline)}>
                            {deadline.completed ? (
                              <>
                                <Clock className="mr-2 h-4 w-4" />
                                Mark as Incomplete
                              </>
                            ) : (
                              <>
                                <CheckCircle className="mr-2 h-4 w-4" />
                                Mark as Complete
                              </>
                            )}
                          </DropdownMenuItem>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem onClick={() => handleDeleteDeadline(deadline)}>
                            <Trash2 className="mr-2 h-4 w-4" />
                            Delete Deadline
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell colSpan={6} className="text-center py-8">
                    <p>No deadlines found for this case.</p>
                    <Button
                      variant="link"
                      className="mt-2"
                      onClick={handleAddDeadline}
                    >
                      Add your first deadline
                    </Button>
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* Add/Edit Deadline Dialog */}
      <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
        <DialogContent className="sm:max-w-[550px]">
          <DialogHeader>
            <DialogTitle>{selectedDeadline ? 'Edit Deadline' : 'Add New Deadline'}</DialogTitle>
            <DialogDescription>
              {selectedDeadline
                ? 'Update deadline details for this case'
                : 'Add a new deadline or important date for this case'}
            </DialogDescription>
          </DialogHeader>

          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
              <FormField
                control={form.control}
                name="title"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Title</FormLabel>
                    <FormControl>
                      <Input placeholder="e.g. Expert Witness Disclosure" {...field} />
                    </FormControl>
                    <FormDescription>
                      A clear title for the deadline
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <FormField
                  control={form.control}
                  name="dueDate"
                  render={({ field }) => (
                    <FormItem className="flex flex-col">
                      <FormLabel>Due Date</FormLabel>
                      <DatePicker
                        date={field.value}
                        setDate={field.onChange}
                      />
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="type"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Deadline Type</FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        value={field.value}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select type" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {DEADLINE_TYPES.map(type => (
                            <SelectItem key={type.id} value={type.id}>
                              {type.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <FormField
                  control={form.control}
                  name="priority"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Priority</FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        value={field.value}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select priority" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="low">Low</SelectItem>
                          <SelectItem value="medium">Medium</SelectItem>
                          <SelectItem value="high">High</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="reminderDays"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Reminder (Days Before)</FormLabel>
                      <FormControl>
                        <div className="flex items-center">
                          <Input
                            type="number"
                            min="0"
                            max="90"
                            {...field}
                          />
                          <Bell className="ml-2 h-4 w-4 text-muted-foreground" />
                        </div>
                      </FormControl>
                      <FormDescription>
                        Days before to send a reminder
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <FormField
                control={form.control}
                name="description"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Description</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Brief description of this deadline..."
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="notes"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Notes</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Any additional notes..."
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <DialogFooter>
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setIsAddDialogOpen(false)}
                >
                  Cancel
                </Button>
                <Button type="submit">
                  {selectedDeadline ? 'Update Deadline' : 'Add Deadline'}
                </Button>
              </DialogFooter>
            </form>
          </Form>
        </DialogContent>
      </Dialog>
    </div>
  )
}
