'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import {
  FileText,
  Search,
  Filter,
  Upload,
  Download,
  Link as LinkIcon,
  Clock,
  CheckCircle,
  AlertCircle,
  MoreHorizontal
} from 'lucide-react'

import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { useToast } from '@/components/ui/use-toast'
import { useAuthenticatedFetch, HttpError } from '@/hooks/useAuthenticatedFetch';
import { useUser } from '@/contexts/UserContext'; // Import useUser
import { useSupabase } from '@/lib/supabase/provider'; // Re-import useSupabase
import { Badge } from '@/components/ui/badge'; // Added missing import

// Document type options
const DOCUMENT_TYPES = [
  { id: "all", label: "All Types" },
  { id: "medical", label: "Medical Records" },
  { id: "insurance", label: "Insurance Documents" },
  { id: "police", label: "Police Reports" },
  { id: "legal", label: "Legal Documents" },
  { id: "photos", label: "Photos & Evidence" },
  { id: "correspondence", label: "Correspondence" },
  { id: "other", label: "Other Documents" }
];

interface CaseDocumentsProps {
  caseId: string;
  caseName: string;
}

// Interface for the document data
interface CaseDocument {
  id: string;
  title: string;
  type: string;
  size: number;
  uploadedAt: string;
  processingStatus: 'completed' | 'processing' | 'failed' | 'error';
  url: string;
}

// Interface for the API response structure
interface CaseDocumentsApiResponse {
  documents: CaseDocument[];
}

export function CaseDocuments({ caseId, caseName }: CaseDocumentsProps) {
  const router = useRouter()
  const { supabase } = useSupabase(); // Get supabase client
  const { user } = useUser(); // Get user
  const { toast } = useToast(); // Restore useToast
  const { authedFetch, isReady, error: fetchHookError } = useAuthenticatedFetch();

  // State
  const [documents, setDocuments] = useState<CaseDocument[]>([])
  const [searchQuery, setSearchQuery] = useState('')
  const [documentType, setDocumentType] = useState('all')
  const [loading, setLoading] = useState(true)

  // Sample mock data - would be replaced with real data from Supabase
  const mockCaseDocuments = [
    {
      id: 'doc1',
      title: 'Medical Records - Johnson',
      type: 'medical',
      size: 2458000, // bytes
      uploadedAt: '2025-04-01T10:30:00Z',
      processingStatus: 'completed',
      url: '#'
    },
    {
      id: 'doc2',
      title: 'Insurance Claim Form',
      type: 'insurance',
      size: 542000, // bytes
      uploadedAt: '2025-04-03T14:45:00Z',
      processingStatus: 'completed',
      url: '#'
    },
    {
      id: 'doc3',
      title: 'Police Report - Smith Accident',
      type: 'police',
      size: 1240000, // bytes
      uploadedAt: '2025-04-05T09:15:00Z',
      processingStatus: 'processing',
      url: '#'
    },
    {
      id: 'doc4',
      title: 'Settlement Offer Letter',
      type: 'legal',
      size: 320000, // bytes
      uploadedAt: '2025-04-06T11:20:00Z',
      processingStatus: 'completed',
      url: '#'
    }
  ];

  // Fetch documents function
  const fetchDocuments = async () => {
    setLoading(true)
    if (!isReady) {
      toast({ variant: 'destructive', title: 'Error', description: 'Authentication context not ready.' });
      setLoading(false);
      return;
    }
    try {
      // Construct URL with query parameters
      const url = new URL(`/api/cases/${caseId}/documents`, window.location.origin);
      if (documentType !== 'all') url.searchParams.append('type', documentType);
      if (searchQuery) url.searchParams.append('search', searchQuery);

      // Call the case documents API with filters using authedFetch
      const data = await authedFetch<CaseDocumentsApiResponse>(url.toString());

      setDocuments(data.documents || [])
    } catch (_error) {
      console.error('Error fetching documents:', error);
      const errorMsg = error instanceof HttpError
        ? `API Error (${error.status}): ${error.message}`
        : (error instanceof Error ? error.message : 'Failed to fetch documents');
      toast({ variant: 'destructive', title: 'Error', description: errorMsg });
      setDocuments([]) // Clear documents on error
    } finally {
      setLoading(false)
    }
  }

  // Effect to load documents when params change
  useEffect(() => {
    if (isReady && caseId) {
      fetchDocuments();
    } else if (fetchHookError) {
      toast({ variant: 'destructive', title: 'Auth Error', description: `Failed to initialize authenticated fetch: ${fetchHookError}` });
      setLoading(false);
    } else if (!caseId) {
       toast({ variant: 'destructive', title: 'Error', description: 'Case ID is missing.' });
       setLoading(false);
    }
  }, [caseId, searchQuery, documentType, isReady, fetchHookError]);

  // Format file size
  const formatFileSize = (bytes: number): string => {
    if (bytes < 1024) return bytes + ' B';
    if (bytes < 1024 * 1024) return (bytes / 1024).toFixed(1) + ' KB';
    if (bytes < 1024 * 1024 * 1024) return (bytes / (1024 * 1024)).toFixed(1) + ' MB';
    return (bytes / (1024 * 1024 * 1024)).toFixed(1) + ' GB';
  }

  // Format date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    })
  }

  // Render status badge
  const renderStatusBadge = (status: string) => {
    switch(status) {
      case 'completed':
        return <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200 flex items-center gap-1"><CheckCircle className="h-3 w-3" /> Processed</Badge>
      case 'processing':
        return <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200 flex items-center gap-1"><Clock className="h-3 w-3" /> Processing</Badge>
      case 'error':
        return <Badge variant="outline" className="bg-red-50 text-red-700 border-red-200 flex items-center gap-1"><AlertCircle className="h-3 w-3" /> Failed</Badge>
      default:
        return <Badge variant="outline">Unknown</Badge>
    }
  }

  // Handle upload for this case
  const handleUploadForCase = () => {
    router.push(`/document-center/upload?caseId=${caseId}&caseName=${encodeURIComponent(caseName)}`)
  }

  // Handle search
  const handleSearch = () => {
    // This will trigger the useEffect
  }

  // Handle download document
  const handleDownloadDocument = async (documentId: string) => {
    try {
      const response = await fetch(`/api/documents/${documentId}/download`)

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to get download URL')
      }

      const { downloadUrl } = await response.json()
      window.open(downloadUrl, '_blank')
    } catch (error: unknown) {
      console.error('Error downloading document:', error)
      toast({
        title: 'Download failed',
        description: error instanceof Error ? error.message : 'Could not download the document',
        variant: 'destructive'
      })
    }
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader className="pb-3">
          <div className="flex justify-between items-center">
            <div>
              <CardTitle>Case Documents</CardTitle>
              <CardDescription>
                Documents linked to this case
              </CardDescription>
            </div>
            <Button onClick={handleUploadForCase}>
              <Upload className="mr-2 h-4 w-4" />
              Upload Document
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          {/* Search and filters */}
          <div className="flex flex-col md:flex-row gap-4 mb-6">
            <div className="relative flex-1">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search documents..."
                className="pl-8"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                onKeyDown={(e) => e.key === 'Enter' && handleSearch()}
              />
            </div>

            <div className="w-full md:w-[200px]">
              <Select value={documentType} onValueChange={setDocumentType}>
                <SelectTrigger>
                  <SelectValue placeholder="Document Type" />
                </SelectTrigger>
                <SelectContent>
                  {DOCUMENT_TYPES.map(type => (
                    <SelectItem key={type.id} value={type.id}>
                      {type.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Documents Table */}
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Document</TableHead>
                <TableHead>Type</TableHead>
                <TableHead>Upload Date</TableHead>
                <TableHead>Size</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {loading ? (
                <TableRow>
                  <TableCell colSpan={6} className="text-center py-8">
                    <div className="flex justify-center items-center">
                      <Clock className="h-5 w-5 mr-2 animate-spin" />
                      Loading documents...
                    </div>
                  </TableCell>
                </TableRow>
              ) : documents.length > 0 ? (
                documents.map((doc) => (
                  <TableRow
                    key={doc.id}
                    className="cursor-pointer"
                    onClick={() => router.push(`/document-center/library/${doc.id}`)}
                  >
                    <TableCell>
                      <div className="flex items-center">
                        <FileText className="h-4 w-4 mr-2 text-muted-foreground" />
                        <span className="font-medium">{doc.title}</span>
                      </div>
                    </TableCell>
                    <TableCell>
                      <span className="capitalize">{doc.type}</span>
                    </TableCell>
                    <TableCell>{formatDate(doc.uploadedAt)}</TableCell>
                    <TableCell>{formatFileSize(doc.size)}</TableCell>
                    <TableCell>{renderStatusBadge(doc.processingStatus)}</TableCell>
                    <TableCell>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="sm" className="h-8 w-8 p-0" onClick={(e) => e.stopPropagation()}>
                            <span className="sr-only">Open menu</span>
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent onClick={(e) => e.stopPropagation()} align="end">
                          <DropdownMenuItem onClick={() => router.push(`/document-center/library/${doc.id}`)}>
                            <FileText className="mr-2 h-4 w-4" />
                            View Document
                          </DropdownMenuItem>
                          <DropdownMenuItem onClick={(e) => {
                            e.stopPropagation()
                            handleDownloadDocument(doc.id)
                          }}>
                            <Download className="mr-2 h-4 w-4" />
                            Download
                          </DropdownMenuItem>
                          {doc.processingStatus === 'error' && (
                            <>
                              <DropdownMenuSeparator />
                              <DropdownMenuItem onClick={() => console.log('Retry processing for', doc.id)}>
                                <Clock className="mr-2 h-4 w-4" />
                                Retry Processing
                              </DropdownMenuItem>
                            </>
                          )}
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell colSpan={6} className="text-center py-8">
                    <p>No documents found for this case.</p>
                    <Button
                      variant="link"
                      className="mt-2"
                      onClick={handleUploadForCase}
                    >
                      Upload your first document
                    </Button>
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </CardContent>
        <CardFooter className="border-t pt-5 px-6">
          <Button
            variant="outline"
            onClick={() => router.push('/document-center/library')}
          >
            View All Documents
          </Button>
        </CardFooter>
      </Card>
    </div>
  )
}
