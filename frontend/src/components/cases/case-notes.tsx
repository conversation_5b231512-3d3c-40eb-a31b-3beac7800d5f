'use client'

import React, { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import {
  StickyNote,
  Plus,
  Clock,
  Search,
  MoreHorizontal,
  Pencil,
  Trash2,
  CalendarDays,
  User,
  Tag as TagIcon
} from 'lucide-react'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import { Textarea } from '@/components/ui/textarea'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { useToast } from '@/components/ui/use-toast'
import { useSupabase } from '@/lib/supabase/provider'
import { useAuthenticatedFetch, HttpError } from '@/hooks/useAuthenticatedFetch'
import { zodResolver } from '@hookform/resolvers/zod'
import { useForm } from 'react-hook-form'
import * as z from 'zod'

// Note form schema
const noteFormSchema = z.object({
  title: z.string().min(3, {
    message: "Title must be at least 3 characters.",
  }),
  content: z.string().min(1, {
    message: "Note content is required",
  }),
  category: z.string().min(1, {
    message: "Please select a category",
  }),
  tags: z.string().optional(),
});

// Note categories
const NOTE_CATEGORIES = [
  { id: 'client-interview', label: 'Client Interview' },
  { id: 'legal-research', label: 'Legal Research' },
  { id: 'case-strategy', label: 'Case Strategy' },
  { id: 'settlement', label: 'Settlement Discussions' },
  { id: 'hearing', label: 'Hearing Notes' },
  { id: 'deposition', label: 'Deposition Notes' },
  { id: 'evidence', label: 'Evidence Analysis' },
  { id: 'witness', label: 'Witness Notes' },
  { id: 'general', label: 'General Notes' },
];

// Note interface
interface Note {
  id: string;
  title: string;
  content: string;
  category: string;
  tags?: string;
  created_at: string;
  updated_at: string;
  created_by: {
    id: string;
    email: string;
    first_name?: string;
    last_name?: string;
  };
}

interface CaseNotesProps {
  caseId: string;
  caseName: string;
}

export function CaseNotes({ caseId, caseName }: CaseNotesProps): React.ReactNode {
  const router = useRouter()
  const { supabase } = useSupabase()
  const { toast } = useToast()
  const { authedFetch, isReady, error: fetchHookError } = useAuthenticatedFetch()

  // State
  const [notes, setNotes] = useState<Note[]>([])
  const [searchQuery, setSearchQuery] = useState('')
  const [categoryFilter, setCategoryFilter] = useState('all')
  const [loading, setLoading] = useState(true)
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false)
  const [selectedNote, setSelectedNote] = useState<Note | null>(null)
  const [isDeleting, setIsDeleting] = useState(false)

  // Form for adding/editing notes
  const form = useForm<z.infer<typeof noteFormSchema>>({
    resolver: zodResolver(noteFormSchema),
    defaultValues: {
      title: '',
      content: '',
      category: '',
      tags: '',
    },
  })

  // Effect to fetch notes on mount and filter changes
  useEffect(() => {
    if (isReady && caseId) {
      fetchNotes()
    } else if (fetchHookError) {
      toast({ variant: 'destructive', title: 'Auth Error', description: `Failed to initialize authenticated fetch: ${fetchHookError}` })
      setLoading(false)
    } else if (!caseId) {
      toast({ variant: 'destructive', title: 'Error', description: 'Case ID is missing.' })
      setLoading(false)
    }
  }, [caseId, searchQuery, categoryFilter, isReady, fetchHookError])

  // Fetch notes function
  const fetchNotes = async () => {
    setLoading(true)
    if (!isReady) {
      toast({ variant: 'destructive', title: 'Error', description: 'Authentication context not ready.' })
      setLoading(false)
      return
    }
    try {
      const url = new URL(`/api/cases/${caseId}/notes`, window.location.origin)
      if (categoryFilter !== 'all') url.searchParams.append('category', categoryFilter)
      if (searchQuery) url.searchParams.append('search', searchQuery)

      const data = await authedFetch<{ notes: Note[] }>(url.toString())

      setNotes(data.notes || [])
    } catch (_error) {
      console.error('Fetch Notes Error:', error)
      const errorMsg = error instanceof HttpError
        ? `API Error (${error.status}): ${error.message}`
        : (error instanceof Error ? error.message : 'Failed to fetch notes')
      toast({ variant: 'destructive', title: 'Error', description: errorMsg })
      setNotes([]) // Clear notes on error
    } finally {
      setLoading(false)
    }
  }

  // Format date
  const formatDate = (dateString: string): string => {
    const date = new Date(dateString)
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    })
  }

  // Format time
  const formatTime = (dateString: string): string => {
    const date = new Date(dateString)
    return date.toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit',
    })
  }

  // Get category label
  const getCategoryLabel = (categoryId: string): string => {
    const category = NOTE_CATEGORIES.find(c => c.id === categoryId)
    return category ? category.label : 'Unknown'
  }

  // Render tags
  const renderTags = (tagString?: string): React.ReactNode => {
    if (!tagString) return null

    const tags = tagString.split(',')
    return (
      <div className="flex flex-wrap gap-1 mt-2">
        {tags.map((tag, index) => (
          <Badge key={index} variant="outline" className="bg-slate-50">
            {tag.trim()}
          </Badge>
        ))}
      </div>
    )
  }

  // Handle opening add dialog
  const handleAddNote = (): void => {
    setSelectedNote(null)
    form.reset({
      title: '',
      content: '',
      category: '',
      tags: '',
    })
    setIsAddDialogOpen(true)
  }

  // Handle opening edit dialog
  const handleEditNote = (note: Note): void => {
    setSelectedNote(note)
    form.reset({
      title: note.title,
      content: note.content,
      category: note.category,
      tags: note.tags || '',
    })
    setIsAddDialogOpen(true)
  }

  // Handle form submission
  const onSubmit = async (formData: z.infer<typeof noteFormSchema>) => {
    if (!isReady) {
      toast({ variant: 'destructive', title: 'Error', description: 'Authentication context not ready.' })
      return
    }

    const method = selectedNote ? 'PUT' : 'POST'
    const url = selectedNote
      ? `/api/cases/${caseId}/notes/${selectedNote.id}`
      : `/api/cases/${caseId}/notes`

    console.log(`Submitting note: Method=${method}, URL=${url}, Payload=`, formData)

    try {
      const data = await authedFetch<any>(url, {
        method: method,
        body: JSON.stringify(formData),
      })

      console.log('API Response:', data)

      if (selectedNote) {
        // Edit existing note
        toast({
          title: 'Note updated',
          description: 'The note has been successfully updated.'
        })
      } else {
        // Create new note
        toast({
          title: 'Note created',
          description: 'A new note has been successfully created.'
        })
      }

      // Fetch updated notes list
      await fetchNotes()

      // Close dialog and reset form
      setIsAddDialogOpen(false)
      setSelectedNote(null)
      form.reset()
    } catch (error: unknown) {
      console.error('Error saving note:', error);
      const errorMsg = error instanceof HttpError
        ? `API Error (${error.status}): ${error.message}`
        : (error instanceof Error ? error.message : 'Failed to save note');
      toast({
        title: 'Error',
        description: errorMsg,
        variant: 'destructive'
      })
    }
  }

  // Handle deleting a note
  const handleDeleteNote = async (note: Note) => {
    if (!isReady) {
      toast({ variant: 'destructive', title: 'Error', description: 'Authentication context not ready.' })
      return
    }
    setIsDeleting(true)
    try {
      // Optimistic UI update
      setNotes(prev => prev.filter(n => n.id !== note.id))

      // Make API call to delete the note
      const data = await authedFetch<any>(`/api/cases/${caseId}/notes/${note.id}`, {
        method: 'DELETE',
      })

      console.log('Delete Response:', data)

      toast({
        title: 'Note deleted',
        description: 'The note has been successfully deleted.'
      })
    } catch (error: unknown) {
      console.error('Error deleting note:', error);
      const errorMsg = error instanceof HttpError
        ? `API Error (${error.status}): ${error.message}`
        : (error instanceof Error ? error.message : 'Failed to delete note');
      toast({
        title: 'Error',
        description: errorMsg,
        variant: 'destructive'
      })
    } finally {
      setIsDeleting(false)
    }
  }

  return (
    <div className="space-y-6">
      {/* Header with search and filters */}
      <div className="flex flex-col md:flex-row justify-between gap-4">
        <div className="relative w-full md:w-2/3">
          <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search notes..."
            className="pl-8"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>
        <div className="flex gap-2">
          <Select
            value={categoryFilter}
            onValueChange={(value) => setCategoryFilter(value)}
          >
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="All Categories" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Categories</SelectItem>
              {NOTE_CATEGORIES.map((category) => (
                <SelectItem key={category.id} value={category.id}>
                  {category.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          <Button onClick={handleAddNote}>
            <Plus className="mr-2 h-4 w-4" />
            Add Note
          </Button>
        </div>
      </div>

      {/* Notes List */}
      <div className="space-y-4">
        {loading ? (
          <div className="py-10 text-center">
            <div className="animate-spin inline-block h-8 w-8 border-4 border-t-primary border-r-transparent border-l-transparent border-b-transparent rounded-full mb-2"></div>
            <p className="text-muted-foreground">Loading notes...</p>
          </div>
        ) : notes.length === 0 ? (
          <div className="py-10 text-center border rounded-lg">
            <StickyNote className="h-12 w-12 mx-auto text-muted-foreground mb-2" />
            <h3 className="text-lg font-medium mb-1">No notes found</h3>
            <p className="text-muted-foreground mb-4">
              {searchQuery || categoryFilter !== 'all'
                ? 'Try changing your search criteria'
                : `There are no notes for this case yet`}
            </p>
            <Button onClick={handleAddNote} variant="outline">
              <Plus className="mr-2 h-4 w-4" />
              Add your first note
            </Button>
          </div>
        ) : (
          notes.map((note) => (
            <Card key={note.id}>
              <CardHeader className="pb-2">
                <div className="flex justify-between items-start">
                  <div>
                    <Badge className="mb-2" variant="secondary">
                      {getCategoryLabel(note.category)}
                    </Badge>
                    <CardTitle className="text-xl">{note.title}</CardTitle>
                  </div>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="icon">
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem onClick={() => handleEditNote(note)}>
                        <Pencil className="mr-2 h-4 w-4" />
                        Edit Note
                      </DropdownMenuItem>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem
                        onClick={() => handleDeleteNote(note)}
                        className="text-destructive focus:text-destructive"
                      >
                        <Trash2 className="mr-2 h-4 w-4" />
                        Delete Note
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              </CardHeader>
              <CardContent>
                <p className="whitespace-pre-wrap">{note.content}</p>
                {renderTags(note.tags)}
              </CardContent>
              <CardFooter className="pt-2 text-xs text-muted-foreground">
                <div className="flex items-center gap-4">
                  <div className="flex items-center">
                    <CalendarDays className="mr-1 h-3 w-3" />
                    {formatDate(note.created_at)} at {formatTime(note.created_at)}
                  </div>
                  <div className="flex items-center">
                    <User className="mr-1 h-3 w-3" />
                    {note.created_by?.first_name} {note.created_by?.last_name}
                  </div>
                </div>
              </CardFooter>
            </Card>
          ))
        )}
      </div>

      {/* Add/Edit Note Dialog */}
      <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
        <DialogContent className="sm:max-w-[550px]">
          <DialogHeader>
            <DialogTitle>{selectedNote ? 'Edit Note' : 'Add New Note'}</DialogTitle>
            <DialogDescription>
              {selectedNote
                ? 'Update note details for this case'
                : 'Add a new note or memo for this case'}
            </DialogDescription>
          </DialogHeader>

          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
              <FormField
                control={form.control}
                name="title"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Title</FormLabel>
                    <FormControl>
                      <Input placeholder="Note title" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="category"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Category</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      value={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select category" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {NOTE_CATEGORIES.map(category => (
                          <SelectItem key={category.id} value={category.id}>
                            {category.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="content"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Content</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Enter note content here..."
                        className="min-h-[200px]"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="tags"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Tags</FormLabel>
                    <FormControl>
                      <div className="flex items-center">
                        <TagIcon className="h-4 w-4 mr-2 text-muted-foreground" />
                        <Input
                          placeholder="interview,medical,strategy (comma separated)"
                          {...field}
                        />
                      </div>
                    </FormControl>
                    <FormDescription>
                      Optional. Separate tags with commas.
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <DialogFooter>
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setIsAddDialogOpen(false)}
                >
                  Cancel
                </Button>
                <Button type="submit">
                  {selectedNote ? 'Update Note' : 'Add Note'}
                </Button>
              </DialogFooter>
            </form>
          </Form>
        </DialogContent>
      </Dialog>
    </div>
  )
}
