'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { RefreshCw, Plus, Search, Phone, Settings } from 'lucide-react';
import { MultiTenantTable, TableColumn } from '@/components/ui/MultiTenantTable';
import { useAuthenticatedFetch } from '@/hooks/useAuthenticatedFetch';
import { useToast } from '@/components/ui/use-toast';

export interface TelnyxNumber {
  id: string;
  tenant_id: string;
  phone_number: string;
  status: 'active' | 'pending' | 'suspended' | 'cancelled';
  country_code: string;
  number_type: 'local' | 'toll-free' | 'mobile';
  monthly_cost: number;
  features: string[];
  forwarding_number?: string;
  webhook_url?: string;
  created_at: string;
  purchased_at?: string;
}

export interface AvailableNumber {
  phone_number: string;
  country_code: string;
  number_type: 'local' | 'toll-free' | 'mobile';
  monthly_cost: number;
  features: string[];
  locality?: string;
  region?: string;
}

interface TelnyxNumbersTableProps {
  showTenantColumn?: boolean;
}

export default function TelnyxNumbersTable({ showTenantColumn = false }: TelnyxNumbersTableProps) {
  const [numbers, setNumbers] = useState<TelnyxNumber[]>([]);
  const [availableNumbers, setAvailableNumbers] = useState<AvailableNumber[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isSearching, setIsSearching] = useState(false);
  const [isPurchasing, setIsPurchasing] = useState(false);
  const [searchAreaCode, setSearchAreaCode] = useState('');
  const [searchCountry, setSearchCountry] = useState('US');
  const [showSearchModal, setShowSearchModal] = useState(false);
  const [showPurchaseModal, setShowPurchaseModal] = useState(false);
  const [selectedNumber, setSelectedNumber] = useState<AvailableNumber | null>(null);
  const [forwardingNumber, setForwardingNumber] = useState('');
  
  const { authedFetch, isReady } = useAuthenticatedFetch();
  const { toast } = useToast();

  const columns: TableColumn<TelnyxNumber>[] = [
    {
      key: 'phone_number',
      header: 'Phone Number',
      className: 'font-mono font-medium',
    },
    {
      key: 'status',
      header: 'Status',
      render: (value) => {
        const statusColors = {
          active: 'bg-green-100 text-green-800',
          pending: 'bg-yellow-100 text-yellow-800',
          suspended: 'bg-red-100 text-red-800',
          cancelled: 'bg-gray-100 text-gray-800',
        };
        return (
          <Badge className={statusColors[value as keyof typeof statusColors] || 'bg-gray-100 text-gray-800'}>
            {value}
          </Badge>
        );
      },
    },
    {
      key: 'number_type',
      header: 'Type',
      render: (value) => <span className="capitalize">{value.replace('-', ' ')}</span>,
    },
    {
      key: 'monthly_cost',
      header: 'Monthly Cost',
      render: (value) => `$${(value / 100).toFixed(2)}`,
    },
    {
      key: 'forwarding_number',
      header: 'Forwarding To',
      className: 'font-mono',
      render: (value) => value || '-',
    },
    {
      key: 'actions',
      header: 'Actions',
      render: (_, row) => (
        <div className="flex gap-2">
          <Button size="sm" variant="outline" onClick={() => handleConfigureNumber(row)}>
            <Settings className="h-3 w-3 mr-1" />
            Configure
          </Button>
        </div>
      ),
    },
  ];

  const availableColumns: TableColumn<AvailableNumber>[] = [
    {
      key: 'phone_number',
      header: 'Phone Number',
      className: 'font-mono font-medium',
    },
    {
      key: 'number_type',
      header: 'Type',
      render: (value) => <span className="capitalize">{value.replace('-', ' ')}</span>,
    },
    {
      key: 'locality',
      header: 'Location',
      render: (value, row) => `${value || ''} ${row.region || ''}`.trim() || '-',
    },
    {
      key: 'monthly_cost',
      header: 'Monthly Cost',
      render: (value) => `$${(value / 100).toFixed(2)}`,
    },
    {
      key: 'actions',
      header: 'Actions',
      render: (_, row) => (
        <Button size="sm" onClick={() => handleSelectNumber(row)}>
          <Plus className="h-3 w-3 mr-1" />
          Purchase
        </Button>
      ),
    },
  ];

  const handleConfigureNumber = (number: TelnyxNumber) => {
    // TODO: Implement number configuration modal
    toast({
      title: 'Configure Number',
      description: `Configuration for ${number.phone_number} coming soon`,
    });
  };

  const handleSelectNumber = (number: AvailableNumber) => {
    setSelectedNumber(number);
    setShowPurchaseModal(true);
    setShowSearchModal(false);
  };

  const handleSearchNumbers = async () => {
    if (!isReady) return;

    try {
      setIsSearching(true);
      const response = await authedFetch<{ numbers: AvailableNumber[] }>('/api/voice/numbers/search', {
        method: 'POST',
        body: JSON.stringify({
          country_code: searchCountry,
          area_code: searchAreaCode || undefined,
          limit: 20,
        }),
      });
      setAvailableNumbers(response.numbers || []);
    } catch (_error) {
      console.error('Error searching numbers:', error);
      toast({
        variant: 'destructive',
        title: 'Error',
        description: 'Failed to search available numbers',
      });
    } finally {
      setIsSearching(false);
    }
  };

  const handlePurchaseNumber = async () => {
    if (!selectedNumber || !isReady) return;

    try {
      setIsPurchasing(true);
      await authedFetch('/api/voice/numbers/purchase', {
        method: 'POST',
        body: JSON.stringify({
          phone_number: selectedNumber.phone_number,
          forwarding_number: forwardingNumber || undefined,
        }),
      });
      
      toast({
        title: 'Success',
        description: `Successfully purchased ${selectedNumber.phone_number}`,
      });
      
      setShowPurchaseModal(false);
      setSelectedNumber(null);
      setForwardingNumber('');
      fetchNumbers();
    } catch (_error) {
      console.error('Error purchasing number:', error);
      toast({
        variant: 'destructive',
        title: 'Error',
        description: 'Failed to purchase number',
      });
    } finally {
      setIsPurchasing(false);
    }
  };

  const fetchNumbers = async () => {
    if (!isReady) return;

    try {
      setIsLoading(true);
      const response = await authedFetch<{ numbers: TelnyxNumber[] }>('/api/voice/numbers');
      setNumbers(response.numbers || []);
    } catch (_error) {
      console.error('Error fetching numbers:', error);
      toast({
        variant: 'destructive',
        title: 'Error',
        description: 'Failed to fetch phone numbers',
      });
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchNumbers();
  }, [isReady]);

  return (
    <>
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Phone Numbers</CardTitle>
              <CardDescription>
                Manage your Telnyx phone numbers and forwarding settings
              </CardDescription>
            </div>
            <div className="flex gap-2">
              <Dialog open={showSearchModal} onOpenChange={setShowSearchModal}>
                <DialogTrigger asChild>
                  <Button>
                    <Plus className="h-4 w-4 mr-2" />
                    Purchase Number
                  </Button>
                </DialogTrigger>
                <DialogContent className="max-w-4xl">
                  <DialogHeader>
                    <DialogTitle>Search Available Numbers</DialogTitle>
                    <DialogDescription>
                      Search for available phone numbers to purchase
                    </DialogDescription>
                  </DialogHeader>
                  <div className="space-y-4">
                    <div className="grid grid-cols-3 gap-4">
                      <div>
                        <Label htmlFor="country">Country</Label>
                        <Input
                          id="country"
                          value={searchCountry}
                          onChange={(e) => setSearchCountry(e.target.value)}
                          placeholder="US"
                        />
                      </div>
                      <div>
                        <Label htmlFor="area-code">Area Code (Optional)</Label>
                        <Input
                          id="area-code"
                          value={searchAreaCode}
                          onChange={(e) => setSearchAreaCode(e.target.value)}
                          placeholder="212"
                        />
                      </div>
                      <div className="flex items-end">
                        <Button onClick={handleSearchNumbers} disabled={isSearching} className="w-full">
                          <Search className={`h-4 w-4 mr-2 ${isSearching ? 'animate-spin' : ''}`} />
                          Search
                        </Button>
                      </div>
                    </div>
                    
                    {availableNumbers.length > 0 && (
                      <MultiTenantTable
                        rows={availableNumbers}
                        columns={availableColumns}
                        showTenantColumn={false}
                        emptyMessage="No available numbers found"
                      />
                    )}
                  </div>
                </DialogContent>
              </Dialog>
              
              <Button onClick={fetchNumbers} disabled={isLoading} variant="outline">
                <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
                Refresh
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <MultiTenantTable
            rows={numbers}
            columns={columns}
            showTenantColumn={showTenantColumn}
            isLoading={isLoading}
            emptyMessage="No phone numbers found. Purchase your first number to get started."
          />
        </CardContent>
      </Card>

      {/* Purchase Confirmation Modal */}
      <Dialog open={showPurchaseModal} onOpenChange={setShowPurchaseModal}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Purchase Phone Number</DialogTitle>
            <DialogDescription>
              Configure and purchase {selectedNumber?.phone_number}
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <Label htmlFor="forwarding">Forwarding Number (Optional)</Label>
              <Input
                id="forwarding"
                value={forwardingNumber}
                onChange={(e) => setForwardingNumber(e.target.value)}
                placeholder="+1234567890"
              />
            </div>
            <div className="flex justify-end gap-2">
              <Button variant="outline" onClick={() => setShowPurchaseModal(false)}>
                Cancel
              </Button>
              <Button onClick={handlePurchaseNumber} disabled={isPurchasing}>
                {isPurchasing ? 'Purchasing...' : `Purchase for $${((selectedNumber?.monthly_cost || 0) / 100).toFixed(2)}/month`}
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </>
  );
}
