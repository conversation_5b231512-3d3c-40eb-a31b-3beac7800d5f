'use client';

import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { 
  Clock, 
  CheckCircle, 
  XCircle, 
  Activity, 
  Trash2, 
  RefreshCw,
  AlertTriangle 
} from 'lucide-react';
import { useQueueStats } from '@/hooks/useQueueStats';
import { formatDistanceToNow } from 'date-fns';

interface QueueStatusWidgetProps {
  className?: string;
}

export default function QueueStatusWidget({ className }: QueueStatusWidgetProps) {
  const { data: stats, isLoading, error, mutate, clearQueue } = useQueueStats();

  const handleClearQueue = async () => {
    try {
      await clearQueue();
    } catch (_err) {
      // Error is already handled in the hook with toast
      console.error('Failed to clear queue:', err);
    }
  };

  const handleRefresh = async () => {
    try {
      await mutate();
    } catch (_err) {
      console.error('Failed to refresh queue stats:', err);
    }
  };

  const getWorkerStatusBadge = (status: string) => {
    switch (status) {
      case 'healthy':
        return <Badge className="bg-green-500 text-white">Healthy</Badge>;
      case 'degraded':
        return <Badge className="bg-yellow-500 text-white">Degraded</Badge>;
      case 'down':
        return <Badge className="bg-red-500 text-white">Down</Badge>;
      default:
        return <Badge variant="outline">Unknown</Badge>;
    }
  };

  const formatProcessingTime = (seconds: number) => {
    if (seconds < 60) {
      return `${seconds.toFixed(1)}s`;
    }
    return `${(seconds / 60).toFixed(1)}m`;
  };

  if (isLoading) {
    return (
      <Card className={className}>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Call Queue Status</CardTitle>
          <Activity className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center h-32">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className={className}>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Call Queue Status</CardTitle>
          <AlertTriangle className="h-4 w-4 text-red-500" />
        </CardHeader>
        <CardContent>
          <div className="text-center py-4">
            <p className="text-sm text-muted-foreground mb-2">Failed to load queue status</p>
            <Button onClick={handleRefresh} size="sm" variant="outline">
              <RefreshCw className="h-3 w-3 mr-1" />
              Retry
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!stats) {
    return (
      <Card className={className}>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Call Queue Status</CardTitle>
          <Activity className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-center py-4">
            <p className="text-sm text-muted-foreground">No queue data available</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  const totalActive = stats.queued + stats.processing;
  const totalProcessed = stats.completed + stats.failed;
  const successRate = totalProcessed > 0 ? (stats.completed / totalProcessed) * 100 : 0;

  return (
    <Card className={className}>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <div>
          <CardTitle className="text-sm font-medium">Call Queue Status</CardTitle>
          <CardDescription className="text-xs">
            Last updated {stats.last_updated ? formatDistanceToNow(new Date(stats.last_updated), { addSuffix: true }) : 'never'}
          </CardDescription>
        </div>
        <div className="flex items-center gap-2">
          {getWorkerStatusBadge(stats.worker_status)}
          <Button onClick={handleRefresh} size="sm" variant="ghost">
            <RefreshCw className="h-3 w-3" />
          </Button>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Current Status */}
        <div className="grid grid-cols-2 gap-4">
          <div className="space-y-1">
            <div className="flex items-center gap-2">
              <Clock className="h-4 w-4 text-blue-500" />
              <span className="text-sm font-medium">Queued</span>
            </div>
            <div className="text-2xl font-bold text-blue-600">{stats.queued}</div>
          </div>
          <div className="space-y-1">
            <div className="flex items-center gap-2">
              <Activity className="h-4 w-4 text-orange-500" />
              <span className="text-sm font-medium">Processing</span>
            </div>
            <div className="text-2xl font-bold text-orange-600">{stats.processing}</div>
          </div>
        </div>

        {/* Today's Summary */}
        <div className="space-y-2">
          <div className="flex items-center justify-between text-sm">
            <span className="text-muted-foreground">Today's Progress</span>
            <span className="font-medium">{stats.total_today} total</span>
          </div>
          <Progress value={successRate} className="h-2" />
          <div className="grid grid-cols-2 gap-4 text-xs">
            <div className="flex items-center gap-1">
              <CheckCircle className="h-3 w-3 text-green-500" />
              <span>{stats.completed} completed</span>
            </div>
            <div className="flex items-center gap-1">
              <XCircle className="h-3 w-3 text-red-500" />
              <span>{stats.failed} failed</span>
            </div>
          </div>
        </div>

        {/* Performance Metrics */}
        {stats.avg_processing_time_seconds > 0 && (
          <div className="text-xs text-muted-foreground">
            Avg processing time: {formatProcessingTime(stats.avg_processing_time_seconds)}
          </div>
        )}

        {/* Actions */}
        <div className="flex gap-2 pt-2">
          <Button
            onClick={handleClearQueue}
            disabled={stats.queued === 0}
            size="sm"
            variant="outline"
            className="flex-1"
          >
            <Trash2 className="h-3 w-3 mr-1" />
            Clear Queue ({stats.queued})
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
