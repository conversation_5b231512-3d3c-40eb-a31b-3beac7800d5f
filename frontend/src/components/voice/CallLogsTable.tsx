'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { RefreshCw, Phone, PhoneIncoming, PhoneOutgoing } from 'lucide-react';
import { MultiTenantTable, TableColumn } from '@/components/ui/MultiTenantTable';
import { useAuthenticatedFetch } from '@/hooks/useAuthenticatedFetch';
import { useToast } from '@/components/ui/use-toast';
import { formatDistanceToNow } from 'date-fns';

export interface CallLog {
  id: string;
  tenant_id: string;
  call_id: string;
  direction: 'inbound' | 'outbound';
  from_number: string;
  to_number: string;
  status: 'completed' | 'failed' | 'busy' | 'no-answer' | 'in-progress';
  duration_seconds: number;
  start_time: string;
  end_time?: string;
  recording_url?: string;
  transcript?: string;
  metadata?: Record<string, any>;
  created_at: string;
}

interface CallLogsTableProps {
  showTenantColumn?: boolean;
}

export default function CallLogsTable({ showTenantColumn = false }: CallLogsTableProps) {
  const [callLogs, setCallLogs] = useState<CallLog[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const { authedFetch, isReady } = useAuthenticatedFetch();
  const { toast } = useToast();

  const columns: TableColumn<CallLog>[] = [
    {
      key: 'direction',
      header: 'Direction',
      render: (value, row) => (
        <div className="flex items-center gap-2">
          {value === 'inbound' ? (
            <PhoneIncoming className="h-4 w-4 text-green-600" />
          ) : (
            <PhoneOutgoing className="h-4 w-4 text-blue-600" />
          )}
          <span className="capitalize">{value}</span>
        </div>
      ),
    },
    {
      key: 'from_number',
      header: 'From',
      className: 'font-mono',
    },
    {
      key: 'to_number',
      header: 'To',
      className: 'font-mono',
    },
    {
      key: 'status',
      header: 'Status',
      render: (value) => {
        const statusColors = {
          completed: 'bg-green-100 text-green-800',
          failed: 'bg-red-100 text-red-800',
          busy: 'bg-yellow-100 text-yellow-800',
          'no-answer': 'bg-gray-100 text-gray-800',
          'in-progress': 'bg-blue-100 text-blue-800',
        };
        return (
          <Badge className={statusColors[value as keyof typeof statusColors] || 'bg-gray-100 text-gray-800'}>
            {value}
          </Badge>
        );
      },
    },
    {
      key: 'duration_seconds',
      header: 'Duration',
      render: (value) => {
        if (!value || value === 0) return '-';
        const minutes = Math.floor(value / 60);
        const seconds = value % 60;
        return `${minutes}:${seconds.toString().padStart(2, '0')}`;
      },
    },
    {
      key: 'start_time',
      header: 'Started',
      render: (value) => (
        <div className="text-sm">
          <div>{new Date(value).toLocaleDateString()}</div>
          <div className="text-muted-foreground">
            {formatDistanceToNow(new Date(value), { addSuffix: true })}
          </div>
        </div>
      ),
    },
    {
      key: 'actions',
      header: 'Actions',
      render: (_, row) => (
        <div className="flex gap-2">
          {row.recording_url && (
            <Button size="sm" variant="outline" onClick={() => window.open(row.recording_url, '_blank')}>
              <Phone className="h-3 w-3 mr-1" />
              Recording
            </Button>
          )}
          {row.transcript && (
            <Button size="sm" variant="outline" onClick={() => handleViewTranscript(row)}>
              View Transcript
            </Button>
          )}
        </div>
      ),
    },
  ];

  const handleViewTranscript = (callLog: CallLog) => {
    // TODO: Implement transcript modal
    toast({
      title: 'Transcript',
      description: callLog.transcript || 'No transcript available',
    });
  };

  const fetchCallLogs = async () => {
    if (!isReady) return;

    try {
      setIsLoading(true);
      const response = await authedFetch<{ calls: CallLog[] }>('/api/voice/calls');
      setCallLogs(response.calls || []);
    } catch (_error) {
      console.error('Error fetching call logs:', error);
      toast({
        variant: 'destructive',
        title: 'Error',
        description: 'Failed to fetch call logs',
      });
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchCallLogs();
  }, [isReady]);

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle>Call Logs</CardTitle>
            <CardDescription>
              View and manage voice receptionist call history
            </CardDescription>
          </div>
          <Button onClick={fetchCallLogs} disabled={isLoading} variant="outline">
            <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        <MultiTenantTable
          rows={callLogs}
          columns={columns}
          showTenantColumn={showTenantColumn}
          isLoading={isLoading}
          emptyMessage="No call logs found"
        />
      </CardContent>
    </Card>
  );
}
