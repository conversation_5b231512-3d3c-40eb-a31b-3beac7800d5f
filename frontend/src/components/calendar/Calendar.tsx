"use client";

import React, { useState, useEffect } from "react";
import { format, startOfWeek, endOfWeek, startOfMonth, endOfMonth, addDays, addMonths, subMonths, isToday, isSameMonth, isSameDay, parseISO } from "date-fns";
import { ChevronLeft, ChevronRight, Plus, Calendar as CalendarIcon, Clock, MapPin, Users, AlertCircle } from "lucide-react";
import { useRouter } from "next/navigation";
import { useSupabase } from '@/lib/supabase/provider';

import { Button } from "@/components/ui/button";
import { Calendar } from "@/components/ui/calendar";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Toolt<PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { cn } from "@/lib/utils";

import type { CalendarEvent, CalendarEventWithRelations } from "@/lib/services/calendar-event-services";
import type { CalendarDeadline, CalendarDeadlineWithRelations } from "@/lib/services/calendar-deadline-service";

type ViewMode = "month" | "week" | "day";

interface CalendarViewProps {
  className?: string;
  defaultView?: ViewMode;
  caseId?: string;
}

export function CalendarView({ className, defaultView = "month", caseId }: CalendarViewProps) {
  const router = useRouter();
  const { supabase } = useSupabase();

  const [viewMode, setViewMode] = useState<ViewMode>(defaultView);
  const [currentDate, setCurrentDate] = useState<Date>(new Date());
  const [events, setEvents] = useState<CalendarEventWithRelations[]>([]);
  const [deadlines, setDeadlines] = useState<CalendarDeadlineWithRelations[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch calendar data
  useEffect(() => {
    const fetchCalendarData = async () => {
      setIsLoading(true);
      setError(null);

      try {
        // Determine date range based on view mode
        let startDate, endDate;

        if (viewMode === "month") {
          startDate = format(startOfMonth(currentDate), "yyyy-MM-dd");
          endDate = format(endOfMonth(currentDate), "yyyy-MM-dd");
        } else if (viewMode === "week") {
          startDate = format(startOfWeek(currentDate), "yyyy-MM-dd");
          endDate = format(endOfWeek(currentDate), "yyyy-MM-dd");
        } else {
          startDate = format(currentDate, "yyyy-MM-dd");
          endDate = format(currentDate, "yyyy-MM-dd");
        }

        // Build query params
        const params = new URLSearchParams();
        params.append("start_date", startDate);
        params.append("end_date", endDate);
        if (caseId) params.append("case_id", caseId);

        // Fetch events
        const eventsResponse = await fetch(`/api/events?${params.toString()}`);
        if (!eventsResponse.ok) throw new Error("Failed to fetch events");
        const eventsData = await eventsResponse.json();

        // Fetch deadlines
        const deadlinesResponse = await fetch(`/api/deadlines?${params.toString()}`);
        if (!deadlinesResponse.ok) throw new Error("Failed to fetch deadlines");
        const deadlinesData = await deadlinesResponse.json();

        setEvents(eventsData.events || []);
        setDeadlines(deadlinesData.deadlines || []);
      } catch (_err) {
        console.error("Error fetching calendar data:", err);
        setError("Failed to load calendar data. Please try again.");
      } finally {
        setIsLoading(false);
      }
    };

    fetchCalendarData();
  }, [viewMode, currentDate, caseId]);

  // Navigation handlers
  const handlePrevious = () => {
    if (viewMode === "month") {
      setCurrentDate(prev => subMonths(prev, 1));
    } else if (viewMode === "week") {
      setCurrentDate(prev => addDays(prev, -7));
    } else {
      setCurrentDate(prev => addDays(prev, -1));
    }
  };

  const handleNext = () => {
    if (viewMode === "month") {
      setCurrentDate(prev => addMonths(prev, 1));
    } else if (viewMode === "week") {
      setCurrentDate(prev => addDays(prev, 7));
    } else {
      setCurrentDate(prev => addDays(prev, 1));
    }
  };

  const handleToday = () => {
    setCurrentDate(new Date());
  };

  // Helper to get events for a specific day
  const getEventsForDay = (day: Date) => {
    return events.filter(event => {
      const eventStart = parseISO(event.start_time);
      return isSameDay(eventStart, day);
    });
  };

  // Helper to get deadlines for a specific day
  const getDeadlinesForDay = (day: Date) => {
    return deadlines.filter(deadline => {
      const deadlineDate = parseISO(deadline.due_date);
      return isSameDay(deadlineDate, day);
    });
  };

  // Render month view
  const renderMonthView = () => {
    return (
      <div className="space-y-4">
        <Calendar
          mode="single"
          selected={currentDate}
          onSelect={(date) => date && setCurrentDate(date)}
          className="rounded-md border"
          modifiers={{
            hasEvent: (date) => getEventsForDay(date).length > 0,
            hasDeadline: (date) => getDeadlinesForDay(date).length > 0,
          }}
          modifiersClassNames={{
            hasEvent: "bg-blue-50 text-blue-600",
            hasDeadline: "border-l-4 border-red-500",
          }}
          components={{
            DayContent: (props) => {
              const day = props.date;
              const dayEvents = getEventsForDay(day);
              const dayDeadlines = getDeadlinesForDay(day);

              return (
                <div className="relative w-full h-full flex flex-col items-center justify-center">
                  <div>{props.date.getDate()}</div>
                  {dayEvents.length > 0 && (
                    <div className="absolute bottom-1 left-1">
                      <div className="w-1.5 h-1.5 rounded-full bg-blue-500"></div>
                    </div>
                  )}
                  {dayDeadlines.length > 0 && (
                    <div className="absolute bottom-1 right-1">
                      <div className="w-1.5 h-1.5 rounded-full bg-red-500"></div>
                    </div>
                  )}
                </div>
              );
            }
          }}
        />

        {/* Daily events for selected day */}
        <div className="mt-6">
          <h3 className="text-lg font-semibold mb-2">
            {format(currentDate, "EEEE, MMMM d, yyyy")}
          </h3>

          <div className="space-y-4">
            {getEventsForDay(currentDate).length === 0 && getDeadlinesForDay(currentDate).length === 0 ? (
              <p className="text-muted-foreground text-sm py-4">No events or deadlines for this day</p>
            ) : (
              <>
                {getEventsForDay(currentDate).map((event) => (
                  <EventCard key={event.id} event={event} />
                ))}

                {getDeadlinesForDay(currentDate).map((deadline) => (
                  <DeadlineCard key={deadline.id} deadline={deadline} />
                ))}
              </>
            )}
          </div>
        </div>
      </div>
    );
  };

  // Render week view
  const renderWeekView = () => {
    const weekStart = startOfWeek(currentDate);
    const weekDays = Array.from({ length: 7 }, (_, i) => addDays(weekStart, i));

    return (
      <div className="space-y-4">
        <div className="grid grid-cols-7 gap-1">
          {weekDays.map((day) => (
            <div
              key={day.toString()}
              className={cn(
                "p-2 text-center border rounded-md cursor-pointer hover:bg-accent transition-colors",
                isToday(day) && "bg-accent text-accent-foreground font-semibold",
                isSameDay(day, currentDate) && "ring-2 ring-primary"
              )}
              onClick={() => setCurrentDate(day)}
            >
              <div className="text-xs font-medium">{format(day, "EEE")}</div>
              <div className="text-lg">{format(day, "d")}</div>

              <div className="mt-1 flex justify-center gap-1">
                {getEventsForDay(day).length > 0 && (
                  <div className="w-2 h-2 rounded-full bg-blue-500"></div>
                )}
                {getDeadlinesForDay(day).length > 0 && (
                  <div className="w-2 h-2 rounded-full bg-red-500"></div>
                )}
              </div>
            </div>
          ))}
        </div>

        {/* Daily events for selected day */}
        <div className="mt-6">
          <h3 className="text-lg font-semibold mb-2">
            {format(currentDate, "EEEE, MMMM d, yyyy")}
          </h3>

          <div className="space-y-4">
            {getEventsForDay(currentDate).length === 0 && getDeadlinesForDay(currentDate).length === 0 ? (
              <p className="text-muted-foreground text-sm py-4">No events or deadlines for this day</p>
            ) : (
              <>
                {getEventsForDay(currentDate).map((event) => (
                  <EventCard key={event.id} event={event} />
                ))}

                {getDeadlinesForDay(currentDate).map((deadline) => (
                  <DeadlineCard key={deadline.id} deadline={deadline} />
                ))}
              </>
            )}
          </div>
        </div>
      </div>
    );
  };

  // Render day view
  const renderDayView = () => {
    const dayEvents = getEventsForDay(currentDate);
    const dayDeadlines = getDeadlinesForDay(currentDate);

    return (
      <div className="space-y-4">
        <div className="text-2xl font-bold mb-4">
          {format(currentDate, "EEEE, MMMM d, yyyy")}
        </div>

        <div className="space-y-4">
          <h3 className="text-lg font-semibold">Events</h3>
          {dayEvents.length === 0 ? (
            <p className="text-muted-foreground text-sm py-2">No events scheduled</p>
          ) : (
            <div className="space-y-4">
              {dayEvents.map((event) => (
                <EventCard key={event.id} event={event} detailed />
              ))}
            </div>
          )}

          <h3 className="text-lg font-semibold mt-6">Deadlines</h3>
          {dayDeadlines.length === 0 ? (
            <p className="text-muted-foreground text-sm py-2">No deadlines due</p>
          ) : (
            <div className="space-y-4">
              {dayDeadlines.map((deadline) => (
                <DeadlineCard key={deadline.id} deadline={deadline} detailed />
              ))}
            </div>
          )}
        </div>
      </div>
    );
  };

  return (
    <div className={cn("space-y-4", className)}>
      {/* Calendar Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <Button variant="outline" size="sm" onClick={handlePrevious}>
            <ChevronLeft className="h-4 w-4" />
          </Button>
          <Button variant="outline" size="sm" onClick={handleToday}>
            Today
          </Button>
          <Button variant="outline" size="sm" onClick={handleNext}>
            <ChevronRight className="h-4 w-4" />
          </Button>
          <h2 className="text-xl font-bold ml-2">
            {viewMode === "month" && format(currentDate, "MMMM yyyy")}
            {viewMode === "week" && `Week of ${format(startOfWeek(currentDate), "MMM d")} - ${format(endOfWeek(currentDate), "MMM d, yyyy")}`}
            {viewMode === "day" && format(currentDate, "MMMM d, yyyy")}
          </h2>
        </div>

        <div className="flex items-center space-x-2">
          <Tabs value={viewMode} onValueChange={(value) => setViewMode(value as ViewMode)}>
            <TabsList>
              <TabsTrigger value="month">Month</TabsTrigger>
              <TabsTrigger value="week">Week</TabsTrigger>
              <TabsTrigger value="day">Day</TabsTrigger>
            </TabsList>
          </Tabs>

          <Button size="sm" onClick={() => router.push("/calendar/new-event")}>
            <Plus className="h-4 w-4 mr-1" /> New Event
          </Button>
        </div>
      </div>

      {/* Loading and Error States */}
      {isLoading && <div className="py-8 text-center">Loading calendar data...</div>}
      {error && <div className="py-4 text-center text-red-500">{error}</div>}

      {/* Calendar Content */}
      {!isLoading && !error && (
        <>
          {viewMode === "month" && renderMonthView()}
          {viewMode === "week" && renderWeekView()}
          {viewMode === "day" && renderDayView()}
        </>
      )}
    </div>
  );
}

// Event Card Component
function EventCard({ event, detailed = false }: { event: CalendarEventWithRelations; detailed?: boolean }) {
  const startTime = parseISO(event.start_time);
  const endTime = parseISO(event.end_time);

  return (
    <Card className="overflow-hidden border-l-4 border-blue-500">
      <CardHeader className="p-4 pb-2">
        <div className="flex justify-between items-start">
          <div>
            <CardTitle className="text-base">{event.title}</CardTitle>
            <CardDescription className="text-sm">
              {event.all_day
                ? "All day"
                : `${format(startTime, "h:mm a")} - ${format(endTime, "h:mm a")}`}
            </CardDescription>
          </div>
          <Badge variant="outline">{event.event_type}</Badge>
        </div>
      </CardHeader>

      {(detailed || event.description) && (
        <CardContent className="p-4 pt-0">
          {event.description && <p className="text-sm mt-2">{event.description}</p>}

          {detailed && (
            <div className="grid grid-cols-2 gap-2 mt-3">
              {event.location && (
                <div className="flex items-center text-sm text-muted-foreground">
                  <MapPin className="h-3.5 w-3.5 mr-1" />
                  <span>{event.location}</span>
                </div>
              )}

              {event.case_info && (
                <div className="flex items-center text-sm text-muted-foreground">
                  <CalendarIcon className="h-3.5 w-3.5 mr-1" />
                  <span>Case: {event.case_info.title}</span>
                </div>
              )}

              {event.assignees && event.assignees.length > 0 && (
                <div className="flex items-center text-sm text-muted-foreground">
                  <Users className="h-3.5 w-3.5 mr-1" />
                  <span>{event.assignees.length} attendee{event.assignees.length !== 1 ? "s" : ""}</span>
                </div>
              )}
            </div>
          )}
        </CardContent>
      )}
    </Card>
  );
}

// Deadline Card Component
function DeadlineCard({ deadline, detailed = false }: { deadline: CalendarDeadlineWithRelations; detailed?: boolean }) {
  const dueDate = parseISO(deadline.due_date);
  const isCompleted = deadline.status === "completed";

  return (
    <Card className={cn(
      "overflow-hidden border-l-4",
      isCompleted ? "border-green-500" : "border-red-500"
    )}>
      <CardHeader className="p-4 pb-2">
        <div className="flex justify-between items-start">
          <div>
            <CardTitle className={cn(
              "text-base flex items-center",
              isCompleted && "line-through opacity-70"
            )}>
              {deadline.title}
              {isCompleted && (
                <Badge className="ml-2" variant="outline">Completed</Badge>
              )}
            </CardTitle>
            <CardDescription className="text-sm">
              Due: {format(dueDate, "MMMM d, yyyy")}
            </CardDescription>
          </div>
          <Badge variant={isCompleted ? "outline" : "destructive"}>
            {deadline.priority}
          </Badge>
        </div>
      </CardHeader>

      {(detailed || deadline.description) && (
        <CardContent className="p-4 pt-0">
          {deadline.description && <p className="text-sm mt-2">{deadline.description}</p>}

          {detailed && (
            <div className="grid grid-cols-2 gap-2 mt-3">
              {deadline.case_info && (
                <div className="flex items-center text-sm text-muted-foreground">
                  <CalendarIcon className="h-3.5 w-3.5 mr-1" />
                  <span>Case: {deadline.case_info.title}</span>
                </div>
              )}

              {deadline.rule_info && deadline.rule_info.jurisdiction && (
                <div className="flex items-center text-sm text-muted-foreground">
                  <AlertCircle className="h-3.5 w-3.5 mr-1" />
                  <span>Jurisdiction: {deadline.rule_info.jurisdiction}</span>
                </div>
              )}

              {isCompleted && deadline.completer && (
                <div className="flex items-center text-sm text-muted-foreground">
                  <Users className="h-3.5 w-3.5 mr-1" />
                  <span>Completed by: {deadline.completer.name || deadline.completer.email}</span>
                </div>
              )}
            </div>
          )}
        </CardContent>
      )}
    </Card>
  );
}
