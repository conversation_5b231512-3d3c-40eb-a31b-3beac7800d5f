'use client'

import { useState, useEffect } from 'react'
// No need for supabase client in this component
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import {
  // Key,
  // Shield,
  // Clock,
  Globe,
  Smartphone,
  Laptop,
  AlertTriangle,
  XCircle,
  // CheckCircle,
  Search
} from 'lucide-react'
import { formatDistanceToNow, formatDistance } from 'date-fns'
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'

interface TokenUsage {
  tokenId: string
  userId: string
  ipAddresses: string[]
  userAgents: string[]
  lastUsed: string
  issuedAt: string
  expiresAt: string
  isRevoked: boolean
}

export function TokensDashboard() {
  const [tokens, setTokens] = useState<TokenUsage[]>([])
  const [loading, setLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedToken, setSelectedToken] = useState<TokenUsage | null>(null)
  const [revokeReason, setRevokeReason] = useState('')
  const [revokeDialogOpen, setRevokeDialogOpen] = useState(false)
  const [revokeAllDialogOpen, setRevokeAllDialogOpen] = useState(false)
  const [revokeAllUserId, setRevokeAllUserId] = useState('')
  const [revokeAllReason, setRevokeAllReason] = useState('')

  useEffect(() => {
    fetchTokens()
  }, []) // eslint-disable-line react-hooks/exhaustive-deps

  async function fetchTokens() {
    setLoading(true)
    try {
      // Fetch tokens from the API
      const response = await fetch('/api/security/tokens')

      if (!response.ok) {
        try {
          const errorData = await response.json()
          console.error('Error response from tokens API:', errorData)
          throw new Error(errorData.error || 'Failed to fetch tokens')
        } catch (jsonError) {
          console.error('Error parsing error response:', jsonError)
          throw new Error('Failed to fetch tokens')
        }
      }

      let data
      try {
        const jsonResponse = await response.json()
        data = jsonResponse.data
      } catch (jsonError) {
        console.error('Error parsing JSON response:', jsonError)
        throw new Error('Invalid response format')
      }

      if (!data) {
        console.log('No tokens found, using mock data for demonstration')
        // Use mock data for demonstration if no tokens are found
        setTokens(createMockTokens())
      } else {
        console.log('Fetched tokens:', data)
        // If data is an array, use it directly, otherwise wrap it in an array
        if (Array.isArray(data)) {
          setTokens(data)
        } else {
          // If it's a single object, wrap it in an array
          setTokens([data])
        }
      }
    } catch (_error) {
      console.error('Error fetching tokens:', error)
      // Use mock data for demonstration in case of error
      setTokens(createMockTokens())
    } finally {
      setLoading(false)
    }
  }

  async function revokeToken() {
    if (!selectedToken) return

    try {
      // Call the API to revoke the token
      const response = await fetch('/api/security/tokens/revoke', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          tokenId: selectedToken.tokenId,
          reason: revokeReason || 'Administrator action'
        })
      })

      if (!response.ok) {
        try {
          const errorData = await response.json()
          console.error('Error response from revoke API:', errorData)
          throw new Error(errorData.error || 'Failed to revoke token')
        } catch (jsonError) {
          console.error('Error parsing error response:', jsonError)
          throw new Error('Failed to revoke token')
        }
      }

      // Update local state
      setTokens(tokens.map(token =>
        token.tokenId === selectedToken.tokenId ? { ...token, isRevoked: true } : token
      ))

      // Close dialog
      setRevokeDialogOpen(false)
      setRevokeReason('')
      setSelectedToken(null)
    } catch (_error) {
      console.error('Error revoking token:', error)
    }
  }

  async function revokeAllTokens() {
    if (!revokeAllUserId) return

    try {
      // Call the API to revoke all tokens for the user
      const response = await fetch('/api/security/tokens/revoke', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          userId: revokeAllUserId,
          reason: revokeAllReason || 'Administrator action',
          revokeAll: true
        })
      })

      if (!response.ok) {
        try {
          const errorData = await response.json()
          console.error('Error response from revoke all API:', errorData)
          throw new Error(errorData.error || 'Failed to revoke all tokens')
        } catch (jsonError) {
          console.error('Error parsing error response:', jsonError)
          throw new Error('Failed to revoke all tokens')
        }
      }

      // Update local state
      setTokens(tokens.map(token =>
        token.userId === revokeAllUserId ? { ...token, isRevoked: true } : token
      ))

      // Close dialog
      setRevokeAllDialogOpen(false)
      setRevokeAllUserId('')
      setRevokeAllReason('')
    } catch (_error) {
      console.error('Error revoking all tokens:', error)
    }
  }

  function createMockTokens(): TokenUsage[] {
    return [
      {
        tokenId: 'token-123',
        userId: 'user-123',
        ipAddresses: ['***********', '***********'],
        userAgents: ['Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7)', 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_7_1)'],
        lastUsed: new Date(Date.now() - 30 * 60 * 1000).toISOString(),
        issuedAt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
        expiresAt: new Date(Date.now() + 14 * 24 * 60 * 60 * 1000).toISOString(),
        isRevoked: false
      },
      {
        tokenId: 'token-456',
        userId: 'user-123',
        ipAddresses: ['***********'],
        userAgents: ['Mozilla/5.0 (Windows NT 10.0; Win64; x64)'],
        lastUsed: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
        issuedAt: new Date(Date.now() - 14 * 24 * 60 * 60 * 1000).toISOString(),
        expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
        isRevoked: false
      },
      {
        tokenId: 'token-789',
        userId: 'user-456',
        ipAddresses: ['************', '***********', '*********'],
        userAgents: ['Mozilla/5.0 (Linux; Android 11)', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64)', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7)'],
        lastUsed: new Date(Date.now() - 5 * 60 * 1000).toISOString(),
        issuedAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(),
        expiresAt: new Date(Date.now() + 25 * 24 * 60 * 60 * 1000).toISOString(),
        isRevoked: false
      },
      {
        tokenId: 'token-012',
        userId: 'user-789',
        ipAddresses: ['***********'],
        userAgents: ['Mozilla/5.0 (iPad; CPU OS 14_7_1)'],
        lastUsed: new Date(Date.now() - 10 * 24 * 60 * 60 * 1000).toISOString(),
        issuedAt: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString(),
        expiresAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString(),
        isRevoked: true
      }
    ]
  }

  function formatDate(dateString: string): string {
    try {
      return formatDistanceToNow(new Date(dateString), { addSuffix: true })
    } catch (err) {
      return 'Unknown date'
    }
  }

  // Unused function - keeping for future reference
  // function formatTimespan(start: string, end: string): string {
  //   try {
  //     return formatDistance(new Date(start), new Date(end))
  //   } catch (err) {
  //     return 'Unknown'
  //   }
  // }

  function getDeviceIcon(userAgent: string) {
    if (userAgent.includes('iPhone') || userAgent.includes('iPad') || userAgent.includes('Android')) {
      return <Smartphone className="h-5 w-5 text-blue-500" />
    }
    return <Laptop className="h-5 w-5 text-green-500" />
  }

  function isTokenExpired(token: TokenUsage): boolean {
    if (!token.expiresAt) return false
    return new Date(token.expiresAt) < new Date()
  }

  function isTokenSuspicious(token: TokenUsage): boolean {
    return token.ipAddresses.length > 2 || token.userAgents.length > 2
  }

  const filteredTokens = tokens.filter(token => {
    if (!searchQuery) return true

    return (
      token.tokenId.toLowerCase().includes(searchQuery.toLowerCase()) ||
      token.userId.toLowerCase().includes(searchQuery.toLowerCase()) ||
      token.ipAddresses.some(ip => ip.toLowerCase().includes(searchQuery.toLowerCase())) ||
      token.userAgents.some(ua => ua.toLowerCase().includes(searchQuery.toLowerCase()))
    )
  })

  if (loading) {
    return <div className="text-center py-8">Loading tokens...</div>
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold">Token Management</h2>
        <div className="flex items-center space-x-2">
          <div className="relative">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-500" />
            <Input
              type="search"
              placeholder="Search tokens..."
              className="pl-8 w-[250px]"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
          <Button onClick={fetchTokens} disabled={loading} size="sm">
            Refresh
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-lg">Active Tokens</CardTitle>
            <CardDescription>Currently valid</CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-3xl font-bold text-green-500">
              {tokens.filter(t => !t.isRevoked && !isTokenExpired(t)).length}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-lg">Revoked</CardTitle>
            <CardDescription>Manually invalidated</CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-3xl font-bold text-red-500">
              {tokens.filter(t => t.isRevoked).length}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-lg">Expired</CardTitle>
            <CardDescription>Past validity period</CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-3xl font-bold text-gray-500">
              {tokens.filter(t => isTokenExpired(t) && !t.isRevoked).length}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-lg">Suspicious</CardTitle>
            <CardDescription>Potential misuse</CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-3xl font-bold text-yellow-500">
              {tokens.filter(t => isTokenSuspicious(t) && !t.isRevoked).length}
            </p>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <div className="flex justify-between items-center">
            <div>
              <CardTitle>Active Tokens</CardTitle>
              <CardDescription>
                JWT tokens currently in use by users
              </CardDescription>
            </div>
            <Dialog open={revokeAllDialogOpen} onOpenChange={setRevokeAllDialogOpen}>
              <DialogTrigger asChild>
                <Button variant="destructive" size="sm">
                  <XCircle className="h-4 w-4 mr-2" />
                  Revoke All for User
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Revoke All Tokens</DialogTitle>
                  <DialogDescription>
                    This will invalidate all active tokens for a specific user. The user will be logged out of all devices and sessions.
                  </DialogDescription>
                </DialogHeader>
                <div className="space-y-4 py-4">
                  <div className="space-y-2">
                    <Label htmlFor="userId">User ID</Label>
                    <Input
                      id="userId"
                      placeholder="Enter user ID"
                      value={revokeAllUserId}
                      onChange={(e) => setRevokeAllUserId(e.target.value)}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="reason">Reason for Revocation</Label>
                    <Input
                      id="reason"
                      placeholder="Security concern, suspicious activity, etc."
                      value={revokeAllReason}
                      onChange={(e) => setRevokeAllReason(e.target.value)}
                    />
                  </div>
                </div>
                <DialogFooter>
                  <Button variant="outline" onClick={() => setRevokeAllDialogOpen(false)}>
                    Cancel
                  </Button>
                  <Button
                    variant="destructive"
                    onClick={revokeAllTokens}
                    disabled={!revokeAllUserId}
                  >
                    Revoke All Tokens
                  </Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>
          </div>
        </CardHeader>
        <CardContent>
          {filteredTokens.length === 0 ? (
            <div className="text-center py-8 text-gray-500">No tokens found</div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Status</TableHead>
                  <TableHead>Token ID</TableHead>
                  <TableHead>User ID</TableHead>
                  <TableHead>Devices</TableHead>
                  <TableHead>IP Addresses</TableHead>
                  <TableHead>Issued</TableHead>
                  <TableHead>Last Used</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredTokens.map(token => (
                  <TableRow
                    key={token.tokenId}
                    className={
                      token.isRevoked ? 'bg-red-50' :
                      isTokenExpired(token) ? 'bg-gray-50' :
                      isTokenSuspicious(token) ? 'bg-yellow-50' :
                      undefined
                    }
                  >
                    <TableCell>
                      {token.isRevoked ? (
                        <Badge className="bg-red-500">Revoked</Badge>
                      ) : isTokenExpired(token) ? (
                        <Badge className="bg-gray-500">Expired</Badge>
                      ) : isTokenSuspicious(token) ? (
                        <Badge className="bg-yellow-500">Suspicious</Badge>
                      ) : (
                        <Badge className="bg-green-500">Active</Badge>
                      )}
                    </TableCell>
                    <TableCell className="font-mono text-xs">
                      {typeof token.tokenId === 'string' ? `${token.tokenId.substring(0, 8)}...` : 'Unknown'}
                    </TableCell>
                    <TableCell className="font-mono text-xs">
                      {typeof token.userId === 'string' ? `${token.userId.substring(0, 8)}...` : 'Unknown'}
                    </TableCell>
                    <TableCell>
                      <div className="flex flex-col space-y-1">
                        {token.userAgents.map((ua, index) => (
                          <div key={index} className="flex items-center space-x-1">
                            {getDeviceIcon(ua)}
                            <span className="text-xs truncate max-w-[150px]" title={ua}>
                              {ua.includes('iPhone') ? 'iPhone' :
                               ua.includes('iPad') ? 'iPad' :
                               ua.includes('Android') ? 'Android' :
                               ua.includes('Windows') ? 'Windows' :
                               ua.includes('Mac') ? 'Mac' :
                               'Unknown'}
                            </span>
                          </div>
                        ))}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex flex-col space-y-1">
                        {token.ipAddresses.map((ip, index) => (
                          <div key={index} className="flex items-center space-x-1">
                            <Globe className="h-4 w-4 text-blue-500" />
                            <span className="text-xs">{ip}</span>
                          </div>
                        ))}
                        {token.ipAddresses.length > 2 && (
                          <Badge className="bg-yellow-500 w-fit">
                            <AlertTriangle className="h-3 w-3 mr-1" />
                            Multiple IPs
                          </Badge>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex flex-col">
                        <span className="text-xs">{formatDate(token.issuedAt)}</span>
                        {token.expiresAt && (
                          <span className="text-xs text-gray-500">
                            Expires: {formatDate(token.expiresAt)}
                          </span>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>
                      {formatDate(token.lastUsed)}
                    </TableCell>
                    <TableCell>
                      {!token.isRevoked && (
                        <Dialog open={revokeDialogOpen && selectedToken?.tokenId === token.tokenId} onOpenChange={(open) => {
                          setRevokeDialogOpen(open)
                          if (!open) setSelectedToken(null)
                        }}>
                          <DialogTrigger asChild>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => setSelectedToken(token)}
                            >
                              Revoke
                            </Button>
                          </DialogTrigger>
                          <DialogContent>
                            <DialogHeader>
                              <DialogTitle>Revoke Token</DialogTitle>
                              <DialogDescription>
                                This will invalidate the token and the user will be logged out of this session.
                              </DialogDescription>
                            </DialogHeader>
                            <div className="space-y-4 py-4">
                              <div className="space-y-2">
                                <Label htmlFor="tokenId">Token ID</Label>
                                <Input
                                  id="tokenId"
                                  value={selectedToken?.tokenId || ''}
                                  readOnly
                                />
                              </div>
                              <div className="space-y-2">
                                <Label htmlFor="reason">Reason for Revocation</Label>
                                <Input
                                  id="reason"
                                  placeholder="Security concern, suspicious activity, etc."
                                  value={revokeReason}
                                  onChange={(e) => setRevokeReason(e.target.value)}
                                />
                              </div>
                            </div>
                            <DialogFooter>
                              <Button variant="outline" onClick={() => {
                                setRevokeDialogOpen(false)
                                setSelectedToken(null)
                              }}>
                                Cancel
                              </Button>
                              <Button
                                variant="destructive"
                                onClick={revokeToken}
                              >
                                Revoke Token
                              </Button>
                            </DialogFooter>
                          </DialogContent>
                        </Dialog>
                      )}
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
