'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { useSupabase } from '@/lib/supabase/provider'
import { useSecurity } from '@/lib/security/context'
import {
  revokeSession,
  revokeAllSessions,
  getActiveSessions,
  getUserDevices
} from '@/lib/security/token-tracker'

export default function SecurityDashboard() {
  const { supabase } = useSupabase()
  const { deviceFingerprint, isDeviceTrusted, trustDevice, blockDevice } = useSecurity()
  const router = useRouter()

  const [sessions, setSessions] = useState<any[]>([])
  const [devices, setDevices] = useState<any[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // Load sessions and devices on mount
  useEffect(() => {
    const loadSecurityData = async () => {
      setLoading(true)
      setError(null)

      try {
        // Get active sessions
        const sessionsData = await getActiveSessions()
        setSessions(sessionsData)

        // Get devices
        const devicesData = await getUserDevices()
        setDevices(devicesData)
      } catch (_err) {
        console.error('Error loading security data:', err)
        setError('Failed to load security information')
      } finally {
        setLoading(false)
      }
    }

    loadSecurityData()
  }, [])

  // Handle session revocation
  const handleRevokeSession = async (sessionId: string) => {
    try {
      const success = await revokeSession(
        sessionId,
        'User initiated revocation'
      )

      if (success) {
        // Refresh sessions list
        const updatedSessions = await getActiveSessions()
        setSessions(updatedSessions)
      } else {
        setError('Failed to revoke session')
      }
    } catch (_err) {
      console.error('Error revoking session:', err)
      setError('Failed to revoke session')
    }
  }

  // Handle revoking all sessions
  const handleRevokeAllSessions = async () => {
    try {
      const success = await revokeAllSessions(
        'User initiated revocation of all sessions'
      )

      if (success) {
        // Sign out and redirect to login
        await supabase.auth.signOut()
        router.push('/login?reason=all-sessions-revoked')
      } else {
        setError('Failed to revoke all sessions')
      }
    } catch (_err) {
      console.error('Error revoking all sessions:', err)
      setError('Failed to revoke all sessions')
    }
  }

  // Handle trusting current device
  const handleTrustDevice = async () => {
    if (!deviceFingerprint) return

    try {
      const success = await trustDevice()

      if (success) {
        // Refresh devices list
        const updatedDevices = await getUserDevices()
        setDevices(updatedDevices)
      } else {
        setError('Failed to trust device')
      }
    } catch (_err) {
      console.error('Error trusting device:', err)
      setError('Failed to trust device')
    }
  }

  // Handle blocking a device
  const handleBlockDevice = async (fingerprint: string) => {
    try {
      const success = await blockDevice(fingerprint)

      if (success) {
        // Refresh devices list
        const updatedDevices = await getUserDevices()
        setDevices(updatedDevices)

        // If blocking current device, sign out
        if (fingerprint === deviceFingerprint) {
          await supabase.auth.signOut()
          router.push('/login?reason=device-blocked')
        }
      } else {
        setError('Failed to block device')
      }
    } catch (_err) {
      console.error('Error blocking device:', err)
      setError('Failed to block device')
    }
  }

  if (loading) {
    return <div className="p-4">Loading security information...</div>
  }

  return (
    <div className="p-4 space-y-8">
      <h1 className="text-2xl font-bold">Security Dashboard</h1>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
          {error}
        </div>
      )}

      {/* Current Device */}
      <div className="bg-white shadow rounded-lg p-6">
        <h2 className="text-xl font-semibold mb-4">Current Device</h2>
        <div className="space-y-2">
          <p>
            <span className="font-medium">Device ID:</span> {deviceFingerprint || 'Unknown'}
          </p>
          <p>
            <span className="font-medium">Trust Status:</span>{' '}
            {isDeviceTrusted ? (
              <span className="text-green-600">Trusted</span>
            ) : (
              <span className="text-yellow-600">Not Trusted</span>
            )}
          </p>

          {!isDeviceTrusted && (
            <button
              onClick={handleTrustDevice}
              className="mt-2 bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded"
            >
              Trust This Device
            </button>
          )}
        </div>
      </div>

      {/* Active Sessions */}
      <div className="bg-white shadow rounded-lg p-6">
        <h2 className="text-xl font-semibold mb-4">Active Sessions</h2>
        {sessions.length === 0 ? (
          <p>No active sessions found.</p>
        ) : (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Session ID
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Created
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Last Used
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    IP Address
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {sessions.map((session) => (
                  <tr key={session.id}>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                      {session.id.substring(0, 8)}...
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {new Date(session.created_at).toLocaleString()}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {session.refreshed_at
                        ? new Date(session.refreshed_at).toLocaleString()
                        : 'N/A'}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {session.ip || 'Unknown'}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <button
                        onClick={() => handleRevokeSession(session.id)}
                        className="text-red-600 hover:text-red-900"
                      >
                        Revoke
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}

        <button
          onClick={handleRevokeAllSessions}
          className="mt-4 bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded"
        >
          Revoke All Sessions
        </button>
      </div>

      {/* Known Devices */}
      <div className="bg-white shadow rounded-lg p-6">
        <h2 className="text-xl font-semibold mb-4">Known Devices</h2>
        {devices.length === 0 ? (
          <p>No devices found.</p>
        ) : (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Device Name
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    First Seen
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Last Seen
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {devices.map((device) => (
                  <tr key={device.id}>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                      {device.device_name || 'Unknown Device'}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {new Date(device.first_seen).toLocaleString()}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {new Date(device.last_seen).toLocaleString()}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {device.is_blocked ? (
                        <span className="text-red-600">Blocked</span>
                      ) : device.is_trusted ? (
                        <span className="text-green-600">Trusted</span>
                      ) : (
                        <span className="text-yellow-600">Not Trusted</span>
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      {device.is_blocked ? (
                        <span className="text-gray-400">Blocked</span>
                      ) : device.fingerprint === deviceFingerprint ? (
                        <span className="text-gray-400">Current Device</span>
                      ) : (
                        <button
                          onClick={() => handleBlockDevice(device.fingerprint)}
                          className="text-red-600 hover:text-red-900"
                        >
                          Block
                        </button>
                      )}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>
    </div>
  )
}
