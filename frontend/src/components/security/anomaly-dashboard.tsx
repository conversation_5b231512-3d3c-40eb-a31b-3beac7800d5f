// @ts-expect-error - LocationDisplay component
import { LocationDisplay } from './location-display';
'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { AlertTriangle, Shield, Info, AlertCircle } from 'lucide-react'
import { formatDistanceToNow, format as formatDate } from 'date-fns'

interface AnomalyEvent {
  id: string
  user_id: string
  event_type: string
  score: number
  severity: 'low' | 'medium' | 'high' | 'critical'
  reasons: string[]
  created_at: string
  details: Record<string, unknown>
}

export function AnomalyDashboard() {
  const [anomalies, setAnomalies] = useState<AnomalyEvent[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    // In a real app, this would fetch from an API
    fetchAnomalies()
  }, [])

  async function fetchAnomalies() {
    setLoading(true)
    try {
      // Simulate API call with mock data
      setTimeout(() => {
        setAnomalies(getMockAnomalies())
        setLoading(false)
      }, 1000)
    } catch (_error) {
      console.error('Error fetching anomalies:', error)
      setLoading(false)
    }
  }

  function getMockAnomalies(): AnomalyEvent[] {
    return [
      {
        id: 'mock-anomaly-1',
        user_id: 'user-123',
        event_type: 'suspicious.anomaly_detected',
        score: 75,
        severity: 'high',
        reasons: ['Login from unusual location', 'Unusual time of day'],
        created_at: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
        details: {
          relatedEventId: 'event-123',
          location: 'Moscow, Russia',
          ip_address: '***********'
        }
      },
      {
        id: 'mock-anomaly-2',
        user_id: 'user-456',
        event_type: 'suspicious.anomaly_detected',
        score: 90,
        severity: 'critical',
        reasons: ['Multiple failed login attempts', 'Login from unusual device', 'Unusual browser'],
        created_at: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(),
        details: {
          relatedEventId: 'event-456',
          location: 'Lagos, Nigeria',
          ip_address: '************'
        }
      },
      {
        id: 'mock-anomaly-3',
        user_id: 'user-789',
        event_type: 'suspicious.anomaly_detected',
        score: 45,
        severity: 'medium',
        reasons: ['Rapid succession login from different location'],
        created_at: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString(),
        details: {
          relatedEventId: 'event-789',
          location: 'Beijing, China',
          ip_address: '*********'
        }
      }
    ]
  }

  function getSeverityIcon(severity: string) {
    switch (severity) {
      case 'critical':
        return <AlertCircle className="h-5 w-5 text-red-500" />
      case 'high':
        return <AlertTriangle className="h-5 w-5 text-orange-500" />
      case 'medium':
        return <Shield className="h-5 w-5 text-yellow-500" />
      case 'low':
        return <Info className="h-5 w-5 text-blue-500" />
      default:
        return <Info className="h-5 w-5 text-gray-500" />
    }
  }

  function getSeverityClass(severity: string) {
    switch (severity) {
      case 'critical':
        return 'bg-red-100 text-red-800 border-red-300'
      case 'high':
        return 'bg-orange-100 text-orange-800 border-orange-300'
      case 'medium':
        return 'bg-yellow-100 text-yellow-800 border-yellow-300'
      case 'low':
        return 'bg-blue-100 text-blue-800 border-blue-300'
      default:
        return 'bg-gray-100 text-gray-800 border-gray-300'
    }
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Security Anomalies</CardTitle>
          <CardDescription>
            Review detected security anomalies and take action
          </CardDescription>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex justify-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
            </div>
          ) : anomalies.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              No anomalies detected
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Severity</TableHead>
                  <TableHead>Event Type</TableHead>
                  <TableHead>Reasons</TableHead>
                  <TableHead>Score</TableHead>
                  <TableHead>Location</TableHead>
                  <TableHead>Time</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {anomalies.map((anomaly) => (
                  <TableRow key={anomaly.id}>
                    <TableCell>
                      <Badge className={`flex items-center gap-1 ${getSeverityClass(anomaly.severity)}`} variant="outline">
                        {getSeverityIcon(anomaly.severity)}
                        {anomaly.severity}
                      </Badge>
                    </TableCell>
                    <TableCell className="font-medium">
                      {anomaly.event_type.split('.').pop()?.replace(/_/g, ' ')}
                    </TableCell>
                    <TableCell>
                      <ul className="list-disc list-inside text-sm">
                        {anomaly.reasons.map((reason, index) => (
                          <li key={index}>{reason}</li>
                        ))}
                      </ul>
                    </TableCell>
                    <TableCell>
                      <div className="flex flex-col">
                        <div className="w-full bg-gray-200 rounded-full h-2 mb-1">
                          <div
                            className={`h-2 rounded-full ${
                              anomaly.score > 75 ? 'bg-red-500' :
                              anomaly.score > 50 ? 'bg-orange-500' :
                              anomaly.score > 25 ? 'bg-yellow-500' : 'bg-blue-500'
                            }`}
                            style={{ width: `${anomaly.score}%` }}
                          ></div>
                        </div>
                        <span>{anomaly.score}</span>
                      </div>
                    </TableCell>
                    <TableCell>
                      {/* @ts-expect-error - Complex location display logic */}
                      <span>{anomaly.details?.location?.toString() || 'Unknown'}</span>
                    </TableCell>
                    <TableCell>
                      {formatDate(new Date(anomaly.created_at), 'MMM d, yyyy HH:mm')}
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
