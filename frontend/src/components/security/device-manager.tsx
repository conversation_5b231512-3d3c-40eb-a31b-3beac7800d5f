'use client'

import { useState, useEffect } from 'react'
import { useSupabase } from '@/lib/supabase/provider'
import { useSecurity } from '@/lib/security'
import { Button } from '@/components/ui/button'
import { Shield, ShieldAlert, ShieldCheck, Laptop, Smartphone, Globe } from 'lucide-react'
import { formatDistanceToNow } from 'date-fns'

interface Device {
  id: string
  fingerprint: string
  first_seen: string
  last_seen: string
  ip_address: string
  user_agent: string
  device_name: string
  is_trusted: boolean
  is_blocked: boolean
}

export function DeviceManager() {
  const { supabase } = useSupabase()
  const { deviceFingerprint, blockDevice, trustDevice } = useSecurity()
  const [devices, setDevices] = useState<Device[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [blockingDevice, setBlockingDevice] = useState<string | null>(null)
  const [trustingDevice, setTrustingDevice] = useState<string | null>(null)

  // Fetch user's devices
  useEffect(() => {
    const fetchDevices = async () => {
      try {
        const { data, error } = await supabase.rpc('get_user_devices')

        if (error) {
          console.error('Error fetching devices:', error)
          setIsLoading(false)
          return
        }

        setDevices(data || [])
      } catch (_error) {
        console.error('Error fetching devices:', error)
      } finally {
        setIsLoading(false)
      }
    }

    fetchDevices()
  }, [supabase])

  // Handle blocking a device
  const handleBlockDevice = async (fingerprint: string) => {
    setBlockingDevice(fingerprint)
    try {
      const success = await blockDevice(fingerprint)

      if (success) {
        // Update local state to reflect the change
        setDevices(devices.map(device =>
          device.fingerprint === fingerprint
            ? { ...device, is_blocked: true, is_trusted: false }
            : device
        ))
      }
    } catch (_error) {
      console.error('Error blocking device:', error)
    } finally {
      setBlockingDevice(null)
    }
  }

  // Handle trusting a device
  const handleTrustDevice = async (fingerprint: string) => {
    setTrustingDevice(fingerprint)
    try {
      const success = await trustDevice(fingerprint)

      if (success) {
        // Update local state to reflect the change
        setDevices(devices.map(device =>
          device.fingerprint === fingerprint
            ? { ...device, is_trusted: true, is_blocked: false }
            : device
        ))
      }
    } catch (_error) {
      console.error('Error trusting device:', error)
    } finally {
      setTrustingDevice(null)
    }
  }

  // Determine device icon based on user agent
  const getDeviceIcon = (userAgent: string) => {
    if (!userAgent) return <Globe className="h-5 w-5" />

    if (userAgent.includes('Mobile') || userAgent.includes('Android') || userAgent.includes('iPhone')) {
      return <Smartphone className="h-5 w-5" />
    }

    return <Laptop className="h-5 w-5" />
  }

  if (isLoading) {
    return <div className="text-center py-8">Loading devices...</div>
  }

  return (
    <div className="space-y-6">
      <h2 className="text-xl font-semibold">Your Devices</h2>

      {devices.length === 0 ? (
        <div className="text-center py-8 text-gray-500">No devices found</div>
      ) : (
        <div className="space-y-4">
          {devices.map(device => {
            const isCurrentDevice = device.fingerprint === deviceFingerprint

            return (
              <div
                key={device.id}
                className={`border rounded-lg p-4 ${
                  isCurrentDevice ? 'border-blue-200 bg-blue-50' : ''
                } ${
                  device.is_blocked ? 'border-red-200 bg-red-50' : ''
                }`}
              >
                <div className="flex items-start justify-between">
                  <div className="flex items-start space-x-3">
                    <div className="mt-1">
                      {getDeviceIcon(device.user_agent)}
                    </div>
                    <div>
                      <div className="font-medium">
                        {device.device_name || 'Unknown Device'}
                        {isCurrentDevice && (
                          <span className="ml-2 text-xs bg-blue-100 text-blue-800 px-2 py-0.5 rounded">
                            Current
                          </span>
                        )}
                      </div>
                      <div className="text-sm text-gray-500">
                        Last active: {formatDistanceToNow(new Date(device.last_seen))} ago
                      </div>
                      <div className="text-sm text-gray-500">
                        First seen: {formatDistanceToNow(new Date(device.first_seen))} ago
                      </div>
                      <div className="text-sm text-gray-500">
                        IP: {device.ip_address || 'Unknown'}
                      </div>
                      <div className="mt-2 flex items-center space-x-2">
                        {device.is_trusted ? (
                          <span className="flex items-center text-xs text-green-700 bg-green-100 px-2 py-0.5 rounded">
                            <ShieldCheck className="h-3 w-3 mr-1" />
                            Trusted
                          </span>
                        ) : device.is_blocked ? (
                          <span className="flex items-center text-xs text-red-700 bg-red-100 px-2 py-0.5 rounded">
                            <ShieldAlert className="h-3 w-3 mr-1" />
                            Blocked
                          </span>
                        ) : (
                          <span className="flex items-center text-xs text-gray-700 bg-gray-100 px-2 py-0.5 rounded">
                            <Shield className="h-3 w-3 mr-1" />
                            Not Trusted
                          </span>
                        )}
                      </div>
                    </div>
                  </div>

                  <div className="flex space-x-2">
                    {!device.is_trusted && !device.is_blocked && (
                      <Button
                        variant="outline"
                        size="sm"
                        className="border-green-300 hover:bg-green-100 text-green-800"
                        onClick={() => handleTrustDevice(device.fingerprint)}
                        disabled={trustingDevice === device.fingerprint}
                      >
                        <ShieldCheck className="h-4 w-4 mr-1" />
                        {trustingDevice === device.fingerprint ? 'Trusting...' : 'Trust Device'}
                      </Button>
                    )}

                    {!device.is_blocked && (
                      <Button
                        variant="outline"
                        size="sm"
                        className="border-red-300 hover:bg-red-100 text-red-800"
                        onClick={() => handleBlockDevice(device.fingerprint)}
                        disabled={blockingDevice === device.fingerprint}
                      >
                        <ShieldAlert className="h-4 w-4 mr-1" />
                        {blockingDevice === device.fingerprint ? 'Blocking...' : 'Block Device'}
                      </Button>
                    )}
                  </div>
                </div>
              </div>
            )
          })}
        </div>
      )}

      <div className="bg-gray-50 border rounded-lg p-4 text-sm text-gray-600">
        <p className="font-medium mb-2">About Device Security</p>
        <ul className="list-disc pl-5 space-y-1">
          <li>Trusted devices allow for extended session times and reduced security prompts</li>
          <li>Blocking a device will immediately sign out all sessions from that device</li>
          <li>We track device information to protect your account from unauthorized access</li>
        </ul>
      </div>
    </div>
  )
}
