'use client'

import { useState, useEffect } from 'react'
// No need for supabase client in this component
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import {
  AlertTriangle,
  Bell,
  Info,
  AlertCircle,
  Mail,
  MessageSquare,
  CheckCircle,
  // XCircle,
  Settings
} from 'lucide-react'
import { formatDistanceToNow } from 'date-fns'
import { <PERSON>bs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Switch } from '@/components/ui/switch'
// import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'

interface Alert {
  id: string
  user_id: string
  title: string
  message: string
  severity: 'low' | 'medium' | 'high' | 'critical'
  event_id: string
  read: boolean
  created_at: string
}

interface AlertConfig {
  id: string
  user_id: string
  email: boolean
  in_app: boolean
  sms: boolean
  min_severity: 'low' | 'medium' | 'high' | 'critical'
}

export function AlertsDashboard() {
  const [alerts, setAlerts] = useState<Alert[]>([])
  const [configs, setConfigs] = useState<AlertConfig[]>([])
  const [loading, setLoading] = useState(true)
  const [configLoading, setConfigLoading] = useState(true)
  const [activeTab, setActiveTab] = useState('alerts')

  useEffect(() => {
    fetchAlerts()
    fetchAlertConfigs()
  }, []) // eslint-disable-line react-hooks/exhaustive-deps

  async function fetchAlerts() {
    setLoading(true)
    try {
      // Fetch alerts from the API
      const response = await fetch('/api/security/alerts')

      if (!response.ok) {
        try {
          const errorData = await response.json()
          console.error('Error response from alerts API:', errorData)
          throw new Error(errorData.error || 'Failed to fetch alerts')
        } catch (jsonError) {
          console.error('Error parsing error response:', jsonError)
          throw new Error('Failed to fetch alerts')
        }
      }

      let data
      try {
        const jsonResponse = await response.json()
        data = jsonResponse.data
      } catch (jsonError) {
        console.error('Error parsing JSON response:', jsonError)
        throw new Error('Invalid response format')
      }

      if (!data || data.length === 0) {
        console.log('No alerts found, using mock data for demonstration')
        // Use mock data for demonstration if no alerts are found
        setAlerts(createMockAlerts())
      } else {
        console.log('Fetched alerts:', data.length)
        setAlerts(data)
      }
    } catch (_error) {
      console.error('Error fetching alerts:', error)
      // Use mock data for demonstration in case of error
      setAlerts(createMockAlerts())
    } finally {
      setLoading(false)
    }
  }

  async function fetchAlertConfigs() {
    setConfigLoading(true)
    try {
      // Fetch alert configurations from the API
      const response = await fetch('/api/security/alerts/config')

      if (!response.ok) {
        try {
          const errorData = await response.json()
          console.error('Error response from alert configs API:', errorData)
          throw new Error(errorData.error || 'Failed to fetch alert configurations')
        } catch (jsonError) {
          console.error('Error parsing error response:', jsonError)
          throw new Error('Failed to fetch alert configurations')
        }
      }

      let data
      try {
        const jsonResponse = await response.json()
        data = jsonResponse.data
      } catch (jsonError) {
        console.error('Error parsing JSON response:', jsonError)
        throw new Error('Invalid response format')
      }

      if (!data) {
        console.log('No alert configurations found, using mock data for demonstration')
        // Use mock data for demonstration if no configurations are found
        setConfigs(createMockConfigs())
      } else {
        console.log('Fetched alert configurations:', data)
        // If data is an array, use it directly, otherwise wrap it in an array
        if (Array.isArray(data)) {
          setConfigs(data)
        } else {
          // If it's a single object, wrap it in an array
          setConfigs([data])
        }
      }
    } catch (_error) {
      console.error('Error fetching alert configurations:', error)
      // Use mock data for demonstration in case of error
      setConfigs(createMockConfigs())
    } finally {
      setConfigLoading(false)
    }
  }

  async function markAsRead(alertId: string) {
    try {
      // Call the API to mark the alert as read
      const response = await fetch('/api/security/alerts/read', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ alertId })
      })

      if (!response.ok) {
        try {
          const errorData = await response.json()
          console.error('Error response from mark as read API:', errorData)
          throw new Error(errorData.error || 'Failed to mark alert as read')
        } catch (jsonError) {
          console.error('Error parsing error response:', jsonError)
          throw new Error('Failed to mark alert as read')
        }
      }

      // Update local state
      setAlerts(alerts.map(alert =>
        alert.id === alertId ? { ...alert, read: true } : alert
      ))
    } catch (_error) {
      console.error('Error marking alert as read:', error)
    }
  }

  async function updateAlertConfig(userId: string, field: string, value: boolean | string) {
    try {
      // Call the API to update the alert configuration
      const response = await fetch('/api/security/alerts/config', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          userId,
          [field]: value
        })
      })

      if (!response.ok) {
        try {
          const errorData = await response.json()
          console.error('Error response from update config API:', errorData)
          throw new Error(errorData.error || 'Failed to update alert configuration')
        } catch (jsonError) {
          console.error('Error parsing error response:', jsonError)
          throw new Error('Failed to update alert configuration')
        }
      }

      // Update local state
      setConfigs(configs.map(config =>
        config.user_id === userId ? { ...config, [field]: value } : config
      ))
    } catch (_error) {
      console.error('Error updating alert configuration:', error)
    }
  }

  function createMockAlerts(): Alert[] {
    return [
      {
        id: 'mock-alert-1',
        user_id: 'user-123',
        title: 'Security Alert: High Risk Detected',
        message: 'We detected unusual activity on your account: Login from unusual location, Unusual time of day. If this wasn\'t you, please contact support immediately.',
        severity: 'high',
        event_id: 'event-123',
        read: false,
        created_at: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString()
      },
      {
        id: 'mock-alert-2',
        user_id: 'user-456',
        title: 'Security Alert: Critical Risk Detected',
        message: 'We detected unusual activity on your account: Multiple failed login attempts, Login from unusual device, Unusual browser. If this wasn\'t you, please contact support immediately.',
        severity: 'critical',
        event_id: 'event-456',
        read: true,
        created_at: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString()
      },
      {
        id: 'mock-alert-3',
        user_id: 'user-789',
        title: 'Security Alert: Medium Risk Detected',
        message: 'We detected unusual activity on your account: Rapid succession login from different location. If this wasn\'t you, please contact support immediately.',
        severity: 'medium',
        event_id: 'event-789',
        read: false,
        created_at: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString()
      }
    ]
  }

  function createMockConfigs(): AlertConfig[] {
    return [
      {
        id: 'mock-config-1',
        user_id: 'user-123',
        email: true,
        in_app: true,
        sms: false,
        min_severity: 'medium'
      },
      {
        id: 'mock-config-2',
        user_id: 'user-456',
        email: true,
        in_app: true,
        sms: true,
        min_severity: 'high'
      },
      {
        id: 'mock-config-3',
        user_id: 'user-789',
        email: true,
        in_app: false,
        sms: false,
        min_severity: 'low'
      }
    ]
  }

  function getSeverityIcon(severity: string) {
    switch (severity) {
      case 'critical':
        return <AlertCircle className="h-5 w-5 text-red-500" />
      case 'high':
        return <AlertTriangle className="h-5 w-5 text-orange-500" />
      case 'medium':
        return <Bell className="h-5 w-5 text-yellow-500" />
      case 'low':
        return <Info className="h-5 w-5 text-blue-500" />
      default:
        return <Info className="h-5 w-5 text-gray-500" />
    }
  }

  function getSeverityColor(severity: string): string {
    switch (severity) {
      case 'critical':
        return 'bg-red-500'
      case 'high':
        return 'bg-orange-500'
      case 'medium':
        return 'bg-yellow-500'
      case 'low':
        return 'bg-blue-500'
      default:
        return 'bg-gray-500'
    }
  }

  function formatDate(dateString: string): string {
    try {
      return formatDistanceToNow(new Date(dateString), { addSuffix: true })
    } catch (_err) {
      return 'Unknown date'
    }
  }

  if (loading && activeTab === 'alerts') {
    return <div className="text-center py-8">Loading alerts...</div>
  }

  if (configLoading && activeTab === 'settings') {
    return <div className="text-center py-8">Loading alert configurations...</div>
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold">Security Alerts</h2>
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-[400px]">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="alerts" className="flex items-center">
              <Bell className="h-4 w-4 mr-2" />
              Alerts
            </TabsTrigger>
            <TabsTrigger value="settings" className="flex items-center">
              <Settings className="h-4 w-4 mr-2" />
              Notification Settings
            </TabsTrigger>
          </TabsList>
        </Tabs>
      </div>

      <TabsContent value="alerts" className="mt-0 space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-lg">Unread</CardTitle>
              <CardDescription>Pending alerts</CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-3xl font-bold">
                {alerts.filter(a => !a.read).length}
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-lg">Critical</CardTitle>
              <CardDescription>Highest priority</CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-3xl font-bold text-red-500">
                {alerts.filter(a => a.severity === 'critical').length}
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-lg">High</CardTitle>
              <CardDescription>Important alerts</CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-3xl font-bold text-orange-500">
                {alerts.filter(a => a.severity === 'high').length}
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-lg">Today</CardTitle>
              <CardDescription>Recent alerts</CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-3xl font-bold">
                {alerts.filter(a => {
                  const date = new Date(a.created_at)
                  const today = new Date()
                  return date.getDate() === today.getDate() &&
                    date.getMonth() === today.getMonth() &&
                    date.getFullYear() === today.getFullYear()
                }).length}
              </p>
            </CardContent>
          </Card>
        </div>

        <Card>
          <CardHeader>
            <CardTitle>Alert History</CardTitle>
            <CardDescription>
              Security notifications sent to users
            </CardDescription>
          </CardHeader>
          <CardContent>
            {alerts.length === 0 ? (
              <div className="text-center py-8 text-gray-500">No alerts found</div>
            ) : (
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Status</TableHead>
                    <TableHead>Severity</TableHead>
                    <TableHead>Title</TableHead>
                    <TableHead>User ID</TableHead>
                    <TableHead>Time</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {alerts.map(alert => (
                    <TableRow key={alert.id} className={!alert.read ? 'bg-gray-50' : undefined}>
                      <TableCell>
                        {alert.read ? (
                          <CheckCircle className="h-5 w-5 text-green-500" />
                        ) : (
                          <Badge className="bg-blue-500">New</Badge>
                        )}
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center space-x-2">
                          {getSeverityIcon(alert.severity)}
                          <Badge className={`${getSeverityColor(alert.severity)} text-white`}>
                            {alert.severity.charAt(0).toUpperCase() + alert.severity.slice(1)}
                          </Badge>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="font-medium">{alert.title}</div>
                        <div className="text-sm text-gray-500 truncate max-w-md">
                          {alert.message}
                        </div>
                      </TableCell>
                      <TableCell className="font-mono text-xs">
                        {typeof alert.user_id === 'string' ? `${alert.user_id.substring(0, 8)}...` : 'Unknown'}
                      </TableCell>
                      <TableCell>
                        {formatDate(alert.created_at)}
                      </TableCell>
                      <TableCell>
                        {!alert.read && (
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => markAsRead(alert.id)}
                          >
                            Mark as Read
                          </Button>
                        )}
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            )}
          </CardContent>
        </Card>
      </TabsContent>

      <TabsContent value="settings" className="mt-0 space-y-6">
        <Card>
          <CardHeader>
            <CardTitle>Alert Notification Settings</CardTitle>
            <CardDescription>
              Configure how security alerts are delivered to users
            </CardDescription>
          </CardHeader>
          <CardContent>
            {configs.length === 0 ? (
              <div className="text-center py-8 text-gray-500">No configurations found</div>
            ) : (
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>User ID</TableHead>
                    <TableHead>Email</TableHead>
                    <TableHead>In-App</TableHead>
                    <TableHead>SMS</TableHead>
                    <TableHead>Min Severity</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {configs.map(config => (
                    <TableRow key={config.id}>
                      <TableCell className="font-mono text-xs">
                        {typeof config.user_id === 'string' ? `${config.user_id.substring(0, 8)}...` : 'Unknown'}
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center space-x-2">
                          <Switch
                            checked={config.email}
                            onCheckedChange={(checked) => updateAlertConfig(config.user_id, 'email', checked)}
                          />
                          <Mail className={`h-5 w-5 ${config.email ? 'text-green-500' : 'text-gray-300'}`} />
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center space-x-2">
                          <Switch
                            checked={config.in_app}
                            onCheckedChange={(checked) => updateAlertConfig(config.user_id, 'in_app', checked)}
                          />
                          <Bell className={`h-5 w-5 ${config.in_app ? 'text-green-500' : 'text-gray-300'}`} />
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center space-x-2">
                          <Switch
                            checked={config.sms}
                            onCheckedChange={(checked) => updateAlertConfig(config.user_id, 'sms', checked)}
                          />
                          <MessageSquare className={`h-5 w-5 ${config.sms ? 'text-green-500' : 'text-gray-300'}`} />
                        </div>
                      </TableCell>
                      <TableCell>
                        <Select
                          value={config.min_severity}
                          onValueChange={(value) => updateAlertConfig(config.user_id, 'min_severity', value)}
                        >
                          <SelectTrigger className="w-[180px]">
                            <SelectValue placeholder="Select severity" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="low">Low</SelectItem>
                            <SelectItem value="medium">Medium</SelectItem>
                            <SelectItem value="high">High</SelectItem>
                            <SelectItem value="critical">Critical</SelectItem>
                          </SelectContent>
                        </Select>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Notification Channels</CardTitle>
            <CardDescription>
              Configure email and SMS providers for security alerts
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-6">
              <div className="space-y-2">
                <h3 className="text-lg font-medium">Email Provider</h3>
                <div className="flex items-center space-x-4">
                  <div className="flex-1">
                    <div className="flex items-center space-x-2">
                      <Mail className="h-5 w-5 text-blue-500" />
                      <span className="font-medium">Resend</span>
                    </div>
                    <p className="text-sm text-gray-500">
                      Configured and ready to send email alerts
                    </p>
                  </div>
                  <Badge className="bg-green-500">Active</Badge>
                </div>
              </div>

              <div className="space-y-2">
                <h3 className="text-lg font-medium">SMS Provider</h3>
                <div className="flex items-center space-x-4">
                  <div className="flex-1">
                    <div className="flex items-center space-x-2">
                      <MessageSquare className="h-5 w-5 text-blue-500" />
                      <span className="font-medium">Twilio</span>
                    </div>
                    <p className="text-sm text-gray-500">
                      Configured and ready to send SMS alerts
                    </p>
                  </div>
                  <Badge className="bg-green-500">Active</Badge>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </TabsContent>
    </div>
  )
}
