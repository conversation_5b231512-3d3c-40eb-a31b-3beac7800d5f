'use client'

import { useState, useEffect } from 'react'
import { useSupabase } from '@/lib/supabase/provider'
import { Card, CardContent } from '@/components/ui/card'
import { Switch } from '@/components/ui/switch'
import { Label } from '@/components/ui/label'
import { Button } from '@/components/ui/button'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Bell, Mail, MessageSquare, Calendar, FileText, Users, Clock } from 'lucide-react'

interface NotificationPreferences {
  id: string
  user_id: string
  email_case_updates: boolean
  email_task_reminders: boolean
  email_document_updates: boolean
  email_client_messages: boolean
  email_deadline_reminders: boolean
  email_team_updates: boolean
  in_app_case_updates: boolean
  in_app_task_reminders: boolean
  in_app_document_updates: boolean
  in_app_client_messages: boolean
  in_app_deadline_reminders: boolean
  in_app_team_updates: boolean
  sms_deadline_reminders: boolean
  reminder_time: 'same_day' | '1_day' | '2_days' | '1_week'
}

export function NotificationSettings() {
  const { supabase } = useSupabase()
  const [preferences, setPreferences] = useState<NotificationPreferences | null>(null)
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [message, setMessage] = useState<{ type: 'success' | 'error', text: string } | null>(null)

  useEffect(() => {
    async function loadPreferences() {
      try {
        setLoading(true)

        // Get current user
        const { data: { session } } = await supabase.auth.getSession()
        if (!session?.user) {
          setLoading(false)
          return
        }

        // Get notification preferences
        const { data, error } = await supabase
          .from('notification_preferences')
          .select('*')
          .eq('user_id', session.user.id)
          .single()

        if (error) {
          console.error('Error fetching notification preferences:', error)
          // Create default preferences
          setPreferences({
            id: 'default',
            user_id: session.user.id,
            email_case_updates: true,
            email_task_reminders: true,
            email_document_updates: true,
            email_client_messages: true,
            email_deadline_reminders: true,
            email_team_updates: true,
            in_app_case_updates: true,
            in_app_task_reminders: true,
            in_app_document_updates: true,
            in_app_client_messages: true,
            in_app_deadline_reminders: true,
            in_app_team_updates: true,
            sms_deadline_reminders: false,
            reminder_time: '1_day'
          })
        } else {
          setPreferences(data)
        }
      } catch (_error) {
        console.error('Error loading notification preferences:', error)
      } finally {
        setLoading(false)
      }
    }

    loadPreferences()
  }, [supabase])

  const handleToggle = (field: string, value: boolean) => {
    if (!preferences) return

    setPreferences({
      ...preferences,
      [field]: value
    })
  }

  const handleSelectChange = (field: string, value: string) => {
    if (!preferences) return

    setPreferences({
      ...preferences,
      [field]: value
    })
  }

  const handleSave = async () => {
    if (!preferences) return

    try {
      setSaving(true)
      setMessage(null)

      const { error } = await supabase
        .from('notification_preferences')
        .upsert({
          id: preferences.id !== 'default' ? preferences.id : undefined,
          user_id: preferences.user_id,
          email_case_updates: preferences.email_case_updates,
          email_task_reminders: preferences.email_task_reminders,
          email_document_updates: preferences.email_document_updates,
          email_client_messages: preferences.email_client_messages,
          email_deadline_reminders: preferences.email_deadline_reminders,
          email_team_updates: preferences.email_team_updates,
          in_app_case_updates: preferences.in_app_case_updates,
          in_app_task_reminders: preferences.in_app_task_reminders,
          in_app_document_updates: preferences.in_app_document_updates,
          in_app_client_messages: preferences.in_app_client_messages,
          in_app_deadline_reminders: preferences.in_app_deadline_reminders,
          in_app_team_updates: preferences.in_app_team_updates,
          sms_deadline_reminders: preferences.sms_deadline_reminders,
          reminder_time: preferences.reminder_time
        })

      if (error) {
        console.error('Error saving notification preferences:', error)
        setMessage({ type: 'error', text: 'Failed to save preferences. Please try again.' })
      } else {
        setMessage({ type: 'success', text: 'Notification preferences saved successfully!' })
      }
    } catch (_error) {
      console.error('Error saving notification preferences:', error)
      setMessage({ type: 'error', text: 'An unexpected error occurred. Please try again.' })
    } finally {
      setSaving(false)

      // Clear message after 3 seconds
      setTimeout(() => {
        setMessage(null)
      }, 3000)
    }
  }

  if (loading) {
    return <div className="text-center py-8">Loading notification preferences...</div>
  }

  if (!preferences) {
    return <div className="text-center py-8">Please sign in to view your notification preferences.</div>
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardContent className="pt-6">
          <div className="space-y-6">
            <div>
              <h3 className="text-lg font-medium mb-4 flex items-center">
                <Mail className="h-5 w-5 mr-2 text-primary" />
                Email Notifications
              </h3>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="email_case_updates">Case Updates</Label>
                    <p className="text-sm text-muted-foreground">
                      Receive notifications about case status changes
                    </p>
                  </div>
                  <Switch
                    id="email_case_updates"
                    checked={preferences.email_case_updates}
                    onCheckedChange={(checked) => handleToggle('email_case_updates', checked)}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="email_task_reminders">Task Reminders</Label>
                    <p className="text-sm text-muted-foreground">
                      Receive reminders about upcoming and overdue tasks
                    </p>
                  </div>
                  <Switch
                    id="email_task_reminders"
                    checked={preferences.email_task_reminders}
                    onCheckedChange={(checked) => handleToggle('email_task_reminders', checked)}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="email_document_updates">Document Updates</Label>
                    <p className="text-sm text-muted-foreground">
                      Receive notifications when documents are added or updated
                    </p>
                  </div>
                  <Switch
                    id="email_document_updates"
                    checked={preferences.email_document_updates}
                    onCheckedChange={(checked) => handleToggle('email_document_updates', checked)}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="email_client_messages">Client Messages</Label>
                    <p className="text-sm text-muted-foreground">
                      Receive notifications when clients send messages
                    </p>
                  </div>
                  <Switch
                    id="email_client_messages"
                    checked={preferences.email_client_messages}
                    onCheckedChange={(checked) => handleToggle('email_client_messages', checked)}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="email_deadline_reminders">Deadline Reminders</Label>
                    <p className="text-sm text-muted-foreground">
                      Receive reminders about upcoming deadlines
                    </p>
                  </div>
                  <Switch
                    id="email_deadline_reminders"
                    checked={preferences.email_deadline_reminders}
                    onCheckedChange={(checked) => handleToggle('email_deadline_reminders', checked)}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="email_team_updates">Team Updates</Label>
                    <p className="text-sm text-muted-foreground">
                      Receive notifications about team activity
                    </p>
                  </div>
                  <Switch
                    id="email_team_updates"
                    checked={preferences.email_team_updates}
                    onCheckedChange={(checked) => handleToggle('email_team_updates', checked)}
                  />
                </div>
              </div>
            </div>

            <div>
              <h3 className="text-lg font-medium mb-4 flex items-center">
                <Bell className="h-5 w-5 mr-2 text-primary" />
                In-App Notifications
              </h3>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <Label htmlFor="in_app_case_updates">Case Updates</Label>
                  <Switch
                    id="in_app_case_updates"
                    checked={preferences.in_app_case_updates}
                    onCheckedChange={(checked) => handleToggle('in_app_case_updates', checked)}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <Label htmlFor="in_app_task_reminders">Task Reminders</Label>
                  <Switch
                    id="in_app_task_reminders"
                    checked={preferences.in_app_task_reminders}
                    onCheckedChange={(checked) => handleToggle('in_app_task_reminders', checked)}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <Label htmlFor="in_app_document_updates">Document Updates</Label>
                  <Switch
                    id="in_app_document_updates"
                    checked={preferences.in_app_document_updates}
                    onCheckedChange={(checked) => handleToggle('in_app_document_updates', checked)}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <Label htmlFor="in_app_client_messages">Client Messages</Label>
                  <Switch
                    id="in_app_client_messages"
                    checked={preferences.in_app_client_messages}
                    onCheckedChange={(checked) => handleToggle('in_app_client_messages', checked)}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <Label htmlFor="in_app_deadline_reminders">Deadline Reminders</Label>
                  <Switch
                    id="in_app_deadline_reminders"
                    checked={preferences.in_app_deadline_reminders}
                    onCheckedChange={(checked) => handleToggle('in_app_deadline_reminders', checked)}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <Label htmlFor="in_app_team_updates">Team Updates</Label>
                  <Switch
                    id="in_app_team_updates"
                    checked={preferences.in_app_team_updates}
                    onCheckedChange={(checked) => handleToggle('in_app_team_updates', checked)}
                  />
                </div>
              </div>
            </div>

            <div>
              <h3 className="text-lg font-medium mb-4 flex items-center">
                <MessageSquare className="h-5 w-5 mr-2 text-primary" />
                SMS Notifications
              </h3>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="sms_deadline_reminders">Deadline Reminders</Label>
                    <p className="text-sm text-muted-foreground">
                      Receive SMS reminders about critical deadlines
                    </p>
                  </div>
                  <Switch
                    id="sms_deadline_reminders"
                    checked={preferences.sms_deadline_reminders}
                    onCheckedChange={(checked) => handleToggle('sms_deadline_reminders', checked)}
                  />
                </div>
              </div>
            </div>

            <div>
              <h3 className="text-lg font-medium mb-4 flex items-center">
                <Clock className="h-5 w-5 mr-2 text-primary" />
                Reminder Timing
              </h3>
              <div className="space-y-2">
                <Label htmlFor="reminder_time">When to send reminders</Label>
                <Select
                  value={preferences.reminder_time}
                  onValueChange={(value) => handleSelectChange('reminder_time', value)}
                >
                  <SelectTrigger id="reminder_time" className="w-full sm:w-[250px]">
                    <SelectValue placeholder="Select timing" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="same_day">Same day</SelectItem>
                    <SelectItem value="1_day">1 day before</SelectItem>
                    <SelectItem value="2_days">2 days before</SelectItem>
                    <SelectItem value="1_week">1 week before</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>

          <div className="mt-6 flex justify-end">
            <Button onClick={handleSave} disabled={saving}>
              {saving ? 'Saving...' : 'Save Preferences'}
            </Button>
          </div>

          {message && (
            <div className={`mt-4 p-3 rounded-md ${
              message.type === 'success' ? 'bg-green-50 text-green-800' : 'bg-red-50 text-red-800'
            }`}>
              {message.text}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
