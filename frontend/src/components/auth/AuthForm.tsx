/**
 * Authentication Form Component
 *
 * A reusable form component for login, registration, and password reset
 * using Shadcn UI components.
 */

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import Image from 'next/image';
import Link from 'next/link';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Checkbox } from '@/components/ui/checkbox';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Loader2 } from 'lucide-react';

import { useAuth } from '@/lib/hooks/useAuth';

// Form schemas
const loginSchema = z.object({
  email: z.string().email({ message: 'Please enter a valid email address' }),
  password: z.string().min(8, { message: 'Password must be at least 8 characters' }),
  rememberMe: z.boolean().optional(),
});

const registerSchema = z.object({
  email: z.string().email({ message: 'Please enter a valid email address' }),
  password: z.string().min(8, { message: 'Password must be at least 8 characters' }),
  firstName: z.string().min(2, { message: 'Please enter your first name' }),
  lastName: z.string().min(2, { message: 'Please enter your last name' }),
  acceptTerms: z.literal(true, {
    errorMap: () => ({ message: 'You must accept the terms and conditions' }),
  }),
});

const resetPasswordSchema = z.object({
  email: z.string().email({ message: 'Please enter a valid email address' }),
});

// Form types
type LoginFormValues = z.infer<typeof loginSchema>;
type RegisterFormValues = z.infer<typeof registerSchema>;
type ResetPasswordFormValues = z.infer<typeof resetPasswordSchema>;

// AuthForm props
interface AuthFormProps {
  type: 'login' | 'register' | 'reset';
  redirectPath?: string;
}

export function AuthForm({ type, redirectPath = '/dashboard' }: AuthFormProps) {
  const { signIn, signUp, resetPassword } = useAuth();
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  // Determine which schema to use based on form type
  const schema =
    type === 'login' ? loginSchema :
    type === 'register' ? registerSchema :
    resetPasswordSchema;

  // Initialize form
  const form = useForm({
    resolver: zodResolver(schema),
    defaultValues:
      type === 'login' ? { email: '', password: '', rememberMe: false } :
      type === 'register' ? { email: '', password: '', firstName: '', lastName: '', acceptTerms: false } :
      { email: '' },
  });

  // Handle form submission
  const onSubmit = async (values: LoginFormValues | RegisterFormValues | ResetPasswordFormValues) => {
    setIsLoading(true);
    setError(null);
    setSuccess(null);

    try {
      if (type === 'login') {
        const { email, password } = values as LoginFormValues;
        const { error } = await signIn(email, password);

        if (error) {
          setError(error.message);
        } else {
          router.push(redirectPath);
        }
      } else if (type === 'register') {
        const { email, password, firstName, lastName } = values as RegisterFormValues;
        const { error } = await signUp(email, password, {
          first_name: firstName,
          last_name: lastName
        });

        if (error) {
          setError(error.message);
        } else {
          setSuccess('Registration successful! Please check your email to confirm your account.');
        }
      } else if (type === 'reset') {
        const { email } = values as ResetPasswordFormValues;
        const { error } = await resetPassword(email);

        if (error) {
          setError(error.message);
        } else {
          setSuccess('Password reset instructions sent to your email.');
        }
      }
    } catch (_err) {
      setError('An unexpected error occurred. Please try again.');
      console.error(err);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Card className="w-full max-w-md mx-auto">
      <CardHeader className="space-y-1">
        <div className="flex justify-center mb-6">
          <Image
            src="/images/logo.svg"
            alt="PI Lawyer AI"
            width={120}
            height={40}
            priority
          />
        </div>

        <CardTitle className="text-2xl font-bold text-center">
          {type === 'login' ? 'Sign In' :
           type === 'register' ? 'Create an Account' :
           'Reset Password'}
        </CardTitle>

        <CardDescription className="text-center">
          {type === 'login' ? 'Enter your credentials to access your account' :
           type === 'register' ? 'Fill out the form to create your account' :
           'Enter your email to receive reset instructions'}
        </CardDescription>
      </CardHeader>

      <CardContent>
        {error && (
          <Alert variant="destructive" className="mb-4">
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {success && (
          <Alert className="mb-4 bg-green-50 text-green-800 border-green-200">
            <AlertDescription>{success}</AlertDescription>
          </Alert>
        )}

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="email"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Email</FormLabel>
                  <FormControl>
                    <Input placeholder="<EMAIL>" {...field} disabled={isLoading} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {(type === 'login' || type === 'register') && (
              <FormField
                control={form.control}
                name="password"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Password</FormLabel>
                    <FormControl>
                      <Input type="password" placeholder="••••••••" {...field} disabled={isLoading} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            )}

            {type === 'register' && (
              <>
                <div className="grid grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="firstName"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>First Name</FormLabel>
                        <FormControl>
                          <Input placeholder="First Name" {...field} disabled={isLoading} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="lastName"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Last Name</FormLabel>
                        <FormControl>
                          <Input placeholder="Last Name" {...field} disabled={isLoading} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <FormField
                  control={form.control}
                  name="acceptTerms"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-start space-x-3 space-y-0 py-2">
                      <FormControl>
                        <Checkbox
                          checked={field.value}
                          onCheckedChange={field.onChange}
                          disabled={isLoading}
                        />
                      </FormControl>
                      <div className="space-y-1 leading-none">
                        <FormLabel>
                          I accept the <Link href="/terms" className="text-primary underline">terms and conditions</Link>
                        </FormLabel>
                        <FormMessage />
                      </div>
                    </FormItem>
                  )}
                />
              </>
            )}

            {type === 'login' && (
              <div className="flex items-center justify-between">
                <FormField
                  control={form.control}
                  name="rememberMe"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                      <FormControl>
                        <Checkbox
                          checked={field.value}
                          onCheckedChange={field.onChange}
                          disabled={isLoading}
                        />
                      </FormControl>
                      <div className="space-y-1 leading-none">
                        <FormLabel>Remember me</FormLabel>
                      </div>
                    </FormItem>
                  )}
                />

                <Link href="/auth/reset-password" className="text-sm text-primary underline">
                  Forgot password?
                </Link>
              </div>
            )}

            <Button type="submit" className="w-full" disabled={isLoading}>
              {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              {type === 'login' ? 'Sign In' :
               type === 'register' ? 'Create Account' :
               'Send Reset Link'}
            </Button>
          </form>
        </Form>
      </CardContent>

      <CardFooter className="flex justify-center">
        {type === 'login' ? (
          <p className="text-sm text-muted-foreground">
            Don't have an account?{' '}
            <Link href="/auth/register" className="text-primary underline">
              Sign up
            </Link>
          </p>
        ) : type === 'register' ? (
          <p className="text-sm text-muted-foreground">
            Already have an account?{' '}
            <Link href="/auth/login" className="text-primary underline">
              Sign in
            </Link>
          </p>
        ) : (
          <p className="text-sm text-muted-foreground">
            Remember your password?{' '}
            <Link href="/auth/login" className="text-primary underline">
              Sign in
            </Link>
          </p>
        )}
      </CardFooter>
    </Card>
  );
}
