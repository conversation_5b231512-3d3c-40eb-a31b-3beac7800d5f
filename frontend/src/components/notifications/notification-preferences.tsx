'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { toast } from 'sonner';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';

interface NotificationSetting {
  id: string;
  name: string;
  description: string;
  email: boolean;
  sms: boolean;
  inApp: boolean;
  category: 'case' | 'document' | 'task' | 'security';
}

const defaultSettings: NotificationSetting[] = [
  {
    id: 'case_updates',
    name: 'Case Updates',
    description: 'Receive notifications when cases are updated',
    email: true,
    sms: false,
    inApp: true,
    category: 'case',
  },
  {
    id: 'case_assignments',
    name: 'Case Assignments',
    description: 'Receive notifications when you are assigned to a case',
    email: true,
    sms: true,
    inApp: true,
    category: 'case',
  },
  {
    id: 'document_uploads',
    name: 'Document Uploads',
    description: 'Receive notifications when documents are uploaded',
    email: false,
    sms: false,
    inApp: true,
    category: 'document',
  },
  {
    id: 'document_analysis',
    name: 'Document Analysis',
    description: 'Receive notifications when document analysis is complete',
    email: true,
    sms: false,
    inApp: true,
    category: 'document',
  },
  {
    id: 'task_assignments',
    name: 'Task Assignments',
    description: 'Receive notifications when tasks are assigned to you',
    email: true,
    sms: false,
    inApp: true,
    category: 'task',
  },
  {
    id: 'task_deadlines',
    name: 'Task Deadlines',
    description: 'Receive notifications for upcoming task deadlines',
    email: true,
    sms: true,
    inApp: true,
    category: 'task',
  },
  {
    id: 'security_alerts',
    name: 'Security Alerts',
    description: 'Receive notifications for security events',
    email: true,
    sms: true,
    inApp: true,
    category: 'security',
  },
];

export function NotificationPreferences() {
  const [settings, setSettings] = useState<NotificationSetting[]>(defaultSettings);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    // In a real app, you would fetch the user's notification preferences here
    // For now, we'll just use the default settings
    setSettings(defaultSettings);
  }, []);

  const handleToggle = (id: string, channel: 'email' | 'sms' | 'inApp', value: boolean) => {
    setSettings(
      settings.map((setting) =>
        setting.id === id ? { ...setting, [channel]: value } : setting
      )
    );
  };

  const handleSave = async () => {
    setLoading(true);
    try {
      // In a real app, you would save the settings to the backend here
      await new Promise((resolve) => setTimeout(resolve, 1000)); // Simulate API call
      toast.success('Notification preferences saved');
    } catch (_error) {
      toast.error('Failed to save notification preferences');
      console.error(error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      <Tabs defaultValue="all">
        <TabsList>
          <TabsTrigger value="all">All Notifications</TabsTrigger>
          <TabsTrigger value="case">Case</TabsTrigger>
          <TabsTrigger value="document">Document</TabsTrigger>
          <TabsTrigger value="task">Task</TabsTrigger>
          <TabsTrigger value="security">Security</TabsTrigger>
        </TabsList>

        <TabsContent value="all" className="space-y-4 mt-4">
          <NotificationSettingsList
            settings={settings}
            handleToggle={handleToggle}
          />
        </TabsContent>

        {['case', 'document', 'task', 'security'].map((category) => (
          <TabsContent key={category} value={category} className="space-y-4 mt-4">
            <NotificationSettingsList
              settings={settings.filter(s => s.category === category)}
              handleToggle={handleToggle}
            />
          </TabsContent>
        ))}
      </Tabs>

      <div className="flex justify-end">
        <Button onClick={handleSave} disabled={loading}>
          {loading ? 'Saving...' : 'Save Preferences'}
        </Button>
      </div>
    </div>
  );
}

function NotificationSettingsList({
  settings,
  handleToggle
}: {
  settings: NotificationSetting[],
  handleToggle: (id: string, channel: 'email' | 'sms' | 'inApp', value: boolean) => void
}) {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Notification Channels</CardTitle>
        <CardDescription>
          Choose how you want to receive different types of notifications
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-6">
          <div className="grid grid-cols-4 gap-4 pb-2 border-b">
            <div>Notification</div>
            <div className="text-center">Email</div>
            <div className="text-center">SMS</div>
            <div className="text-center">In-App</div>
          </div>

          {settings.map((setting) => (
            <div key={setting.id} className="grid grid-cols-4 gap-4 items-center">
              <div>
                <Label className="font-medium">{setting.name}</Label>
                <p className="text-sm text-muted-foreground">{setting.description}</p>
              </div>

              <div className="flex justify-center">
                <Switch
                  checked={setting.email}
                  onCheckedChange={(checked) => handleToggle(setting.id, 'email', checked)}
                />
              </div>

              <div className="flex justify-center">
                <Switch
                  checked={setting.sms}
                  onCheckedChange={(checked) => handleToggle(setting.id, 'sms', checked)}
                />
              </div>

              <div className="flex justify-center">
                <Switch
                  checked={setting.inApp}
                  onCheckedChange={(checked) => handleToggle(setting.id, 'inApp', checked)}
                />
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}
