/**
 * Standalone Subscription Status Component for Testing
 *
 * This is a simplified version of the subscription status component
 * that doesn't depend on external services.
 */
import React from 'react';
import { format } from 'date-fns';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Calendar, CheckCircle, AlertCircle, Info } from 'lucide-react';

interface SubscriptionStatusProps {
  subscription: {
    status: string;
    plan?: {
      name: string;
      features?: Record<string, any>;
    };
    currentPeriodStart?: string;
    currentPeriodEnd?: string;
    trialEnd?: string | null;
    canceledAt?: string | null;
  };
}

export default function SubscriptionStatus({ subscription }: SubscriptionStatusProps) {
  const formatDate = (dateString?: string) => {
    if (!dateString) return 'Unknown';
    return format(new Date(dateString), 'MMMM d, yyyy');
  };

  const renderStatusBadge = () => {
    switch (subscription.status) {
      case 'active':
        return <Badge className="bg-green-500">Active</Badge>;
      case 'trialing':
        return <Badge className="bg-blue-500">Trial</Badge>;
      case 'canceled':
        return <Badge className="bg-red-500">Canceled</Badge>;
      case 'inactive':
        return <Badge className="bg-gray-500">Inactive</Badge>;
      default:
        return <Badge className="bg-gray-500">{subscription.status}</Badge>;
    }
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <div className="flex justify-between items-center">
          <CardTitle>Subscription Status</CardTitle>
          {renderStatusBadge()}
        </div>
        <CardDescription>
          {subscription.plan?.name || 'No active plan'}
        </CardDescription>
      </CardHeader>
      <CardContent>
        {subscription.status === 'active' && (
          <div className="space-y-4">
            <div className="flex items-start space-x-3">
              <CheckCircle className="h-5 w-5 text-green-500 mt-0.5" />
              <div>
                <h4 className="font-medium">Active Subscription</h4>
                <p className="text-sm text-gray-600">
                  Your subscription is active and will renew automatically.
                </p>
              </div>
            </div>
            <div className="flex items-start space-x-3">
              <Calendar className="h-5 w-5 text-gray-500 mt-0.5" />
              <div>
                <h4 className="font-medium">Current Period</h4>
                <p className="text-sm text-gray-600">
                  {formatDate(subscription.currentPeriodStart)} - {formatDate(subscription.currentPeriodEnd)}
                </p>
              </div>
            </div>
          </div>
        )}

        {subscription.status === 'trialing' && (
          <div className="bg-blue-50 p-4 rounded-md flex items-start space-x-3">
            <Calendar className="h-5 w-5 text-blue-500 mt-0.5" />
            <div>
              <h4 className="font-medium text-blue-700">Trial Period</h4>
              <p className="text-sm text-blue-600">
                Your trial ends on {subscription.trialEnd ? formatDate(subscription.trialEnd) : 'Unknown'}
              </p>
            </div>
          </div>
        )}

        {subscription.status === 'canceled' && (
          <div className="bg-red-50 p-4 rounded-md flex items-start space-x-3">
            <AlertCircle className="h-5 w-5 text-red-500 mt-0.5" />
            <div>
              <h4 className="font-medium text-red-700">Subscription Canceled</h4>
              <p className="text-sm text-red-600">
                Your subscription has been canceled
                {subscription.canceledAt ? ` on ${formatDate(subscription.canceledAt)}` : ''}.
                Access will end on {formatDate(subscription.currentPeriodEnd)}.
              </p>
              <Button className="mt-2" size="sm">
                <Link href="/settings/subscription/plans">Renew Subscription</Link>
              </Button>
            </div>
          </div>
        )}

        {subscription.status === 'inactive' && (
          <div className="bg-gray-50 p-4 rounded-md flex items-start space-x-3">
            <Info className="h-5 w-5 text-gray-500 mt-0.5" />
            <div>
              <h4 className="font-medium text-gray-700">No Active Subscription</h4>
              <p className="text-sm text-gray-600">
                You don&apos;t have an active subscription. Subscribe to access premium features.
              </p>
              <Button className="mt-2" size="sm">
                <Link href="/settings/subscription/plans">Subscribe Now</Link>
              </Button>
            </div>
          </div>
        )}
      </CardContent>
      <CardFooter className="flex justify-between">
        <Button variant="outline" asChild>
          <Link href="/settings/subscription/plans">View Plans</Link>
        </Button>
        {subscription.status === 'active' && (
          <Button variant="outline" asChild>
            <Link href="/settings/subscription/billing">Manage Billing</Link>
          </Button>
        )}
      </CardFooter>
    </Card>
  );
}
