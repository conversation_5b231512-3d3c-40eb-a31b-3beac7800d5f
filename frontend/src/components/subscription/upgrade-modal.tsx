'use client';

import { useState, useEffect } from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Loader2, Check, ExternalLink, Mail } from 'lucide-react';
import { useSupabase } from '@/lib/supabase/provider';
import { useAuth } from '@/lib/auth/useAuth';
import { SubscriptionService, SubscriptionPlanDTO } from '@/lib/services/subscription-service';
import { getCurrentPlan, getPlanBadgeColor, getPlanDisplayName } from '@/lib/subscription';
import { useToast } from '@/components/ui/use-toast';

interface UpgradeModalProps {
  /** Whether the modal is open */
  open: boolean;
  /** Function to close the modal */
  onClose: () => void;
  /** Optional current plan code to highlight */
  currentPlanCode?: string;
}

interface PlanGroup {
  category: string;
  plans: SubscriptionPlanDTO[];
}

export function UpgradeModal({ open, onClose, currentPlanCode }: UpgradeModalProps) {
  const { supabase } = useSupabase();
  const { profile } = useAuth();
  const { toast } = useToast();
  
  const [plans, setPlans] = useState<SubscriptionPlanDTO[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [currentPlan, setCurrentPlan] = useState<string | null>(null);

  useEffect(() => {
    if (!open) return;

    const fetchData = async () => {
      try {
        setLoading(true);
        setError(null);

        const subscriptionService = new SubscriptionService(supabase);
        
        // Fetch available plans
        const availablePlans = await subscriptionService.getSubscriptionPlans(false);
        setPlans(availablePlans);

        // Get current plan if not provided and tenant ID is available
        if (!currentPlanCode && profile?.tenant_id) {
          const plan = await getCurrentPlan(profile.tenant_id, supabase);
          setCurrentPlan(plan.planCode);
        } else {
          setCurrentPlan(currentPlanCode || null);
        }
      } catch (_err) {
        console.error('Error fetching plans:', err);
        setError(err instanceof Error ? err.message : 'Failed to load plans');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [open, supabase, profile?.tenant_id, currentPlanCode]);

  // Group plans by category (inferred from plan code/name)
  const groupedPlans: PlanGroup[] = plans.reduce((groups, plan) => {
    let category = 'Standard Plans';
    
    if (plan.code.toLowerCase().includes('enterprise') || plan.name.toLowerCase().includes('enterprise')) {
      category = 'Enterprise';
    } else if (plan.code.toLowerCase().includes('bundle') || plan.name.toLowerCase().includes('bundle')) {
      category = 'Bundle Plans';
    } else if (plan.code.toLowerCase().includes('addon') || plan.name.toLowerCase().includes('addon')) {
      category = 'Add-ons';
    }

    const existingGroup = groups.find(g => g.category === category);
    if (existingGroup) {
      existingGroup.plans.push(plan);
    } else {
      groups.push({ category, plans: [plan] });
    }

    return groups;
  }, [] as PlanGroup[]);

  const handleSelectPlan = async (plan: SubscriptionPlanDTO) => {
    // Check if plan has Stripe price ID for direct checkout
    const planFeatures = plan.features as any;
    const stripePriceId = planFeatures?.stripe_price_id;

    if (stripePriceId) {
      // Redirect to Stripe checkout
      try {
        const checkoutUrl = `/api/billing/checkout?price_id=${stripePriceId}`;
        window.location.href = checkoutUrl;
      } catch (_err) {
        toast({
          title: 'Error',
          description: 'Failed to redirect to checkout. Please try again.',
          variant: 'destructive',
        });
      }
    } else {
      // Open email for enterprise/custom plans
      const subject = encodeURIComponent(`Upgrade to ${plan.name}`);
      const body = encodeURIComponent(
        `Hi,\n\nI would like to upgrade to the ${plan.name} plan.\n\nCurrent Plan: ${currentPlan || 'Unknown'}\nTenant ID: ${profile?.tenant_id || 'Unknown'}\n\nPlease contact me with next steps.\n\nThanks!`
      );
      
      window.open(`mailto:<EMAIL>?subject=${subject}&body=${body}`, '_blank');
      
      toast({
        title: 'Email Opened',
        description: 'Please complete your upgrade request via email.',
      });
    }
  };

  const formatPrice = (monthlyPrice: number, yearlyPrice: number) => {
    if (monthlyPrice === 0 && yearlyPrice === 0) {
      return 'Free';
    }
    
    if (monthlyPrice > 0) {
      return `$${monthlyPrice}/month`;
    }
    
    return 'Contact Sales';
  };

  const extractFeatures = (plan: SubscriptionPlanDTO): string[] => {
    const features: string[] = [];
    const planFeatures = plan.features as any;
    
    if (planFeatures && typeof planFeatures === 'object') {
      // Extract feature flags
      for (const [key, value] of Object.entries(planFeatures)) {
        if (key.startsWith('feature_') && value === true) {
          const featureName = key.replace('feature_', '').replace('_', ' ');
          features.push(featureName.charAt(0).toUpperCase() + featureName.slice(1));
        }
      }
      
      // Extract features array
      if (Array.isArray(planFeatures.features)) {
        features.push(...planFeatures.features.map((f: string) => 
          f.replace('_', ' ').charAt(0).toUpperCase() + f.slice(1)
        ));
      }
    }
    
    return features;
  };

  if (loading) {
    return (
      <Dialog open={open} onOpenChange={onClose}>
        <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Upgrade Your Plan</DialogTitle>
            <DialogDescription>
              Loading available plans...
            </DialogDescription>
          </DialogHeader>
          <div className="flex items-center justify-center py-8">
            <Loader2 className="h-8 w-8 animate-spin" />
          </div>
        </DialogContent>
      </Dialog>
    );
  }

  if (error) {
    return (
      <Dialog open={open} onOpenChange={onClose}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>Error</DialogTitle>
            <DialogDescription>
              Failed to load subscription plans: {error}
            </DialogDescription>
          </DialogHeader>
          <div className="flex justify-end">
            <Button onClick={onClose}>Close</Button>
          </div>
        </DialogContent>
      </Dialog>
    );
  }

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="max-w-6xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Upgrade Your Plan</DialogTitle>
          <DialogDescription>
            Choose the plan that best fits your needs. You can upgrade or downgrade at any time.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-8">
          {groupedPlans.map((group) => (
            <div key={group.category}>
              <h3 className="text-lg font-semibold mb-4">{group.category}</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {group.plans.map((plan) => {
                  const isCurrentPlan = currentPlan === plan.code;
                  const features = extractFeatures(plan);
                  const badgeColor = getPlanBadgeColor(plan.code);
                  
                  return (
                    <Card 
                      key={plan.id} 
                      className={`relative ${isCurrentPlan ? 'ring-2 ring-primary' : ''}`}
                    >
                      {isCurrentPlan && (
                        <div className="absolute -top-2 left-4">
                          <Badge className="bg-primary text-primary-foreground">
                            Current Plan
                          </Badge>
                        </div>
                      )}
                      
                      <CardHeader>
                        <div className="flex items-center justify-between">
                          <CardTitle className="text-lg">{plan.name}</CardTitle>
                          <Badge variant="outline" className={badgeColor}>
                            {getPlanDisplayName(plan.code)}
                          </Badge>
                        </div>
                        <CardDescription>{plan.description}</CardDescription>
                        <div className="text-2xl font-bold">
                          {formatPrice(plan.basePriceMonthly, plan.basePriceYearly)}
                        </div>
                      </CardHeader>
                      
                      <CardContent>
                        {features.length > 0 && (
                          <div className="space-y-2 mb-4">
                            <h4 className="font-medium text-sm">Included features:</h4>
                            <ul className="space-y-1">
                              {features.slice(0, 4).map((feature) => (
                                <li key={feature} className="flex items-center text-sm">
                                  <Check className="h-4 w-4 text-green-500 mr-2 flex-shrink-0" />
                                  {feature}
                                </li>
                              ))}
                              {features.length > 4 && (
                                <li className="text-sm text-muted-foreground">
                                  +{features.length - 4} more features
                                </li>
                              )}
                            </ul>
                          </div>
                        )}
                        
                        <Button 
                          className="w-full" 
                          onClick={() => handleSelectPlan(plan)}
                          disabled={isCurrentPlan}
                          variant={isCurrentPlan ? 'outline' : 'default'}
                        >
                          {isCurrentPlan ? (
                            'Current Plan'
                          ) : plan.basePriceMonthly > 0 ? (
                            <>
                              Select Plan
                              <ExternalLink className="h-4 w-4 ml-2" />
                            </>
                          ) : (
                            <>
                              Contact Sales
                              <Mail className="h-4 w-4 ml-2" />
                            </>
                          )}
                        </Button>
                      </CardContent>
                    </Card>
                  );
                })}
              </div>
            </div>
          ))}
        </div>

        <div className="flex justify-end pt-4 border-t">
          <Button variant="outline" onClick={onClose}>
            Close
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}
