'use client';

import { useState, useEffect } from 'react';
// @ts-expect-error - Import from provider
import { useSupabase } from '@/lib/supabase/provider';
import { SubscriptionService } from '@/lib/services/subscription-service';
import { UsageTrackingService } from '@/lib/services/usage-tracking-service';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Button } from '@/components/ui/button';
import { AlertCircle, CheckCircle, Clock, AlertTriangle } from 'lucide-react';
import { formatDistanceToNow } from 'date-fns';
import { UpgradeModal } from './upgrade-modal';

interface SubscriptionStatusProps {
  tenantId: string;
}

export function SubscriptionStatus({ tenantId }: SubscriptionStatusProps) {
  const { supabase } = useSupabase();
  const [loading, setLoading] = useState(true);
  const [subscription, setSubscription] = useState<any>(null);
  const [usageData, setUsageData] = useState<any[]>([]);
  const [error, setError] = useState<string | null>(null);
  const [upgradeModalOpen, setUpgradeModalOpen] = useState(false);

  useEffect(() => {
    async function loadSubscriptionData() {
      try {
        setLoading(true);
        const subscriptionService = new SubscriptionService(supabase);
        const usageTrackingService = new UsageTrackingService(supabase);

        // Get subscription data
        const subscriptionData = await subscriptionService.getTenantSubscription(tenantId);
        setSubscription(subscriptionData);

        // Get usage data for different resource types
        if (subscriptionData) {
          const usageTypes = ['document_upload', 'document_processing', 'api_calls'];
          const usagePromises = usageTypes.map(type =>
            usageTrackingService.getCurrentPeriodUsage(tenantId, type)
          );

          const usageResults = await Promise.all(usagePromises);
          setUsageData(usageResults.filter(Boolean));
        }
      } catch (_err) {
        console.error('Error loading subscription data:', err);
        setError('Failed to load subscription data');
      } finally {
        setLoading(false);
      }
    }

    loadSubscriptionData();
  }, [supabase, tenantId]);

  if (loading) {
    return (
      <Card className="w-full">
        <CardHeader>
          <CardTitle>Subscription Status</CardTitle>
          <CardDescription>Loading subscription information...</CardDescription>
        </CardHeader>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className="w-full border-red-200">
        <CardHeader>
          <CardTitle className="flex items-center">
            <AlertCircle className="mr-2 h-5 w-5 text-red-500" />
            Subscription Status
          </CardTitle>
          <CardDescription>Error: {error}</CardDescription>
        </CardHeader>
      </Card>
    );
  }

  if (!subscription) {
    return (
      <Card className="w-full border-yellow-200">
        <CardHeader>
          <CardTitle className="flex items-center">
            <AlertTriangle className="mr-2 h-5 w-5 text-yellow-500" />
            No Active Subscription
          </CardTitle>
          <CardDescription>Your organization doesn't have an active subscription.</CardDescription>
        </CardHeader>
        <CardFooter>
          <Button variant="default" onClick={() => setUpgradeModalOpen(true)}>
            Start Free Trial
          </Button>
        </CardFooter>
      </Card>
    );
  }

  const isTrialing = subscription.status === 'trialing';
  const isActive = subscription.status === 'active';
  const isCanceled = subscription.status === 'canceled';

  const trialEnd = subscription.trialEnd ? new Date(subscription.trialEnd) : null;
  const currentPeriodEnd = subscription.currentPeriodEnd ? new Date(subscription.currentPeriodEnd) : null;

  const getStatusBadge = () => {
    if (isTrialing) {
      return <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">Trial</Badge>;
    }
    if (isActive) {
      return <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">Active</Badge>;
    }
    if (isCanceled) {
      return <Badge variant="outline" className="bg-red-50 text-red-700 border-red-200">Canceled</Badge>;
    }
    return <Badge variant="outline">Unknown</Badge>;
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <div className="flex justify-between items-center">
          <CardTitle>Subscription Status</CardTitle>
          {getStatusBadge()}
        </div>
        <CardDescription>
          {isTrialing && trialEnd && (
            <div className="flex items-center text-blue-600">
              <Clock className="mr-2 h-4 w-4" />
              Trial ends in {formatDistanceToNow(trialEnd)}
            </div>
          )}
          {isActive && currentPeriodEnd && (
            <div className="flex items-center text-green-600">
              <CheckCircle className="mr-2 h-4 w-4" />
              Renews in {formatDistanceToNow(currentPeriodEnd)}
            </div>
          )}
          {isCanceled && currentPeriodEnd && (
            <div className="flex items-center text-red-600">
              <AlertCircle className="mr-2 h-4 w-4" />
              Access ends in {formatDistanceToNow(currentPeriodEnd)}
            </div>
          )}
        </CardDescription>
      </CardHeader>

      <CardContent>
        <div className="space-y-4">
          <div>
            <div className="font-medium mb-1">Plan: {subscription.planName || 'Unknown'}</div>
            <div className="text-sm text-gray-500">{subscription.billingCycle === 'monthly' ? 'Monthly billing' : 'Annual billing'}</div>
          </div>

          {usageData.length > 0 && (
            <div className="space-y-3 mt-4">
              <div className="font-medium">Resource Usage</div>
              {usageData.map((usage, index) => {
                // Calculate percentage based on quota limits
                // This is a simplified example - in a real app, you'd get the quota limits from the subscription plan
                const quotaLimit = usage.usageType === 'document_upload' ? 100 :
                                  usage.usageType === 'document_processing' ? 50 : 1000;
                const percentUsed = Math.min(100, Math.round((usage.usageCount / quotaLimit) * 100));

                return (
                  <div key={index} className="space-y-1">
                    <div className="flex justify-between text-sm">
                      <span className="capitalize">{usage.usageType.replace('_', ' ')}</span>
                      <span>{usage.usageCount} / {quotaLimit}</span>
                    </div>
                    <Progress value={percentUsed} className="h-2" />
                  </div>
                );
              })}
            </div>
          )}
        </div>
      </CardContent>

      <CardFooter className="flex justify-between">
        {isTrialing && (
          <Button variant="default" onClick={() => setUpgradeModalOpen(true)}>
            Upgrade Plan
          </Button>
        )}
        {isActive && !isCanceled && (
          <Button variant="outline" onClick={() => setUpgradeModalOpen(true)}>
            Manage Subscription
          </Button>
        )}
        {isCanceled && (
          <Button variant="default" onClick={() => setUpgradeModalOpen(true)}>
            Reactivate
          </Button>
        )}
      </CardFooter>

      <UpgradeModal
        open={upgradeModalOpen}
        onClose={() => setUpgradeModalOpen(false)}
        currentPlanCode={subscription?.plan?.code}
      />
    </Card>
  );
}
