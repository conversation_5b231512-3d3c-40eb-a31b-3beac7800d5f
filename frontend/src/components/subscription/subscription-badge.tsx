'use client';

import { useState, useEffect } from 'react';
import { Badge } from '@/components/ui/badge';
import { 
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import { useSupabase } from '@/lib/supabase/provider';
import { useAuth } from '@/lib/auth/useAuth';
import { getCurrentPlan, getPlanBadgeColor, getPlanDisplayName } from '@/lib/subscription';
import type { CurrentPlan } from '@/lib/subscription';
import { Clock, AlertTriangle } from 'lucide-react';

interface SubscriptionBadgeProps {
  /** Optional plan code override - if not provided, will fetch current plan */
  planCode?: string;
  /** Whether the subscription is expired */
  expired?: boolean;
  /** Click handler for the badge */
  onClick?: () => void;
  /** Additional CSS classes */
  className?: string;
}

export function SubscriptionBadge({ 
  planCode: propPlanCode, 
  expired: propExpired, 
  onClick,
  className = '' 
}: SubscriptionBadgeProps) {
  const { supabase } = useSupabase();
  const { profile } = useAuth();
  const [currentPlan, setCurrentPlan] = useState<CurrentPlan | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Use prop values if provided, otherwise use fetched plan data
  const planCode = propPlanCode || currentPlan?.planCode || 'unknown';
  const expired = propExpired !== undefined ? propExpired : currentPlan?.expired || false;
  const planName = currentPlan?.planName || getPlanDisplayName(planCode);
  const features = currentPlan?.features || [];

  useEffect(() => {
    // If plan code is provided as prop, don&apos;t fetch
    if (propPlanCode) {
      setLoading(false);
      return;
    }

    // If no tenant ID, can&apos;t fetch plan
    if (!profile?.tenant_id) {
      setLoading(false);
      return;
    }

    const fetchCurrentPlan = async () => {
      try {
        setLoading(true);
        setError(null);
        
        const plan = await getCurrentPlan(profile.tenant_id, supabase);
        setCurrentPlan(plan);
      } catch (_err) {
        console.error('Error fetching current plan:', err);
        setError(err instanceof Error ? err.message : 'Failed to fetch plan');
      } finally {
        setLoading(false);
      }
    };

    fetchCurrentPlan();
  }, [profile?.tenant_id, supabase, propPlanCode]);

  // Don't render if loading or no plan data
  if (loading || (!propPlanCode && !currentPlan)) {
    return null;
  }

  // Get badge styling
  const badgeColor = getPlanBadgeColor(planCode);
  const isClickable = !!onClick;

  // Determine badge content and styling based on status
  let badgeContent = getPlanDisplayName(planCode);
  let badgeClasses = badgeColor;
  let icon = null;

  if (expired) {
    badgeClasses = 'bg-red-100 text-red-700 border-red-200';
    icon = <AlertTriangle className="h-3 w-3 mr-1" />;
  } else if (currentPlan?.status === 'trialing') {
    badgeClasses = 'bg-blue-100 text-blue-700 border-blue-200';
    icon = <Clock className="h-3 w-3 mr-1" />;
    badgeContent = 'Trial';
  }

  // Add cursor pointer if clickable
  if (isClickable) {
    badgeClasses += ' cursor-pointer hover:opacity-80 transition-opacity';
  }

  const badge = (
    <Badge 
      variant="outline" 
      className={`${badgeClasses} ${className}`}
      onClick={onClick}
    >
      {icon}
      {badgeContent}
    </Badge>
  );

  // If there's an error, show error state
  if (error) {
    return (
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <Badge variant="outline" className="bg-gray-100 text-gray-600 border-gray-200">
              Error
            </Badge>
          </TooltipTrigger>
          <TooltipContent>
            <p>Failed to load subscription: {error}</p>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    );
  }

  // Wrap with tooltip if we have plan data
  if (currentPlan || features.length > 0) {
    return (
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            {badge}
          </TooltipTrigger>
          <TooltipContent>
            <div className="space-y-2">
              <p className="font-medium">{planName}</p>
              {features.length > 0 && (
                <div>
                  <p className="text-sm text-muted-foreground mb-1">Included features:</p>
                  <ul className="text-sm space-y-1">
                    {features.slice(0, 5).map((feature) => (
                      <li key={feature} className="flex items-center">
                        <span className="w-1 h-1 bg-current rounded-full mr-2" />
                        {formatFeatureName(feature)}
                      </li>
                    ))}
                    {features.length > 5 && (
                      <li className="text-muted-foreground">
                        +{features.length - 5} more features
                      </li>
                    )}
                  </ul>
                </div>
              )}
              {expired && (
                <p className="text-sm text-red-600 font-medium">
                  Subscription expired
                </p>
              )}
              {currentPlan?.status === 'trialing' && currentPlan.trialEnd && (
                <p className="text-sm text-blue-600">
                  Trial ends {new Date(currentPlan.trialEnd).toLocaleDateString()}
                </p>
              )}
            </div>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    );
  }

  // Return plain badge if no additional info
  return badge;
}

/**
 * Format feature name for display
 * @param feature - Feature code
 * @returns Human-readable feature name
 */
function formatFeatureName(feature: string): string {
  return feature
    .split('_')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ');
}
