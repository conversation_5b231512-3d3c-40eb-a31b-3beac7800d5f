'use client';

import { useState, useEffect } from 'react';
import { useSupabase } from '@/lib/supabase/provider';
import { useUser } from '@/contexts/UserContext';
import { UsageTrackingService } from '@/lib/services/usage-tracking-service';
import { SubscriptionService } from '@/lib/services/subscription-service';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import {
  Upload,
  FileText,
  Brain,
  MessageSquare,
  FileOutput,
  AlertTriangle,
  ArrowUpRight
} from 'lucide-react';
import Link from 'next/link';

type UsageType = 'document_upload' | 'document_processing' | 'ai_research' |
  'ai_document_analysis' | 'ai_document_generation' | 'ai_intake' | 'api_call';

interface UsageSummary {
  count: number;
  size?: number;
  limit?: number;
  percentUsed: number;
}

interface UsageData {
  document_upload: UsageSummary;
  document_processing: UsageSummary;
  ai_research: UsageSummary;
  ai_document_analysis: UsageSummary;
  ai_document_generation: UsageSummary;
  ai_intake: UsageSummary;
  api_call: UsageSummary;
}

export function UsageDashboard() {
  const { supabase } = useSupabase();
  const { user, tenantId } = useUser(); // Get user and tenantId from UserContext
  const [usageData, setUsageData] = useState<UsageData | null>(null);
  const [loading, setLoading] = useState(true);
  const [quotas, setQuotas] = useState<any>(null);

  useEffect(() => {
    if (!tenantId) return; // Use tenantId directly from context

    const usageTrackingService = new UsageTrackingService(supabase);
    const subscriptionService = new SubscriptionService(supabase);

    // Helper function to get usage summary
    async function getUsageSummary(tid: string) {
      // Get current period usage for each type
      const usageTypes: UsageType[] = [
        'document_upload', 'document_processing', 'ai_research',
        'ai_document_analysis', 'ai_document_generation', 'ai_intake', 'api_call'
      ];

      const usageSummary: Record<UsageType, { count: number, size?: number }> = {
        document_upload: { count: 0 },
        document_processing: { count: 0 },
        ai_research: { count: 0 },
        ai_document_analysis: { count: 0 },
        ai_document_generation: { count: 0 },
        ai_intake: { count: 0 },
        api_call: { count: 0 }
      };

      // Fetch usage data for each type
      for (const type of usageTypes) {
        // We know tid is non-null here because of the guard in useEffect
        const usage = await usageTrackingService.getCurrentPeriodUsage(tid, type);
        if (usage) {
          usageSummary[type as UsageType] = {
            count: usage.usageCount,
            size: usage.resourceSizeBytes || undefined
          };
        }
      }

      return usageSummary;
    }

    async function fetchData() {
      try {
        setLoading(true);
        const summary = await getUsageSummary(tenantId!);

        // Get tenant quotas
        const { data: quotasData, error: quotasError } = await supabase
          .from('tenant_quotas')
          .select('*')
          .eq('tenant_id', tenantId)
          .single();

        if (quotasError) throw quotasError;

        setQuotas(quotasData);

        // Get subscription details for additional limits
        const subscription = await subscriptionService.getTenantSubscription(tenantId!);

        // Calculate limits and percentages
        const usageData: UsageData = {
          document_upload: {
            count: summary.document_upload.count || 0,
            size: summary.document_upload.size || 0,
            limit: quotasData.max_monthly_uploads,
            percentUsed: quotasData.max_monthly_uploads
              ? Math.min(100, Math.round((summary.document_upload.count || 0) / quotasData.max_monthly_uploads * 100))
              : 0
          },
          document_processing: {
            count: summary.document_processing.count || 0,
            limit: quotasData.max_concurrent_processing,
            percentUsed: quotasData.max_concurrent_processing
              ? Math.min(100, Math.round((summary.document_processing.count || 0) / quotasData.max_concurrent_processing * 100))
              : 0
          },
          ai_research: {
            count: summary.ai_research.count || 0,
            limit: subscription?.plan?.features ? (subscription.plan.features as any).maxAiResearch || 1000 : 1000,
            percentUsed: subscription?.plan?.features && (subscription.plan.features as any).maxAiResearch
              ? Math.min(100, Math.round((summary.ai_research.count || 0) / ((subscription.plan.features as any).maxAiResearch as number) * 100))
              : 0
          },
          ai_document_analysis: {
            count: summary.ai_document_analysis.count || 0,
            limit: subscription?.plan?.features ? (subscription.plan.features as any).maxAiAnalysis || 1000 : 1000,
            percentUsed: subscription?.plan?.features && (subscription.plan.features as any).maxAiAnalysis
              ? Math.min(100, Math.round((summary.ai_document_analysis.count || 0) / ((subscription.plan.features as any).maxAiAnalysis as number) * 100))
              : 0
          },
          ai_document_generation: {
            count: summary.ai_document_generation.count || 0,
            limit: subscription?.plan?.features ? (subscription.plan.features as any).maxAiGeneration || 100 : 100,
            percentUsed: subscription?.plan?.features && (subscription.plan.features as any).maxAiGeneration
              ? Math.min(100, Math.round((summary.ai_document_generation.count || 0) / ((subscription.plan.features as any).maxAiGeneration as number) * 100))
              : 0
          },
          ai_intake: {
            count: summary.ai_intake.count || 0,
            limit: subscription?.plan?.features ? (subscription.plan.features as any).maxAiIntake || 100 : 100,
            percentUsed: subscription?.plan?.features && (subscription.plan.features as any).maxAiIntake
              ? Math.min(100, Math.round((summary.ai_intake.count || 0) / ((subscription.plan.features as any).maxAiIntake as number) * 100))
              : 0
          },
          api_call: {
            count: summary.api_call.count || 0,
            limit: subscription?.plan?.features ? (subscription.plan.features as any).maxApiCalls || 10000 : 10000,
            percentUsed: subscription?.plan?.features && (subscription.plan.features as any).maxApiCalls
              ? Math.min(100, Math.round((summary.api_call.count || 0) / ((subscription.plan.features as any).maxApiCalls as number) * 100))
              : 0
          }
        };

        setUsageData(usageData);
      } catch (_error) {
        console.error('Error fetching usage data:', error);
      } finally {
        setLoading(false);
      }
    }

    fetchData();
  }, [supabase, tenantId]);

  const getUsageColor = (percentUsed: number) => {
    if (percentUsed >= 90) return 'bg-red-500';
    if (percentUsed >= 75) return 'bg-amber-500';
    return 'bg-green-500';
  };

  const getUsageIcon = (usageType: UsageType) => {
    switch (usageType) {
      case 'document_upload':
        return <Upload className="h-5 w-5" />;
      case 'document_processing':
        return <FileText className="h-5 w-5" />;
      case 'ai_research':
        return <Brain className="h-5 w-5" />;
      case 'ai_document_analysis':
        return <FileText className="h-5 w-5" />;
      case 'ai_document_generation':
        return <FileOutput className="h-5 w-5" />;
      case 'ai_intake':
        return <MessageSquare className="h-5 w-5" />;
      case 'api_call':
        return <ArrowUpRight className="h-5 w-5" />;
    }
  };

  const formatUsageLabel = (usageType: UsageType) => {
    switch (usageType) {
      case 'document_upload':
        return 'Document Uploads';
      case 'document_processing':
        return 'Document Processing';
      case 'ai_research':
        return 'AI Research';
      case 'ai_document_analysis':
        return 'AI Document Analysis';
      case 'ai_document_generation':
        return 'AI Document Generation';
      case 'ai_intake':
        return 'AI Intake';
      case 'api_call':
        return 'API Calls';
    }
  };

  const formatSize = (bytes?: number) => {
    if (!bytes) return '0 B';

    const units = ['B', 'KB', 'MB', 'GB', 'TB'];
    let size = bytes;
    let unitIndex = 0;

    while (size >= 1024 && unitIndex < units.length - 1) {
      size /= 1024;
      unitIndex++;
    }

    return `${size.toFixed(1)} ${units[unitIndex]}`;
  };

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Resource Usage</CardTitle>
          <CardDescription>Loading usage data...</CardDescription>
        </CardHeader>
      </Card>
    );
  }

  if (!usageData) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Resource Usage</CardTitle>
          <CardDescription>Failed to load usage data</CardDescription>
        </CardHeader>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Resource Usage</CardTitle>
        <CardDescription>
          Current usage for this billing period
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="uploads">
          <TabsList className="mb-4">
            <TabsTrigger value="uploads">Uploads</TabsTrigger>
            <TabsTrigger value="ai">AI Usage</TabsTrigger>
            <TabsTrigger value="api">API Usage</TabsTrigger>
          </TabsList>

          <TabsContent value="uploads" className="space-y-4">
            {/* Document Uploads */}
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Upload className="h-5 w-5 text-muted-foreground" />
                  <span>Document Uploads</span>
                </div>
                <div className="text-sm">
                  {usageData.document_upload.count} / {usageData.document_upload.limit || 'Unlimited'}
                </div>
              </div>
              <Progress
                value={usageData.document_upload.percentUsed}
                className={getUsageColor(usageData.document_upload.percentUsed)}
              />
              <div className="text-xs text-muted-foreground">
                Total size: {formatSize(usageData.document_upload.size)}
              </div>
            </div>

            {/* Document Processing */}
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <FileText className="h-5 w-5 text-muted-foreground" />
                  <span>Document Processing</span>
                </div>
                <div className="text-sm">
                  {usageData.document_processing.count} / {usageData.document_processing.limit || 'Unlimited'}
                </div>
              </div>
              <Progress
                value={usageData.document_processing.percentUsed}
                className={getUsageColor(usageData.document_processing.percentUsed)}
              />
            </div>

            {usageData.document_upload.percentUsed >= 75 && (
              <Alert variant="warning" className="mt-4">
                <AlertTriangle className="h-4 w-4" />
                <AlertTitle>Approaching Upload Limit</AlertTitle>
                <AlertDescription>
                  You are approaching your monthly upload limit. Consider upgrading your plan for additional capacity.
                  <div className="mt-2">
                    <Link href="/settings/subscription/plans">
                      <Button variant="outline" size="sm">View Plans</Button>
                    </Link>
                  </div>
                </AlertDescription>
              </Alert>
            )}
          </TabsContent>

          <TabsContent value="ai" className="space-y-4">
            {/* AI Research */}
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Brain className="h-5 w-5 text-muted-foreground" />
                  <span>AI Research</span>
                </div>
                <div className="text-sm">
                  {usageData.ai_research.count} / {usageData.ai_research.limit || 'Unlimited'}
                </div>
              </div>
              <Progress
                value={usageData.ai_research.percentUsed}
                className={getUsageColor(usageData.ai_research.percentUsed)}
              />
            </div>

            {/* AI Document Analysis */}
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <FileText className="h-5 w-5 text-muted-foreground" />
                  <span>AI Document Analysis</span>
                </div>
                <div className="text-sm">
                  {usageData.ai_document_analysis.count} / {usageData.ai_document_analysis.limit || 'Unlimited'}
                </div>
              </div>
              <Progress
                value={usageData.ai_document_analysis.percentUsed}
                className={getUsageColor(usageData.ai_document_analysis.percentUsed)}
              />
            </div>

            {/* AI Document Generation */}
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <FileOutput className="h-5 w-5 text-muted-foreground" />
                  <span>AI Document Generation</span>
                </div>
                <div className="text-sm">
                  {usageData.ai_document_generation.count} / {usageData.ai_document_generation.limit || 'Unlimited'}
                </div>
              </div>
              <Progress
                value={usageData.ai_document_generation.percentUsed}
                className={getUsageColor(usageData.ai_document_generation.percentUsed)}
              />
            </div>

            {/* AI Intake */}
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <MessageSquare className="h-5 w-5 text-muted-foreground" />
                  <span>AI Intake</span>
                </div>
                <div className="text-sm">
                  {usageData.ai_intake.count} / {usageData.ai_intake.limit || 'Unlimited'}
                </div>
              </div>
              <Progress
                value={usageData.ai_intake.percentUsed}
                className={getUsageColor(usageData.ai_intake.percentUsed)}
              />
            </div>
          </TabsContent>

          <TabsContent value="api" className="space-y-4">
            {/* API Calls */}
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <ArrowUpRight className="h-5 w-5 text-muted-foreground" />
                  <span>API Calls</span>
                </div>
                <div className="text-sm">
                  {usageData.api_call.count} / {usageData.api_call.limit || 'Unlimited'}
                </div>
              </div>
              <Progress
                value={usageData.api_call.percentUsed}
                className={getUsageColor(usageData.api_call.percentUsed)}
              />
            </div>

            <div className="text-sm text-muted-foreground mt-4">
              <p>API usage is tracked across all endpoints and services.</p>
              <p className="mt-2">For detailed API usage analytics, visit the API dashboard.</p>
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
}
