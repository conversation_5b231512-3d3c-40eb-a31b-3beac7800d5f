import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON>Content, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { PriorityBadge } from "@/components/ui/priority-badge";
import { Button } from "@/components/ui/button";
import { Activity } from '@/lib/ml/features';
import { activityPriorityService, PriorityLevel } from '@/lib/services/activity-priority-service';
import { formatDistanceToNow } from 'date-fns';
import { ActivityInsightsPanel } from './activity-insights-panel';
import { InsightAction } from '@/lib/services/insights-service';
import { ChevronDown, ChevronUp } from 'lucide-react';

interface ActivityCardProps {
  activity: Activity;
  onView?: (activity: Activity) => void;
  onComplete?: (activity: Activity) => void;
  onAction?: (action: InsightAction, activity: Activity) => void;
  showInsights?: boolean;
  className?: string;
}

/**
 * Activity Card Component
 *
 * Displays an activity with its priority and relevant information.
 */
export function ActivityCard({
  activity,
  onView,
  onComplete,
  onAction,
  showInsights = true,
  className
}: ActivityCardProps) {
  const [priority, setPriority] = useState<PriorityLevel>(PriorityLevel.MEDIUM);
  const [loading, setLoading] = useState<boolean>(true);
  const [expanded, setExpanded] = useState<boolean>(false);

  // Get activity priority on mount
  useEffect(() => {
    const getPriority = async () => {
      try {
        setLoading(true);
        const predictedPriority = await activityPriorityService.predictPriority(activity);
        setPriority(predictedPriority);
      } catch (_error) {
        console.error('Error getting activity priority:', error);
        setPriority(PriorityLevel.MEDIUM); // Default fallback
      } finally {
        setLoading(false);
      }
    };

    getPriority();
  }, [activity]);

  // Format activity created date
  const formattedDate = formatDistanceToNow(new Date(activity.created_at), { addSuffix: true });

  // Get activity type icon
  const getActivityIcon = () => {
    switch (activity.type) {
      case 'email':
        return (
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
            <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z" />
            <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z" />
          </svg>
        );
      case 'call':
        return (
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
            <path d="M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z" />
          </svg>
        );
      case 'meeting':
        return (
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clipRule="evenodd" />
          </svg>
        );
      case 'document':
        return (
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
            <path fillRule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z" clipRule="evenodd" />
          </svg>
        );
      case 'task':
        return (
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
          </svg>
        );
      default:
        return (
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
            <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-3a1 1 0 00-.867.5 1 1 0 11-1.731-1A3 3 0 0113 8a3.001 3.001 0 01-2 2.83V11a1 1 0 11-2 0v-1a1 1 0 011-1 1 1 0 100-2zm0 8a1 1 0 100-2 1 1 0 000 2z" clipRule="evenodd" />
          </svg>
        );
    }
  };

  // Get activity title
  const getActivityTitle = () => {
    if (activity.metadata?.title) {
      return activity.metadata.title;
    }

    switch (activity.type) {
      case 'email':
        return `Email: ${activity.metadata?.subject || 'No Subject'}`;
      case 'call':
        return `Call with ${activity.metadata?.contact || 'Unknown'}`;
      case 'meeting':
        return `Meeting: ${activity.metadata?.subject || 'No Subject'}`;
      case 'document':
        return `Document: ${activity.metadata?.name || 'Unnamed Document'}`;
      case 'task':
        return `Task: ${activity.metadata?.description || 'No Description'}`;
      default:
        return `${activity.type.charAt(0).toUpperCase() + activity.type.slice(1)}`;
    }
  };

  // Handle insight action
  const handleInsightAction = (action: InsightAction) => {
    if (onAction) {
      onAction(action, activity);
    }
  };

  return (
    <Card className={className}>
      <CardHeader className="flex flex-row items-center justify-between pb-2">
        <div className="flex items-center space-x-2">
          <div className="p-2 rounded-full bg-muted">
            {getActivityIcon()}
          </div>
          <CardTitle className="text-md font-medium">{getActivityTitle()}</CardTitle>
        </div>
        <div className="flex items-center space-x-2">
          {loading ? (
            <div className="h-6 w-16 bg-muted animate-pulse rounded-full" />
          ) : (
            <PriorityBadge priority={priority} />
          )}
          {showInsights && (
            <Button
              variant="ghost"
              size="icon"
              className="h-8 w-8"
              onClick={() => setExpanded(!expanded)}
            >
              {expanded ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
            </Button>
          )}
        </div>
      </CardHeader>
      <CardContent className="pb-2">
        <p className="text-sm text-muted-foreground">
          {activity.metadata?.description || 'No description available'}
        </p>
        <p className="text-xs text-muted-foreground mt-2">
          Created {formattedDate}
        </p>

        {/* Insights Panel (Expandable) */}
        {expanded && showInsights && (
          <div className="mt-4">
            <ActivityInsightsPanel
              activity={activity}
              onAction={handleInsightAction}
            />
          </div>
        )}
      </CardContent>
      <CardFooter className="flex justify-between pt-2">
        {onView && (
          <Button
            variant="outline"
            size="sm"
            onClick={() => onView(activity)}
          >
            View Details
          </Button>
        )}
        {onComplete && (
          <Button
            variant="default"
            size="sm"
            onClick={() => onComplete(activity)}
          >
            Mark Complete
          </Button>
        )}
      </CardFooter>
    </Card>
  );
}
