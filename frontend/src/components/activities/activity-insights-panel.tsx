/**
 * Activity Insights Panel
 *
 * A unified component that displays both ML-generated priority information
 * and AI-generated insights for an activity.
 */
import { useState, useEffect } from 'react';
import { Activity } from '@/lib/ml/features';
import { PriorityLevel } from '@/lib/ml/modelInference';
import { activityPriorityService } from '@/lib/services/activity-priority-service';
import { insightsService, Insight, InsightAction } from '@/lib/services/insights-service';
import { PriorityBadge } from '@/components/ui/priority-badge';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import { AlertCircle, CheckCircle2, Clock, ArrowRight } from 'lucide-react';

interface ActivityInsightsPanelProps {
  activity: Activity;
  showInsights?: boolean;
  onAction?: (action: InsightAction) => void;
}

export function ActivityInsightsPanel({
  activity,
  showInsights = true,
  onAction
}: ActivityInsightsPanelProps) {
  const [priority, setPriority] = useState<PriorityLevel>();
  const [reasoning, setReasoning] = useState<string>('');
  const [insights, setInsights] = useState<Insight[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    async function loadPriorityAndInsights() {
      try {
        setLoading(true);

        // Get priority reasoning from the insights service
        // This combines ML model prediction with explanation
        const priorityInfo = await insightsService.getPriorityReasoning(activity);
        setPriority(priorityInfo.priority);
        setReasoning(priorityInfo.reasoning);

        // Only load insights if requested
        if (showInsights) {
          const activityInsights = await insightsService.getInsightsForActivity(activity);
          setInsights(activityInsights);
        }
      } catch (_error) {
        console.error('Error loading priority and insights:', error);
      } finally {
        setLoading(false);
      }
    }

    loadPriorityAndInsights();
  }, [activity, showInsights]);

  // Handle action click
  const handleActionClick = (action: InsightAction) => {
    if (onAction) {
      onAction(action);
    }
  };

  // Render loading state
  if (loading) {
    return (
      <Card className="w-full">
        <CardHeader className="pb-2">
          <Skeleton className="h-4 w-24" />
          <Skeleton className="h-6 w-full" />
        </CardHeader>
        <CardContent>
          <Skeleton className="h-4 w-full mb-2" />
          <Skeleton className="h-4 w-full mb-2" />
          <Skeleton className="h-4 w-3/4" />
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      {/* Priority Card */}
      <Card className="w-full">
        <CardHeader className="pb-2">
          <div className="flex items-center justify-between">
            <CardTitle className="text-lg">Activity Priority</CardTitle>
            <PriorityBadge priority={priority || PriorityLevel.MEDIUM} />
          </div>
        </CardHeader>
        <CardContent>
          <p className="text-sm text-gray-600">
            {reasoning || 'No priority reasoning available.'}
          </p>
        </CardContent>
      </Card>

      {/* Insights Card - Only show if requested and insights exist */}
      {showInsights && insights.length > 0 && (
        <Card className="w-full">
          <CardHeader className="pb-2">
            <CardTitle className="text-lg">AI Insights</CardTitle>
            <CardDescription>
              Based on ML prioritization and activity context
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {insights.map((insight) => (
              <div key={insight.id} className="border-b pb-3 last:border-b-0 last:pb-0">
                <h4 className="font-medium text-base">{insight.title}</h4>
                <p className="text-sm text-gray-600 mt-1">{insight.description}</p>

                {/* Actions */}
                {insight.actions && insight.actions.length > 0 && (
                  <div className="mt-3 space-y-2">
                    <h5 className="text-xs font-semibold text-gray-500 uppercase">Suggested Actions</h5>
                    <div className="flex flex-wrap gap-2">
                      {insight.actions.map((action, index) => (
                        <Button
                          key={index}
                          variant="outline"
                          size="sm"
                          className="text-xs"
                          onClick={() => handleActionClick(action)}
                        >
                          {action.title}
                          <ArrowRight className="ml-1 h-3 w-3" />
                        </Button>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            ))}
          </CardContent>
        </Card>
      )}

      {/* No Insights Message */}
      {showInsights && insights.length === 0 && !loading && (
        <Card className="w-full bg-gray-50">
          <CardContent className="pt-6">
            <div className="flex items-center justify-center flex-col text-center p-4">
              <Clock className="h-8 w-8 text-gray-400 mb-2" />
              <h4 className="font-medium text-gray-700">No insights available</h4>
              <p className="text-sm text-gray-500 mt-1">
                Insights will be generated automatically for high-priority activities.
              </p>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
