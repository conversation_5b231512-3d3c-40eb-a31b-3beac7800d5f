import { CompatCopilotChat } from "@/lib/utils/chat-compat";
import { useState } from "react";
import { useSupabase } from '@/lib/supabase/provider';
import { ErrorBoundary } from 'react-error-boundary';

export function CopilotChatComponent() {
  const [isOpen, setIsOpen] = useState(false);
  const { supabase } = useSupabase();

  const legalPrompts = [
    "What are the key elements of a personal injury case?",
    "How do I calculate damages for my client?",
    "What's the statute of limitations for this type of case?",
    "Analyze the strength of my current case",
    "Draft a demand letter template",
    "Research similar cases in Texas",
  ];

  const handleError = (error: Error) => {
    console.error('CopilotKit Error:', error);
    console.error('Error Context:', {
      isOpen,
      currentView: "dashboard",
      userRole: "attorney"
    });
  };

  const checkRuntimeStatus = () => {
    try {
      if (!supabase) {
        console.error('Supabase client not initialized');
        return false;
      }
      console.log('CopilotKit Runtime Check: Dependencies loaded successfully');
      return true;
    } catch (_error) {
      console.error('CopilotKit Runtime Check Failed:', error);
      return false;
    }
  };

  const handleOpen = () => {
    const isRunning = checkRuntimeStatus();
    if (isRunning) {
      setIsOpen(true);
    } else {
      console.error('CopilotKit runtime check failed');
    }
  };

  return (
    <div className="fixed bottom-4 right-4">
      <button
        onClick={() => isOpen ? setIsOpen(false) : handleOpen()}
        className="bg-primary text-white rounded-full p-4 shadow-lg hover:bg-primary/90 transition-all"
      >
        {isOpen ? "Close AI Assistant" : "Open AI Assistant"}
      </button>

      {isOpen && (
        <div className="fixed bottom-20 right-4 w-[400px] h-[600px] bg-background border rounded-lg shadow-xl">
          <ErrorBoundary fallback={<div>Something went wrong</div>} onError={handleError}>
            <CompatCopilotChat
              className="w-full h-full"
              displayName="AI Legal Assistant"
              placeholder="Ask me anything about your cases..."
              suggestions={legalPrompts}
              context={{
                currentView: "dashboard",
                userRole: "attorney",
                jurisdiction: "Texas",
                practiceArea: "Personal Injury",
              }}
              initialMessage="Hello! I'm your AI Legal Assistant, specialized in Texas personal injury law. I can help you with case analysis, document drafting, legal research, and more. What would you like help with today?"
              onError={handleError}
              agent="supervisor_agent"
            />
          </ErrorBoundary>
        </div>
      )}
    </div>
  );
}
