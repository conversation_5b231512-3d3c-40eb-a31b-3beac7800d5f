'use client'

import { useState } from 'react'
import { zodResolver } from '@hookform/resolvers/zod'
import { useForm } from 'react-hook-form'
import * as z from 'zod'
// Use a simple date formatter instead of date-fns to avoid compatibility issues
const formatDate = (date: Date): string => {
  return date.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  });
}
import { toast } from 'sonner'
import { useStaffIntakeAction } from '@/lib/actions/staff-intake-actions'

import { Button } from '@/components/ui/button'
import { Checkbox } from '@/components/ui/checkbox'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { cn } from '@/lib/utils'
import { useSupabase } from '@/lib/supabase/provider'

// Schema for client intake form
const intakeFormSchema = z.object({
  // Client Information (Required for all case types)
  firstName: z.string().min(1, 'First name is required'),
  middleName: z.string().optional(),
  lastName: z.string().min(1, 'Last name is required'),
  dateOfBirth: z.date({
    required_error: 'Date of birth is required',
  }),
  ssn: z.string().optional(), // Social Security Number
  email: z.string().email('Invalid email address'),
  phone: z.string().min(10, 'Phone number must be at least 10 digits'),
  address: z.string().min(1, 'Address is required'),
  city: z.string().min(1, 'City is required'),
  state: z.string().min(1, 'State is required'),
  zipCode: z.string().min(5, 'ZIP code must be at least 5 digits'),

  // Employment Information
  occupation: z.string().optional(),
  employer: z.string().optional(),
  workStatus: z.string().optional(), // e.g., full-time, part-time, unemployed

  // Case Information
  practiceArea: z.enum(['personal_injury', 'criminal_defense', 'family_law']),
  caseType: z.string().min(1, 'Case type is required'),
  caseDescription: z.string().min(10, 'Please provide a brief description'),

  // Other Party Information (Names only, for conflict check)
  otherParties: z.array(
    z.object({
      name: z.string().optional(),
      relationship: z.string().optional(), // e.g., defendant, spouse, opposing party
    })
  ).default([{ name: '', relationship: '' }]),

  // Previous Legal Consultation
  previousAttorneyConsultation: z.boolean().default(false),
  previousAttorneyDetails: z.string().optional(),

  // Case Status
  intakePriority: z.enum(['high', 'medium', 'low']).default('medium'),

  // Internal Notes
  internalNotes: z.string().optional(),
});

// Practice area and case type options
const practiceAreas = [
  { value: 'personal_injury', label: 'Personal Injury & Civil Litigation' },
  { value: 'criminal_defense', label: 'Criminal Defense' },
  { value: 'family_law', label: 'Family Law' },
];

const caseTypes = {
  personal_injury: [
    { value: 'auto_accident', label: 'Auto Accident' },
    { value: 'slip_fall', label: 'Slip and Fall' },
    { value: 'workplace', label: 'Workplace Injury' },
    { value: 'medical_malpractice', label: 'Medical Malpractice' },
    { value: 'product_liability', label: 'Product Liability' },
    { value: 'wrongful_death', label: 'Wrongful Death' },
    { value: 'other_injury', label: 'Other Personal Injury' },
  ],
  criminal_defense: [
    { value: 'dwi_dui', label: 'DWI/DUI' },
    { value: 'drug_charges', label: 'Drug Charges' },
    { value: 'assault', label: 'Assault' },
    { value: 'theft', label: 'Theft' },
    { value: 'white_collar', label: 'White Collar Crime' },
    { value: 'other_criminal', label: 'Other Criminal Charges' },
  ],
  family_law: [
    { value: 'divorce', label: 'Divorce' },
    { value: 'child_custody', label: 'Child Custody' },
    { value: 'child_support', label: 'Child Support' },
    { value: 'adoption', label: 'Adoption' },
    { value: 'protective_order', label: 'Protective Order' },
    { value: 'other_family', label: 'Other Family Law' },
  ],
};

export function StaffIntakeForm() {
  const { supabase } = useSupabase()
  const [isSubmitting, setIsSubmitting] = useState(false)

  // Define the form
  const form = useForm<z.infer<typeof intakeFormSchema>>({
    resolver: zodResolver(intakeFormSchema),
    defaultValues: {
      firstName: '',
      middleName: '',
      lastName: '',
      ssn: '',
      email: '',
      phone: '',
      address: '',
      city: '',
      state: '',
      zipCode: '',
      occupation: '',
      employer: '',
      workStatus: '',
      practiceArea: 'personal_injury',
      caseType: '',
      caseDescription: '',
      otherParties: [{ name: '', relationship: '' }],
      previousAttorneyConsultation: false,
      previousAttorneyDetails: '',
      intakePriority: 'medium',
      internalNotes: '',
    },
  })

  // Register AI action for submitting the form using our compatibility layer
  useStaffIntakeAction(async ({ clientName, practiceArea, caseType, details }) => {
    toast.info(`Creating intake for ${clientName}`)
    // Populate form fields with AI-supplied data
    form.setValue('firstName', clientName.split(' ')[0])
    form.setValue('lastName', clientName.split(' ').slice(-1)[0])
    form.setValue('practiceArea', practiceArea as any)
    form.setValue('caseType', caseType)
    form.setValue('caseDescription', details)
  })

  // State for managing dynamic other parties
  const [otherParties, setOtherParties] = useState([0]);

  // Function to add another party
  const addOtherParty = () => {
    const currentValues = form.getValues().otherParties || [];
    form.setValue('otherParties', [...currentValues, { name: '', relationship: '' }]);
    setOtherParties([...otherParties, otherParties.length]);
  };

  // Function to remove a party
  const removeOtherParty = (index: number) => {
    const currentValues = form.getValues().otherParties || [];
    if (currentValues.length > 1) {
      const newValues = [...currentValues];
      newValues.splice(index, 1);
      form.setValue('otherParties', newValues);
      setOtherParties(otherParties.filter((_, i) => i !== index));
    }
  };

  // Get available case types based on selected practice area
  const getAvailableCaseTypes = () => {
    const practiceArea = form.watch('practiceArea');
    return caseTypes[practiceArea] || [];
  };

  // Submit handler
  async function onSubmit(data: z.infer<typeof intakeFormSchema>) {
    try {
      setIsSubmitting(true)

      // Log form data for debugging
      console.log('Form data to submit:', data)

      // Start a transaction using Supabase's RPC function
      const { data: result, error } = await supabase.rpc('create_client_intake', {
        p_client_data: {
          // Client personal information
          first_name: data.firstName,
          middle_name: data.middleName || null,
          last_name: data.lastName,
          date_of_birth: data.dateOfBirth,
          ssn: data.ssn || null,
          client_type: 'individual',
          email: data.email,
          phone_primary: data.phone,
          phone_secondary: null,

          // Address information (stored as JSONB)
          address: {
            street: data.address,
            city: data.city,
            state: data.state,
            zip: data.zipCode
          },

          // Employment information (using new dedicated columns)
          occupation: data.occupation || null,
          employer: data.employer || null,
          work_status: data.workStatus || null,

          // Status and metadata
          status: 'active',
          intake_date: new Date().toISOString().split('T')[0],
          conflict_check_status: 'pending'
        },
        p_case_data: {
          // Case information
          title: `${data.firstName} ${data.lastName} - ${data.caseType}`,
          description: data.caseDescription,

          // Using new dedicated columns
          practice_area: data.practiceArea,
          case_type: data.caseType,
          intake_priority: data.intakePriority,

          // Status information
          status: 'intake',
          previously_consulted: data.previousAttorneyConsultation,

          // Additional information in metadata
          metadata: {
            previous_attorney_details: data.previousAttorneyDetails || null,
            internal_notes: data.internalNotes || null
          }
        },
        p_other_parties: data.otherParties.filter(party => party.name && party.name.trim() !== '').map(party => {
          // Split name into first and last
          const nameParts = (party.name || '').split(' ');
          const lastName = nameParts.pop() || '';
          const firstName = nameParts.join(' ');

          return {
            first_name: firstName,
            last_name: lastName,
            type: 'other',
            role: party.relationship || 'other',
            address: {} // Required field, but can be empty object
          };
        })
      });

      if (error) {
        throw new Error(`Transaction failed: ${error.message}`);
      }

      toast.success('Client intake submitted successfully!');
      console.log('Transaction result:', result);

      // Reset form
      form.reset();
    } catch (_error) {
      console.error('Error submitting client intake:', error);
      toast.error(`Failed to submit client intake: ${error instanceof Error ? error.message : 'Please try again.'}`);
    } finally {
      setIsSubmitting(false);
    }
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
        {/* Client Information Section */}
        <div className="space-y-4">
          <h3 className="text-lg font-medium">Client Information</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <FormField
              control={form.control}
              name="firstName"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>First Name</FormLabel>
                  <FormControl>
                    <Input placeholder="First name" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="middleName"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Middle Name</FormLabel>
                  <FormControl>
                    <Input placeholder="Middle name (optional)" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="lastName"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Last Name</FormLabel>
                  <FormControl>
                    <Input placeholder="Last name" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <FormField
              control={form.control}
              name="dateOfBirth"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Date of Birth</FormLabel>
                  <FormControl>
                    <Input
                      type="date"
                      {...field}
                      value={field.value ? field.value.toISOString().split('T')[0] : ''}
                      onChange={(e) => {
                        if (e.target.value) {
                          field.onChange(new Date(e.target.value));
                        }
                      }}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="ssn"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Social Security Number</FormLabel>
                  <FormControl>
                    <Input placeholder="XXX-XX-XXXX (for identification purposes)" {...field} />
                  </FormControl>
                  <FormDescription>
                    Helps identify you as a client. Securely stored.
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <FormField
              control={form.control}
              name="email"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Email</FormLabel>
                  <FormControl>
                    <Input type="email" placeholder="<EMAIL>" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="phone"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Phone</FormLabel>
                  <FormControl>
                    <Input placeholder="(*************" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          <FormField
            control={form.control}
            name="address"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Address</FormLabel>
                <FormControl>
                  <Input placeholder="123 Main St" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <FormField
              control={form.control}
              name="city"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>City</FormLabel>
                  <FormControl>
                    <Input placeholder="City" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="state"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>State</FormLabel>
                  <FormControl>
                    <Input placeholder="State" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="zipCode"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>ZIP Code</FormLabel>
                  <FormControl>
                    <Input placeholder="12345" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          {/* Employment Information */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 pt-2">
            <FormField
              control={form.control}
              name="occupation"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Occupation</FormLabel>
                  <FormControl>
                    <Input placeholder="Current occupation" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="employer"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Employer</FormLabel>
                  <FormControl>
                    <Input placeholder="Current employer" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="workStatus"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Work Status</FormLabel>
                  <Select onValueChange={field.onChange} defaultValue={field.value}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select work status" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="full_time">Full-time</SelectItem>
                      <SelectItem value="part_time">Part-time</SelectItem>
                      <SelectItem value="unemployed">Unemployed</SelectItem>
                      <SelectItem value="retired">Retired</SelectItem>
                      <SelectItem value="student">Student</SelectItem>
                      <SelectItem value="disabled">Disabled</SelectItem>
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
        </div>

        {/* Case Information Section */}
        <div className="space-y-4 pt-4 border-t">
          <h3 className="text-lg font-medium">Case Information</h3>
          <FormDescription>
            Select the practice area and provide basic case details for conflict check.
          </FormDescription>

          <FormField
            control={form.control}
            name="practiceArea"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Practice Area</FormLabel>
                <Select onValueChange={field.onChange} defaultValue={field.value}>
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Select practice area" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {practiceAreas.map((area) => (
                      <SelectItem key={area.value} value={area.value}>
                        {area.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="caseType"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Type of Case</FormLabel>
                <Select onValueChange={field.onChange} defaultValue={field.value}>
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Select case type" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {getAvailableCaseTypes().map((type) => (
                      <SelectItem key={type.value} value={type.value}>
                        {type.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="caseDescription"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Brief Case Description</FormLabel>
                <FormControl>
                  <Textarea
                    placeholder="Please provide a brief description of the case..."
                    className="min-h-24"
                    {...field}
                  />
                </FormControl>
                <FormDescription>
                  Only provide essential details needed for conflict check.
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="intakePriority"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Intake Priority</FormLabel>
                <Select onValueChange={field.onChange} defaultValue={field.value}>
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Select priority" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    <SelectItem value="high">High</SelectItem>
                    <SelectItem value="medium">Medium</SelectItem>
                    <SelectItem value="low">Low</SelectItem>
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        {/* Other Party Information */}
        <div className="space-y-4 pt-4 border-t">
          <h3 className="text-lg font-medium">Other Parties</h3>
          <FormDescription>
            List any parties that may create a conflict of interest (opposing parties, family members, etc.)
          </FormDescription>

          {otherParties.map((_, index) => (
            <div key={index} className="space-y-4 p-4 border rounded-md">
              <div className="flex justify-between items-center">
                <h4 className="font-medium">Other Party {index + 1}</h4>
                {index > 0 && (
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={() => removeOtherParty(index)}
                  >
                    Remove
                  </Button>
                )}
              </div>

              <FormField
                control={form.control}
                name={`otherParties.${index}.name`}
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Name</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="Full name of the person"
                        {...field}
                      />
                    </FormControl>
                    <FormDescription>
                      Enter the full legal name if known
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name={`otherParties.${index}.relationship`}
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Relationship to Case</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="e.g., Defendant, Spouse, Opposing Party"
                        {...field}
                      />
                    </FormControl>
                    <FormDescription>
                      Specify their role or relationship to the case
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
          ))}

          <Button
            type="button"
            variant="outline"
            onClick={addOtherParty}
            className="w-full"
          >
            Add Another Party
          </Button>
        </div>

        {/* Previous Legal Consultation */}
        <div className="space-y-4 pt-4 border-t">
          <h3 className="text-lg font-medium">Previous Legal Consultation</h3>

          <FormField
            control={form.control}
            name="previousAttorneyConsultation"
            render={({ field }) => (
              <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                <FormControl>
                  <Checkbox
                    checked={field.value}
                    onCheckedChange={field.onChange}
                  />
                </FormControl>
                <div className="space-y-1 leading-none">
                  <FormLabel>
                    Has the client previously consulted with another attorney about this matter?
                  </FormLabel>
                  <FormDescription>
                    This information is important for conflict checks
                  </FormDescription>
                </div>
              </FormItem>
            )}
          />

          {form.watch('previousAttorneyConsultation') && (
            <FormField
              control={form.control}
              name="previousAttorneyDetails"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Previous Attorney Details</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="Name of previous attorney or law firm"
                      {...field}
                    />
                  </FormControl>
                  <FormDescription>
                    Provide the name of the attorney or law firm previously consulted
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
          )}
        </div>

        {/* Case Status and Internal Notes */}
        <div className="space-y-4 pt-4 border-t">
          <h3 className="text-lg font-medium">Case Status & Internal Notes</h3>

          <FormField
            control={form.control}
            name="intakePriority"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Intake Priority</FormLabel>
                <Select onValueChange={field.onChange} defaultValue={field.value}>
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Select priority" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    <SelectItem value="high">High Priority</SelectItem>
                    <SelectItem value="medium">Medium Priority</SelectItem>
                    <SelectItem value="low">Low Priority</SelectItem>
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="internalNotes"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Internal Notes</FormLabel>
                <FormControl>
                  <Textarea
                    placeholder="Any additional notes for internal use only..."
                    className="min-h-24"
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <Button type="submit" className="w-full" disabled={isSubmitting}>
          {isSubmitting ? 'Submitting...' : 'Submit Client Intake'}
        </Button>
      </form>
    </Form>
  )
}
