'use client';

import React, { useState } from 'react';
import { usePrompts, Prompt, PromptVersion, CreatePromptData } from '@/hooks/admin/usePrompts';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Drawer,
  DrawerClose,
  DrawerContent,
  DrawerDescription,
  DrawerFooter,
  DrawerHeader,
  DrawerTitle,
  DrawerTrigger,
} from '@/components/ui/drawer';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Loader2, Plus, Pencil, Trash2, Tag, FileText, Clock, Check, X } from 'lucide-react';
import { format } from 'date-fns';

export default function PromptsPage() {
  const { prompts, isLoading, createPrompt, updatePrompt, deletePrompt } = usePrompts();
  const [isDrawerOpen, setIsDrawerOpen] = useState(false);
  const [editingPrompt, setEditingPrompt] = useState<Prompt | null>(null);
  const [promptToDelete, setPromptToDelete] = useState<Prompt | null>(null);
  
  // Form state
  const [formData, setFormData] = useState<{
    key: string;
    description: string;
    tags: string;
    content: string;
    is_active: boolean;
  }>({
    key: '',
    description: '',
    tags: '',
    content: '',
    is_active: true,
  });

  // Handle form input changes
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  // Handle checkbox changes
  const handleCheckboxChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, checked } = e.target;
    setFormData(prev => ({ ...prev, [name]: checked }));
  };

  // Open drawer for creating a new prompt
  const handleNewPrompt = () => {
    setEditingPrompt(null);
    setFormData({
      key: '',
      description: '',
      tags: '',
      content: '',
      is_active: true,
    });
    setIsDrawerOpen(true);
  };

  // Open drawer for editing an existing prompt
  const handleEditPrompt = (prompt: Prompt) => {
    setEditingPrompt(prompt);
    setFormData({
      key: prompt.key,
      description: prompt.description || '',
      tags: prompt.tags ? prompt.tags.join(', ') : '',
      content: prompt.latestVersion?.content || '',
      is_active: prompt.is_active,
    });
    setIsDrawerOpen(true);
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    // Parse tags
    const tags = formData.tags
      ? formData.tags.split(',').map(tag => tag.trim()).filter(Boolean)
      : null;
    
    if (editingPrompt) {
      // Update existing prompt
      await updatePrompt({
        id: editingPrompt.id,
        key: formData.key,
        description: formData.description || undefined,
        tags: tags || undefined,
        is_active: formData.is_active,
      });
    } else {
      // Create new prompt
      const promptData: CreatePromptData = {
        key: formData.key,
        description: formData.description || undefined,
        tags: tags || undefined,
        content: formData.content,
      };
      await createPrompt(promptData);
    }
    
    setIsDrawerOpen(false);
  };

  // Handle prompt deletion
  const handleDeleteConfirm = async () => {
    if (promptToDelete) {
      await deletePrompt(promptToDelete.id);
      setPromptToDelete(null);
    }
  };

  // Format date for display
  const formatDate = (dateString: string) => {
    try {
      return format(new Date(dateString), 'MMM d, yyyy h:mm a');
    } catch (_e) {
      return dateString;
    }
  };

  return (
    <div className="container mx-auto py-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold">Prompt Management</h1>
        <Button onClick={handleNewPrompt}>
          <Plus className="mr-2 h-4 w-4" />
          New Prompt
        </Button>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Prompts</CardTitle>
          <CardDescription>
            Manage prompt templates used throughout the application
          </CardDescription>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="flex justify-center items-center h-40">
              <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Key</TableHead>
                  <TableHead>Description</TableHead>
                  <TableHead>Tags</TableHead>
                  <TableHead>Last Updated</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {prompts.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={6} className="text-center py-8 text-muted-foreground">
                      No prompts found. Create your first prompt to get started.
                    </TableCell>
                  </TableRow>
                ) : (
                  prompts.map((prompt) => (
                    <TableRow key={prompt.id}>
                      <TableCell className="font-medium">{prompt.key}</TableCell>
                      <TableCell>{prompt.description || '-'}</TableCell>
                      <TableCell>
                        {prompt.tags && prompt.tags.length > 0 ? (
                          <div className="flex flex-wrap gap-1">
                            {prompt.tags.map((tag) => (
                              <Badge key={tag} variant="outline">
                                {tag}
                              </Badge>
                            ))}
                          </div>
                        ) : (
                          '-'
                        )}
                      </TableCell>
                      <TableCell>{formatDate(prompt.updated_at)}</TableCell>
                      <TableCell>
                        {prompt.is_active ? (
                          <Badge variant="default" className="bg-green-100 text-green-800 border-green-200">
                            <Check className="mr-1 h-3 w-3" />
                            Active
                          </Badge>
                        ) : (
                          <Badge variant="destructive" className="bg-red-100 text-red-800">
                            <X className="mr-1 h-3 w-3" />
                            Inactive
                          </Badge>
                        )}
                      </TableCell>
                      <TableCell className="text-right">
                        <div className="flex justify-end gap-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleEditPrompt(prompt)}
                          >
                            <Pencil className="h-4 w-4" />
                          </Button>
                          <AlertDialog>
                            <AlertDialogTrigger asChild>
                              <Button
                                variant="outline"
                                size="sm"
                                className="text-red-500 hover:text-red-700"
                                onClick={() => setPromptToDelete(prompt)}
                              >
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            </AlertDialogTrigger>
                            <AlertDialogContent>
                              <AlertDialogHeader>
                                <AlertDialogTitle>Delete Prompt</AlertDialogTitle>
                                <AlertDialogDescription>
                                  Are you sure you want to delete the prompt "{promptToDelete?.key}"? 
                                  This action cannot be undone and will delete all versions of this prompt.
                                </AlertDialogDescription>
                              </AlertDialogHeader>
                              <AlertDialogFooter>
                                <AlertDialogCancel onClick={() => setPromptToDelete(null)}>
                                  Cancel
                                </AlertDialogCancel>
                                <AlertDialogAction
                                  onClick={handleDeleteConfirm}
                                  className="bg-red-500 hover:bg-red-700"
                                >
                                  Delete
                                </AlertDialogAction>
                              </AlertDialogFooter>
                            </AlertDialogContent>
                          </AlertDialog>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>

      {/* Drawer for creating/editing prompts */}
      <Drawer open={isDrawerOpen} onOpenChange={setIsDrawerOpen}>
        <DrawerContent>
          <form onSubmit={handleSubmit}>
            <DrawerHeader>
              <DrawerTitle>
                {editingPrompt ? 'Edit Prompt' : 'Create New Prompt'}
              </DrawerTitle>
              <DrawerDescription>
                {editingPrompt
                  ? 'Update the prompt details below'
                  : 'Fill in the details to create a new prompt'}
              </DrawerDescription>
            </DrawerHeader>
            <div className="px-4 py-2">
              <Tabs defaultValue="details">
                <TabsList className="mb-4">
                  <TabsTrigger value="details">Details</TabsTrigger>
                  <TabsTrigger value="content">Content</TabsTrigger>
                </TabsList>
                <TabsContent value="details">
                  <div className="grid gap-4 py-4">
                    <div className="grid grid-cols-4 items-center gap-4">
                      <Label htmlFor="key" className="text-right">
                        Key
                      </Label>
                      <Input
                        id="key"
                        name="key"
                        value={formData.key}
                        onChange={handleInputChange}
                        className="col-span-3"
                        required
                      />
                    </div>
                    <div className="grid grid-cols-4 items-center gap-4">
                      <Label htmlFor="description" className="text-right">
                        Description
                      </Label>
                      <Input
                        id="description"
                        name="description"
                        value={formData.description}
                        onChange={handleInputChange}
                        className="col-span-3"
                      />
                    </div>
                    <div className="grid grid-cols-4 items-center gap-4">
                      <Label htmlFor="tags" className="text-right">
                        Tags
                      </Label>
                      <Input
                        id="tags"
                        name="tags"
                        value={formData.tags}
                        onChange={handleInputChange}
                        className="col-span-3"
                        placeholder="tag1, tag2, tag3"
                      />
                    </div>
                    <div className="grid grid-cols-4 items-center gap-4">
                      <Label htmlFor="is_active" className="text-right">
                        Active
                      </Label>
                      <div className="col-span-3 flex items-center">
                        <input
                          type="checkbox"
                          id="is_active"
                          name="is_active"
                          checked={formData.is_active}
                          onChange={handleCheckboxChange}
                          className="mr-2 h-4 w-4"
                        />
                        <Label htmlFor="is_active">Prompt is active and available for use</Label>
                      </div>
                    </div>
                  </div>
                </TabsContent>
                <TabsContent value="content">
                  <div className="grid gap-4 py-4">
                    <div className="grid grid-cols-1 gap-4">
                      <Label htmlFor="content">
                        Prompt Content
                      </Label>
                      <Textarea
                        id="content"
                        name="content"
                        value={formData.content}
                        onChange={handleInputChange}
                        className="min-h-[300px] font-mono"
                        placeholder="Enter prompt template content..."
                        required={!editingPrompt}
                      />
                      <p className="text-sm text-muted-foreground">
                        Use {`{{variable}}`} syntax for template variables.
                      </p>
                    </div>
                  </div>
                </TabsContent>
              </Tabs>
            </div>
            <DrawerFooter>
              <Button type="submit">
                {editingPrompt ? 'Save Changes' : 'Create Prompt'}
              </Button>
              <DrawerClose asChild>
                <Button variant="outline">Cancel</Button>
              </DrawerClose>
            </DrawerFooter>
          </form>
        </DrawerContent>
      </Drawer>
    </div>
  );
}
