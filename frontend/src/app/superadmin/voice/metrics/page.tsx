'use client';

import React, { useState, useEffect } from 'react';
import { SuperAdminGuard } from '@/components/auth/SuperAdminGuard';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { RefreshCw, Phone, Clock, AlertTriangle, Activity } from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';

interface VoiceMetrics {
  activeCalls: number;
  queueDepth: number;
  p95Latency: number;
  callRejects: number;
  lastUpdated: string;
}

interface PrometheusMetric {
  metric: Record<string, string>;
  value: [number, string];
}

interface PrometheusResponse {
  status: string;
  data: {
    resultType: string;
    result: PrometheusMetric[];
  };
}

export default function VoiceMetricsPage() {
  const { toast } = useToast();
  const [metrics, setMetrics] = useState<VoiceMetrics>({
    activeCalls: 0,
    queueDepth: 0,
    p95Latency: 0,
    callRejects: 0,
    lastUpdated: new Date().toISOString(),
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const grafanaUrl = process.env.NEXT_PUBLIC_GRAFANA_URL;
  const prometheusUrl = process.env.NEXT_PUBLIC_PROMETHEUS_PROXY_URL || '/api/prometheus';

  const fetchPrometheusMetrics = async () => {
    try {
      setLoading(true);
      setError(null);

      // Fetch multiple metrics in parallel
      const [activeCallsRes, queueDepthRes, latencyRes, rejectsRes] = await Promise.all([
        fetch(`${prometheusUrl}/query?query=sum(avr_active_calls_total)`),
        fetch(`${prometheusUrl}/query?query=sum(avr_queue_depth_total)`),
        fetch(`${prometheusUrl}/query?query=histogram_quantile(0.95, sum(rate(avr_call_duration_seconds_bucket[5m])) by (le))`),
        fetch(`${prometheusUrl}/query?query=sum(rate(avr_call_rejects_total[5m]))`),
      ]);

      const [activeCalls, queueDepth, latency, rejects] = await Promise.all([
        activeCallsRes.json() as Promise<PrometheusResponse>,
        queueDepthRes.json() as Promise<PrometheusResponse>,
        latencyRes.json() as Promise<PrometheusResponse>,
        rejectsRes.json() as Promise<PrometheusResponse>,
      ]);

      // Parse metric values
      const parseMetricValue = (response: PrometheusResponse): number => {
        if (response.status === 'success' && response.data.result.length > 0) {
          return parseFloat(response.data.result[0].value[1]) || 0;
        }
        return 0;
      };

      setMetrics({
        activeCalls: Math.round(parseMetricValue(activeCalls)),
        queueDepth: Math.round(parseMetricValue(queueDepth)),
        p95Latency: Math.round(parseMetricValue(latency) * 1000), // Convert to ms
        callRejects: Math.round(parseMetricValue(rejects)),
        lastUpdated: new Date().toISOString(),
      });
    } catch (_err) {
      console.error('Error fetching Prometheus metrics:', err);
      setError('Failed to fetch voice metrics. Please check your Prometheus configuration.');
      toast({
        title: 'Metrics Error',
        description: 'Failed to fetch voice metrics from Prometheus',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  const handleRefresh = () => {
    fetchPrometheusMetrics();
  };

  useEffect(() => {
    // Initial fetch
    fetchPrometheusMetrics();

    // Set up auto-refresh every 15 seconds
    const interval = setInterval(fetchPrometheusMetrics, 15000);
    return () => clearInterval(interval);
  }, [fetchPrometheusMetrics]);

  const getStatusColor = (metric: keyof VoiceMetrics, value: number): "default" | "destructive" | "secondary" | "outline" => {
    switch (metric) {
      case 'activeCalls':
        return value > 50 ? 'destructive' : value > 20 ? 'secondary' : 'default';
      case 'queueDepth':
        return value > 10 ? 'destructive' : value > 5 ? 'secondary' : 'default';
      case 'p95Latency':
        return value > 5000 ? 'destructive' : value > 2000 ? 'secondary' : 'default';
      case 'callRejects':
        return value > 5 ? 'destructive' : value > 2 ? 'secondary' : 'default';
      default:
        return 'default';
    }
  };

  const formatLastUpdated = (timestamp: string): string => {
    const date = new Date(timestamp);
    return date.toLocaleTimeString();
  };

  return (
    <SuperAdminGuard>
      <div className="container mx-auto p-6 space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Voice Metrics</h1>
            <p className="text-muted-foreground">
              Real-time monitoring of voice service performance and queue status
            </p>
          </div>
          <Button
            onClick={handleRefresh}
            disabled={loading}
            variant="outline"
            size="sm"
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
        </div>

        {/* Live Stats Header */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Active Calls</CardTitle>
              <Phone className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="flex items-center space-x-2">
                <div className="text-2xl font-bold">{metrics.activeCalls}</div>
                <Badge variant={getStatusColor('activeCalls', metrics.activeCalls)}>
                  {metrics.activeCalls > 20 ? 'High' : 'Normal'}
                </Badge>
              </div>
              <p className="text-xs text-muted-foreground">
                Currently active voice calls
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Queue Depth</CardTitle>
              <Activity className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="flex items-center space-x-2">
                <div className="text-2xl font-bold">{metrics.queueDepth}</div>
                <Badge variant={getStatusColor('queueDepth', metrics.queueDepth)}>
                  {metrics.queueDepth > 5 ? 'High' : 'Normal'}
                </Badge>
              </div>
              <p className="text-xs text-muted-foreground">
                Calls waiting in queue
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">P95 Latency</CardTitle>
              <Clock className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="flex items-center space-x-2">
                <div className="text-2xl font-bold">{metrics.p95Latency}ms</div>
                <Badge variant={getStatusColor('p95Latency', metrics.p95Latency)}>
                  {metrics.p95Latency > 2000 ? 'Slow' : 'Good'}
                </Badge>
              </div>
              <p className="text-xs text-muted-foreground">
                95th percentile response time
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Call Rejects</CardTitle>
              <AlertTriangle className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="flex items-center space-x-2">
                <div className="text-2xl font-bold">{metrics.callRejects}</div>
                <Badge variant={getStatusColor('callRejects', metrics.callRejects)}>
                  {metrics.callRejects > 2 ? 'High' : 'Normal'}
                </Badge>
              </div>
              <p className="text-xs text-muted-foreground">
                Rejected calls per minute
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Status and Last Updated */}
        <div className="flex items-center justify-between text-sm text-muted-foreground">
          <div className="flex items-center space-x-2">
            <div className={`h-2 w-2 rounded-full ${error ? 'bg-red-500' : 'bg-green-500'}`} />
            <span>{error ? 'Connection Error' : 'Connected to Prometheus'}</span>
          </div>
          <span>Last updated: {formatLastUpdated(metrics.lastUpdated)}</span>
        </div>

        {/* Grafana Dashboard */}
        {grafanaUrl ? (
          <Card>
            <CardHeader>
              <CardTitle>Voice Service Dashboard</CardTitle>
              <CardDescription>
                Comprehensive metrics and visualizations from Grafana
              </CardDescription>
            </CardHeader>
            <CardContent className="p-0">
              <iframe
                src={`${grafanaUrl}?orgId=1&refresh=10s&kiosk&theme=light`}
                width="100%"
                height="800"
                frameBorder="0"
                className="rounded-b-lg"
                title="Voice Metrics Grafana Dashboard"
              />
            </CardContent>
          </Card>
        ) : (
          <Card>
            <CardHeader>
              <CardTitle>Grafana Dashboard</CardTitle>
              <CardDescription>
                Configure NEXT_PUBLIC_GRAFANA_URL to display the embedded dashboard
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex items-center justify-center h-64 bg-muted rounded-lg">
                <div className="text-center">
                  <Activity className="h-12 w-12 mx-auto text-muted-foreground mb-2" />
                  <p className="text-muted-foreground">
                    Grafana dashboard will appear here when configured
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {error && (
          <Card className="border-destructive">
            <CardHeader>
              <CardTitle className="text-destructive">Configuration Error</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-sm">{error}</p>
              <p className="text-xs text-muted-foreground mt-2">
                Please ensure NEXT_PUBLIC_PROMETHEUS_PROXY_URL is configured and the Prometheus service is accessible.
              </p>
            </CardContent>
          </Card>
        )}
      </div>
    </SuperAdminGuard>
  );
}
