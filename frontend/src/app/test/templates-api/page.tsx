'use client';

import { useState, useEffect } from 'react';
import { useSupabase } from '@/lib/supabase/provider';
import { useUser } from '@/contexts/UserContext';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { CheckCircle2, XCircle, AlertTriangle, Trash2 } from 'lucide-react';
import { useAuthenticatedFetch, HttpError } from '@/hooks/useAuthenticatedFetch';

// Sample template for creation
const sampleTemplate = {
  name: 'Sample Template',
  description: 'A sample template for testing',
  state: 'TX',
  practice_area: 'Personal Injury',
  category: 'Client Communication',
  document_type: 'Letter',
  content: JSON.stringify({
    blocks: [
      {
        type: 'paragraph',
        text: 'This is a sample template for testing purposes.'
      }
    ]
  }),
  variables: JSON.stringify({
    client_name: {
      type: 'string',
      description: 'Client full name'
    },
    case_number: {
      type: 'string',
      description: 'Case reference number'
    }
  })
};

export default function TemplatesApiTest() {
  const { supabase } = useSupabase();
  const { user, role: userRole } = useUser();
  const [templates, setTemplates] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [selectedTemplate, setSelectedTemplate] = useState<any>(null);
  const [editedTemplate, setEditedTemplate] = useState<any>({});
  const [result, setResult] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);

  const { authedFetch, isReady, error: fetchHookError } = useAuthenticatedFetch();

  useEffect(() => {
    if (user && isReady) {
      fetchTemplates();
    } else if (fetchHookError) {
      setError(`Failed to initialize authenticated fetch: ${fetchHookError}`);
    }
  }, [user, isReady, fetchHookError]);

  const fetchTemplates = async () => {
    setLoading(true);
    setError(null);

    if (!isReady) {
      setError("Authentication context not ready.");
      setLoading(false);
      return;
    }

    try {
      console.log('Fetching templates from API using authedFetch...');
      const data = await authedFetch<any>('/api/templates');

      console.log('API Response Data:', data);

      setTemplates(data.templates || []);
      setResult({
        success: true,
        message: `Retrieved ${data.templates?.length || 0} templates`,
        data: data.templates
      });
    } catch (_err) {
      console.error('Exception in fetchTemplates:', err);
      const errorMsg = err instanceof HttpError
        ? `API Error (${err.status}): ${err.message}`
        : (err instanceof Error ? err.message : 'Error fetching templates');
      setError(errorMsg);
      setResult({
        success: false,
        message: 'Error fetching templates',
        error: errorMsg
      });
    } finally {
      setLoading(false);
    }
  };

  const createTemplate = async () => {
    setLoading(true);
    setError(null);

    if (!isReady) {
      setError("Authentication context not ready.");
      setLoading(false);
      return;
    }

    try {
      console.log('Creating template using authedFetch...');
      const data = await authedFetch<any>('/api/templates', {
        method: 'POST',
        body: JSON.stringify(sampleTemplate)
      });

      console.log('API Response Data:', data);

      setResult({
        success: true,
        message: 'Template created successfully',
        data: data.template
      });
      fetchTemplates();
    } catch (_err) {
      console.error('Exception in createTemplate:', err);
      const errorMsg = err instanceof HttpError
        ? `API Error (${err.status}): ${err.message}`
        : (err instanceof Error ? err.message : 'Error creating template');
      setError(errorMsg);
      setResult({
        success: false,
        message: 'Error creating template',
        error: errorMsg
      });
    } finally {
      setLoading(false);
    }
  };

  const updateTemplate = async (id: string) => {
    setLoading(true);
    setError(null);

    if (!isReady) {
      setError("Authentication context not ready.");
      setLoading(false);
      return;
    }

    if (!editedTemplate.name || !editedTemplate.description) {
      setError('Name and description are required for update.');
      setLoading(false);
      return;
    }

    const updatePayload = {
      name: editedTemplate.name,
      description: editedTemplate.description,
      // Include other fields if they can be updated
    };

    try {
      console.log(`Updating template ${id} using authedFetch...`, updatePayload);
      const data = await authedFetch<any>(`/api/templates?id=${id}`, {
        method: 'PUT',
        body: JSON.stringify(updatePayload)
      });

      console.log('API Response Data:', data);

      if (data.success) {
        setResult({
          success: true,
          message: 'Template updated successfully',
          data: data.template
        });
        fetchTemplates();
      } else {
        setError(data.error || 'Failed to update template');
        setResult({
          success: false,
          message: data.error || 'Failed to update template'
        });
      }
    } catch (_err) {
      setError('Error updating template');
      setResult({
        success: false,
        message: 'Error updating template'
      });
    } finally {
      setLoading(false);
    }
  };

  const deleteTemplate = async (id: string) => {
    setLoading(true);
    setError(null);

    try {
      console.log(`Deleting template ${id} using authedFetch...`);
      const data = await authedFetch<any>(`/api/templates?id=${id}`, {
        method: 'DELETE'
      });

      console.log('API Response Data:', data);

      if (data.success) {
        setResult({
          success: true,
          message: 'Template deleted successfully'
        });
        fetchTemplates();
      } else {
        setError(data.error || 'Failed to delete template');
        setResult({
          success: false,
          message: data.error || 'Failed to delete template'
        });
      }
    } catch (_err) {
      setError('Error deleting template');
      setResult({
        success: false,
        message: 'Error deleting template'
      });
    } finally {
      setLoading(false);
    }
  };

  const handleSelectTemplate = (template: any) => {
    setSelectedTemplate(template);
    setEditedTemplate({
      name: template.name,
      description: template.description
    });
  };

  return (
    <div className="container mx-auto py-8">
      <h1 className="text-2xl font-bold mb-6">Legal Templates API Test</h1>

      <Card className="mb-6">
        <CardHeader>
          <CardTitle>Current User</CardTitle>
          <CardDescription>Authentication status and role information</CardDescription>
        </CardHeader>
        <CardContent>
          {user ? (
            <div>
              <p><strong>Email:</strong> {user.email}</p>
              <p><strong>Role:</strong> {userRole || 'Unknown'}</p>
              <p><strong>ID:</strong> {user.id}</p>
            </div>
          ) : (
            <Alert variant="destructive">
              <AlertTriangle className="h-4 w-4" />
              <AlertTitle>Not Authenticated</AlertTitle>
              <AlertDescription>
                You are not currently logged in. Please sign in to test the API endpoints.
              </AlertDescription>
            </Alert>
          )}
        </CardContent>
      </Card>

      <Tabs defaultValue="list" className="mb-6">
        <TabsList>
          <TabsTrigger value="list">List Templates</TabsTrigger>
          <TabsTrigger value="create">Create Template</TabsTrigger>
          <TabsTrigger value="update">Update Template</TabsTrigger>
          <TabsTrigger value="delete">Delete Template</TabsTrigger>
        </TabsList>

        <TabsContent value="list" className="p-4 border rounded-md">
          <h2 className="text-lg font-semibold mb-4">List Templates (GET)</h2>
          <p className="text-sm text-gray-600 mb-4">
            This endpoint retrieves all templates accessible to the current user based on RLS policies.
            Partners, attorneys, and staff can access templates.
          </p>
          <Button onClick={fetchTemplates} disabled={loading || !user}>
            {loading ? 'Loading...' : 'Fetch Templates'}
          </Button>

          {templates.length > 0 && (
            <div className="mt-4">
              <h3 className="text-md font-semibold mb-2">Templates ({templates.length})</h3>
              <div className="space-y-2">
                {templates.map((template) => (
                  <Card key={template.id} className="p-4">
                    <div className="flex justify-between">
                      <div>
                        <h4 className="font-semibold">{template.name}</h4>
                        <p className="text-sm text-gray-600">{template.description}</p>
                        <div className="text-xs text-gray-500 mt-1">
                          <span className="mr-2">{template.practice_area}</span>
                          <span className="mr-2">{template.category}</span>
                          <span>{template.state}</span>
                        </div>
                      </div>
                      <div className="flex space-x-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleSelectTemplate(template)}
                        >
                          Edit
                        </Button>
                        <Button
                          variant="destructive"
                          size="sm"
                          onClick={() => deleteTemplate(template.id)}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </Card>
                ))}
              </div>
            </div>
          )}
        </TabsContent>

        <TabsContent value="create" className="p-4 border rounded-md">
          <h2 className="text-lg font-semibold mb-4">Create Template (POST)</h2>
          <p className="text-sm text-gray-600 mb-4">
            This endpoint creates a new template. Only partners can create templates according to RLS policies.
          </p>
          <div className="mb-4">
            <h3 className="text-md font-semibold mb-2">Sample Template Data</h3>
            <pre className="bg-gray-100 p-2 rounded text-xs overflow-auto max-h-60">
              {JSON.stringify(sampleTemplate, null, 2)}
            </pre>
          </div>
          <Button onClick={createTemplate} disabled={loading || !user}>
            {loading ? 'Creating...' : 'Create Template'}
          </Button>
        </TabsContent>

        <TabsContent value="update" className="p-4 border rounded-md">
          <h2 className="text-lg font-semibold mb-4">Update Template (PUT)</h2>
          <p className="text-sm text-gray-600 mb-4">
            This endpoint updates an existing template. Partners and attorneys can update templates according to RLS policies.
          </p>

          {selectedTemplate ? (
            <div className="space-y-4">
              <div>
                <Label htmlFor="name">Template Name</Label>
                <Input
                  id="name"
                  value={editedTemplate.name || ''}
                  onChange={(e) => setEditedTemplate({ ...editedTemplate, name: e.target.value })}
                />
              </div>
              <div>
                <Label htmlFor="description">Description</Label>
                <Textarea
                  id="description"
                  value={editedTemplate.description || ''}
                  onChange={(e) => setEditedTemplate({ ...editedTemplate, description: e.target.value })}
                />
              </div>
              <Button
                onClick={() => updateTemplate(selectedTemplate.id)}
                disabled={loading || !user}
              >
                {loading ? 'Updating...' : 'Update Template'}
              </Button>
            </div>
          ) : (
            <Alert>
              <AlertTitle>No Template Selected</AlertTitle>
              <AlertDescription>
                Please select a template from the List Templates tab first.
              </AlertDescription>
            </Alert>
          )}
        </TabsContent>

        <TabsContent value="delete" className="p-4 border rounded-md">
          <h2 className="text-lg font-semibold mb-4">Delete Template (DELETE)</h2>
          <p className="text-sm text-gray-600 mb-4">
            This endpoint deletes a template. Only partners can delete templates according to RLS policies.
          </p>

          {templates.length > 0 ? (
            <div className="space-y-4">
              <div>
                <Label htmlFor="template-select">Select Template to Delete</Label>
                <Select onValueChange={(value) => {
                  const template = templates.find(t => t.id === value);
                  if (template) handleSelectTemplate(template);
                }}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select a template" />
                  </SelectTrigger>
                  <SelectContent>
                    {templates.map((template) => (
                      <SelectItem key={template.id} value={template.id}>
                        {template.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {selectedTemplate && (
                <div>
                  <Alert variant="destructive" className="mb-4">
                    <AlertTitle>Warning</AlertTitle>
                    <AlertDescription>
                      Are you sure you want to delete "{selectedTemplate.name}"? This action cannot be undone.
                    </AlertDescription>
                  </Alert>
                  <Button
                    variant="destructive"
                    onClick={() => deleteTemplate(selectedTemplate.id)}
                    disabled={loading || !user}
                  >
                    {loading ? 'Deleting...' : 'Delete Template'}
                  </Button>
                </div>
              )}
            </div>
          ) : (
            <Alert>
              <AlertTitle>No Templates Available</AlertTitle>
              <AlertDescription>
                There are no templates available to delete. Please create a template first.
              </AlertDescription>
            </Alert>
          )}
        </TabsContent>
      </Tabs>

      {result && (
        <Card className={`mb-6 border-l-4 ${result.success ? 'border-l-green-500' : 'border-l-red-500'}`}>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              {result.success ? (
                <CheckCircle2 className="h-5 w-5 text-green-500" />
              ) : (
                <XCircle className="h-5 w-5 text-red-500" />
              )}
              API Result
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="font-semibold">{result.message}</p>
            {result.data && (
              <div className="mt-2 p-2 bg-gray-50 rounded text-xs overflow-auto max-h-60">
                <pre>{JSON.stringify(result.data, null, 2)}</pre>
              </div>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  );
}
