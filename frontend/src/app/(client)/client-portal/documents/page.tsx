'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { FileText, FileUp, AlertTriangle, ExternalLink } from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { useSupabase } from '@/lib/supabase/provider';
import { formatDistanceToNow } from 'date-fns';

export default function ClientDocumentsPage() {
  const router = useRouter();
  const { supabase } = useSupabase();
  const [user, setUser] = useState<any>(null);
  const [clientData, setClientData] = useState<any>(null);
  const [documents, setDocuments] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchUserAndDocuments = async () => {
      try {
        // Get the current user
        const { data: { user } } = await supabase.auth.getUser();
        if (!user) {
          router.push('/login');
          return;
        }

        setUser(user);

        // Get the client data
        const { data: clientData, error: clientError } = await supabase
          .from('clients')
          .select('*')
          .eq('user_id', user.id)
          .single();

        if (clientError) {
          throw clientError;
        }

        setClientData(clientData);

        // Get the client's documents
        const { data: documentsData, error: documentsError } = await supabase
          .from('documents')
          .select('*')
          .eq('client_id', clientData.id)
          .order('created_at', { ascending: false });

        if (documentsError) {
          throw documentsError;
        }

        setDocuments(documentsData || []);
      } catch (error: any) {
        console.error('Error fetching documents:', error);
        setError(error.message);
      } finally {
        setIsLoading(false);
      }
    };

    fetchUserAndDocuments();
  }, [supabase, router]);

  const openDocument = async (documentId: string) => {
    try {
      // Get a signed URL for the document
      const { data, error } = await supabase.functions.invoke('get-document-url', {
        body: { documentId }
      });

      if (error) throw error;

      // Open the document in a new tab
      window.open(data.url, '_blank');
    } catch (_error) {
      console.error('Error opening document:', error);
      alert('Could not open document. Please try again later.');
    }
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
      </div>
    );
  }

  if (error) {
    return (
      <Alert variant="destructive" className="max-w-2xl mx-auto mt-8">
        <AlertTriangle className="h-4 w-4" />
        <AlertTitle>Error</AlertTitle>
        <AlertDescription>
          There was an error loading your documents. Please try again later or contact support.
        </AlertDescription>
        <Button variant="outline" className="mt-4" asChild>
          <Link href="/client-portal">Return to Dashboard</Link>
        </Button>
      </Alert>
    );
  }

  return (
    <div className="max-w-6xl mx-auto p-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">My Documents</h1>
        {clientData?.conflict_check_status === 'cleared' && (
          <Button asChild>
            <Link href="/client-portal/documents/upload">
              <FileUp className="mr-2 h-4 w-4" />
              Upload New Document
            </Link>
          </Button>
        )}
      </div>

      {clientData?.conflict_check_status !== 'cleared' && (
        <Alert variant="warning" className="mb-6">
          <AlertTriangle className="h-4 w-4" />
          <AlertTitle>Document Upload Unavailable</AlertTitle>
          <AlertDescription>
            Your case is still undergoing our conflict check review process. Document upload will be available once this process is complete.
          </AlertDescription>
        </Alert>
      )}

      {documents.length === 0 ? (
        <Card>
          <CardHeader>
            <CardTitle>No Documents Found</CardTitle>
            <CardDescription>
              You don&apos;t have any documents yet.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <p>
              {clientData?.conflict_check_status === 'cleared'
                ? 'You can upload documents related to your case using the "Upload New Document" button above.'
                : 'Once your case has been cleared through our conflict check process, you will be able to upload documents.'}
            </p>
          </CardContent>
        </Card>
      ) : (
        <Card>
          <CardHeader>
            <CardTitle>Your Documents</CardTitle>
            <CardDescription>
              View and access documents related to your case.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Document Name</TableHead>
                  <TableHead>Type</TableHead>
                  <TableHead>Uploaded</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {documents.map((document) => (
                  <TableRow key={document.id}>
                    <TableCell className="font-medium">{document.filename}</TableCell>
                    <TableCell>
                      <Badge variant="outline">{document.document_type}</Badge>
                    </TableCell>
                    <TableCell>{formatDistanceToNow(new Date(document.created_at), { addSuffix: true })}</TableCell>
                    <TableCell>
                      <Badge
                        variant={document.status === 'processed' ? 'default' : 'secondary'}
                      >
                        {document.status}
                      </Badge>
                    </TableCell>
                    <TableCell className="text-right">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => openDocument(document.id)}
                      >
                        <ExternalLink className="h-4 w-4 mr-1" />
                        View
                      </Button>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
