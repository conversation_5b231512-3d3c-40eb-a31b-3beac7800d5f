'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { supabase } from '@/lib/supabase/client'
import { generateDeviceFingerprint } from '@/lib/security/auth-security-logger'
import { logSecurityEvent } from '@/lib/security/forensics'

export default function TestSecurityDashboardPage() {
  const [session, setSession] = useState<any>(null)
  const [tokenInfo, setTokenInfo] = useState<any>(null)
  const [deviceInfo, setDeviceInfo] = useState<any>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const router = useRouter()

  useEffect(() => {
    const getSession = async () => {
      setLoading(true)
      setError(null)

      try {
        const { data, error } = await supabase.auth.getSession()

        if (error) {
          setError(error.message)
          return
        }

        setSession(data.session)

        if (data.session?.access_token) {
          // Parse JWT token
          try {
            const parts = data.session.access_token.split('.');
            if (parts.length === 3) {
              const payload = JSON.parse(atob(parts[1]));
              setTokenInfo(payload);
            }
          } catch (_err) {
            console.error('Error parsing token:', err);
          }
        }

        // Get device fingerprint
        const fingerprint = await generateDeviceFingerprint();
        setDeviceInfo({
          fingerprint,
          userAgent: navigator.userAgent,
          platform: navigator.platform,
          language: navigator.language
        });
      } catch (_err) {
        console.error('Error getting session:', err)
        setError('Failed to get session')
      } finally {
        setLoading(false)
      }
    }

    getSession()
  }, [])

  const handleLogEvent = async () => {
    try {
      // Get device fingerprint
      const deviceFingerprint = await generateDeviceFingerprint();

      // Log security event using our enhanced logging function
      // Pass the required supabase client as the first parameter
      await logSecurityEvent(supabase, 'auth.test_event', {
        userId: tokenInfo?.sub,
        email: tokenInfo?.email,
        deviceFingerprint,
        reason: 'Testing security logging',
        timestamp: new Date().toISOString(),
        sessionId: session?.session?.id,
        tokenId: tokenInfo?.jti,
        browser: navigator.userAgent,
        platform: navigator.platform,
        language: navigator.language
      });

      // The function doesn't return a result, so we just show success message
      alert('Security event logged successfully');
    } catch (_err) {
      console.error('Error logging event:', err);
      alert(`Failed to log security event: ${err instanceof Error ? err.message : 'Unknown error'}`);
    }
  }

  if (loading) {
    return <div className="p-8">Loading...</div>
  }

  if (!session) {
    return (
      <div className="p-8">
        <h1 className="text-2xl font-bold mb-4">Security Dashboard</h1>
        <p className="mb-4">You need to be logged in to view the security dashboard.</p>
        <button
          onClick={() => router.push('/login')}
          className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded"
        >
          Go to Login
        </button>
      </div>
    )
  }

  return (
    <div className="p-8">
      <h1 className="text-2xl font-bold mb-4">Security Dashboard</h1>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          {error}
        </div>
      )}

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="bg-white shadow rounded-lg p-6">
          <h2 className="text-xl font-semibold mb-4">Current Session</h2>
          <div className="space-y-2">
            <p>
              <span className="font-medium">User ID:</span> {tokenInfo?.sub}
            </p>
            <p>
              <span className="font-medium">Email:</span> {tokenInfo?.email}
            </p>
            <p>
              <span className="font-medium">Role:</span> {tokenInfo?.role}
            </p>
            <p>
              <span className="font-medium">Token ID (JTI):</span> {tokenInfo?.jti || 'Not available'}
            </p>
            <p>
              <span className="font-medium">Expires:</span>{' '}
              {tokenInfo?.exp ? new Date(tokenInfo.exp * 1000).toLocaleString() : 'Unknown'}
            </p>
          </div>
        </div>

        <div className="bg-white shadow rounded-lg p-6">
          <h2 className="text-xl font-semibold mb-4">Device Information</h2>
          <div className="space-y-2">
            <p>
              <span className="font-medium">Device Fingerprint:</span> {deviceInfo?.fingerprint}
            </p>
            <p>
              <span className="font-medium">Platform:</span> {deviceInfo?.platform}
            </p>
            <p>
              <span className="font-medium">Language:</span> {deviceInfo?.language}
            </p>
            <p>
              <span className="font-medium">User Agent:</span>
              <span className="block text-xs mt-1 break-all">{deviceInfo?.userAgent}</span>
            </p>
          </div>
        </div>
      </div>

      <div className="mt-6 bg-white shadow rounded-lg p-6">
        <h2 className="text-xl font-semibold mb-4">Security Actions</h2>
        <div className="space-y-4">
          <button
            onClick={handleLogEvent}
            className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded mr-4"
          >
            Log Security Event
          </button>

          <button
            onClick={() => router.push('/test-security')}
            className="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded"
          >
            Back to Test Page
          </button>
        </div>
      </div>
    </div>
  )
}
