'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { supabase } from '@/lib/supabase/client'
import { useUser } from '@/contexts/UserContext' // Import useUser
import { generateDeviceFingerprint } from '@/lib/security/auth-security-logger'
// Removed logSecurityEvent import as we use authedFetch now
import { useAuthenticatedFetch } from '@/hooks/useAuthenticatedFetch'

// Define expected API response structure for logging
interface LogSecurityEventApiResponse {
  success?: boolean;
  error?: string;
}

// Define the expected structure of the JWT payload for JTI extraction
interface JwtPayloadForJti {
  jti?: string;
  exp: number;
  // Add other essential fields if needed for parsing validation,
  // but we primarily care about jti and exp here for this component's logic.
  sub?: string;
  role?: string;
}


export default function TestSecurityPage() {
  const { user, jwtPayload, loading: isUserLoading } = useUser(); // Use the UserContext with correct properties
  const [jti, setJti] = useState<string | null>(null);
  const [tokenExp, setTokenExp] = useState<number | null>(null); // Store expiration too
  const [isTokenInfoLoading, setIsTokenInfoLoading] = useState(true); // Separate loading for token info
  const [error, setError] = useState<string | null>(null);
  const router = useRouter();
  const { authedFetch } = useAuthenticatedFetch();

  // Effect to get session specifically for JTI and expiration
  useEffect(() => {
    const getTokenInfo = async () => {
      setIsTokenInfoLoading(true);
      setError(null);

      try {
        // Still need getSession for the raw access token
        const { data: sessionData, error: sessionError } = await supabase.auth.getSession();

        if (sessionError) {
          setError(`Session Error: ${sessionError.message}`);
          setJti(null); // Clear JTI on error
          setTokenExp(null);
          return;
        }

        if (sessionData.session?.access_token) {
          // Parse JWT token ONLY for JTI and expiration
          try {
            const parts = sessionData.session.access_token.split('.');
            if (parts.length === 3) {
              const payload: JwtPayloadForJti = JSON.parse(atob(parts[1]));
              setJti(payload.jti ?? null); // Store JTI
              setTokenExp(payload.exp ?? null); // Store expiration
            } else {
              setError('Invalid token format.');
              setJti(null);
              setTokenExp(null);
            }
          } catch (_err) {
            console.error('Error parsing token:', err);
            setError('Failed to parse token.');
            setJti(null);
            setTokenExp(null);
          }
        } else {
          // No session or token, clear JTI and expiration
          setJti(null);
          setTokenExp(null);
        }
      } catch (_err) {
        console.error('Error in getTokenInfo:', err);
        setError('Failed to get token information.');
        setJti(null);
        setTokenExp(null);
      } finally {
        setIsTokenInfoLoading(false);
      }
    };

    // Only run if user context is loaded (or explicitly logged out)
    if (!isUserLoading) {
       getTokenInfo();
    }

  }, [isUserLoading]); // Rerun when user loading state changes

  const handleLogEvent = async () => {
    if (!user) {
      alert('User not available.');
      return;
    }

    try {
      const deviceFingerprint = await generateDeviceFingerprint();

      await authedFetch<LogSecurityEventApiResponse>('/api/security/log', {
        method: 'POST',
        body: JSON.stringify({
          eventType: 'auth.test_event',
          details: {
            userId: user.id, // Use user.id from context
            email: user.email, // Use user.email from context
            tenantId: jwtPayload?.tenant_id, // Use jwtPayload?.tenant_id
            deviceFingerprint,
            reason: 'Testing security logging',
            timestamp: new Date().toISOString(),
            // sessionId: session?.id, // session object no longer stored directly
            tokenId: jti // Use jti from state
          }
        })
      });

      alert('Security event logged successfully');
    } catch (err: unknown) {
      console.error('Error logging event:', err);
      const errorMessage = err instanceof Error ? err.message : 'Unknown error';
      alert(`Failed to log security event: ${errorMessage}`);
    }
  }

  const handleRevokeSession = async () => {
    if (!jti) { // Use jti from state
      alert('No token ID (JTI) available for revocation.');
      return;
    }

    try {
      // Call the existing revoke_session function
      // @ts-expect-error - supabase.rpc requires specific types for custom functions not inferred automatically
      await supabase.rpc('revoke_session', {
        session_jti: jti, // Use jti from state
        revocation_reason: 'User initiated test revocation'
      });

      alert('Session revoked successfully');

      // Sign out locally
      await supabase.auth.signOut();
      // User context will update via its own listener, triggering redirect if necessary
      // Or force redirect immediately:
      router.push('/login');
    } catch (_err) {
      console.error('Error revoking session:', err);
      alert('Failed to revoke session');
    }
  }

  // Combine loading states
  const isLoading = isUserLoading || isTokenInfoLoading;

  if (isLoading) {
    return <div className="p-8">Loading security context...</div>;
  }

  // If user context is loaded but user is null, they are logged out
  if (!user) {
    return (
      <div className="p-8">
        <h1 className="text-2xl font-bold mb-4">Security Test</h1>
        <p className="mb-4">You need to be logged in to test security features.</p>
        <button
          onClick={() => router.push('/login')}
          className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded"
        >
          Go to Login
        </button>
      </div>
    );
  }

  // If user is loaded, but there was an error getting JTI
  if (error && !jti) {
     console.warn("User is logged in, but failed to get JTI:", error);
     // Decide if you want to show an error or proceed without JTI-dependent features
  }


  return (
    <div className="p-8">
      <h1 className="text-2xl font-bold mb-4">Security Test</h1>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          {error}
        </div>
      )}

      <div className="bg-white shadow rounded-lg p-6 mb-6">
        <h2 className="text-xl font-semibold mb-4">Current User & Session Info</h2>
        <div className="space-y-2">
          <p><span className="font-medium">User ID:</span> {user.id}</p>
          <p><span className="font-medium">Email:</span> {user.email}</p>
          <p><span className="font-medium">Role:</span> {jwtPayload?.role || 'N/A'}</p>
          <p><span className="font-medium">Tenant ID:</span> {jwtPayload?.tenant_id || 'N/A'}</p>
          <p><span className="font-medium">Token ID (JTI):</span> {jti || 'Not available'}</p>
          <p>
            <span className="font-medium">Token Expires:</span>{' '}
            {tokenExp ? new Date(tokenExp * 1000).toLocaleString() : 'Unknown'}
          </p>
        </div>
      </div>

      <div className="bg-white shadow rounded-lg p-6 mb-6">
        <h2 className="text-xl font-semibold mb-4">Security Actions</h2>
        <div className="space-y-4">
          <button
            onClick={handleLogEvent}
            className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded mr-4 disabled:opacity-50"
            disabled={!user} // Disable if user not loaded
          >
            Log Security Event
          </button>

          <button
            onClick={handleRevokeSession}
            className="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded disabled:opacity-50"
            disabled={!jti} // Disable if JTI not available
          >
            Revoke Current Session
          </button>
        </div>
      </div>

      <div className="bg-white shadow rounded-lg p-6">
        <h2 className="text-xl font-semibold mb-4">Security Dashboard</h2>
        <p className="mb-4">
          Visit the Security Dashboard to manage your sessions and devices:
        </p>
        <button
          onClick={() => router.push('/test-security/dashboard')} // Ensure this route exists
          className="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded"
        >
          Go to Security Dashboard
        </button>
      </div>
    </div>
  )
}
