'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { useTasksApi } from '@/hooks/useTasksApi';

/**
 * A simple page to verify the tasks API is working correctly with schema support
 */
export default function TasksTestPage() {
  const [tasks, setTasks] = useState<any[]>([]);
  const [error, setError] = useState<string | null>(null);
  const { getAllTasks, isLoading } = useTasksApi();

  const fetchTasks = async () => {
    try {
      setError(null);
      const tasksData = await getAllTasks();
      setTasks(tasksData);
      console.log(`Successfully loaded ${tasksData.length} tasks using schema-aware hook`);
    } catch (_err) {
      console.error('Error fetching tasks:', err);
      setError(err instanceof Error ? err.message : 'Unknown error occurred');
    }
  };

  return (
    <div className="container mx-auto py-6">
      <Card className="mb-6">
        <CardHeader>
          <CardTitle>Task Schema Test</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col gap-4">
            <div className="flex justify-between items-center">
              <div>
                <p className="text-muted-foreground">
                  Tests the schema-aware tasks API to verify it's correctly accessing the tasks in the tenants schema.
                </p>
              </div>
              <Button
                onClick={fetchTasks}
                variant="default"
                disabled={isLoading}
              >
                {isLoading ? 'Loading...' : 'Fetch Tasks'}
              </Button>
            </div>

            {error && (
              <div className="bg-red-50 border border-red-200 text-red-700 p-4 rounded-md">
                <p><strong>Error:</strong> {error}</p>
              </div>
            )}

            {tasks.length > 0 && (
              <div className="bg-green-50 border border-green-200 p-4 rounded-md">
                <p className="font-medium text-green-700">✅ Success! Loaded {tasks.length} tasks</p>
                <p className="text-sm text-green-600 mt-2">The schema-aware API hook is working correctly.</p>
              </div>
            )}

            <div className="border rounded-md overflow-hidden">
              <table className="w-full">
                <thead>
                  <tr className="bg-muted">
                    <th className="p-2 text-left">ID</th>
                    <th className="p-2 text-left">Title</th>
                    <th className="p-2 text-left">Status</th>
                    <th className="p-2 text-left">Due Date</th>
                  </tr>
                </thead>
                <tbody>
                  {tasks.map((task) => (
                    <tr key={task.id} className="border-t">
                      <td className="p-2 text-xs font-mono">{task.id.substring(0, 8)}...</td>
                      <td className="p-2">{task.title}</td>
                      <td className="p-2">
                        <span className={`inline-block px-2 py-1 text-xs rounded ${
                          task.status === 'done' ? 'bg-green-100 text-green-800' :
                          task.status === 'in_progress' ? 'bg-blue-100 text-blue-800' :
                          'bg-gray-100 text-gray-800'
                        }`}>
                          {task.status}
                        </span>
                      </td>
                      <td className="p-2">{task.due_date ? new Date(task.due_date).toLocaleDateString() : 'N/A'}</td>
                    </tr>
                  ))}
                  {tasks.length === 0 && (
                    <tr>
                      <td colSpan={4} className="p-4 text-center text-muted-foreground">
                        Click "Fetch Tasks" to load tasks
                      </td>
                    </tr>
                  )}
                </tbody>
              </table>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
