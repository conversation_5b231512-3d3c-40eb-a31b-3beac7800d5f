// frontend/src/app/(authenticated)/calendar/new-event/page.tsx
"use client";

import * as React from "react";
import { useRouter } from "next/navigation";
import { createBrowserSupabaseClient } from "@/lib/auth/client";
import { useCoAgent } from "@copilotkit/react-core";

// UI Components
import { Button } from "@/components/ui/button";
import { Card, CardHeader, CardTitle, CardContent } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { Badge } from "@/components/ui/badge";

import { EventAgentChat } from "./EventAgentChat";

// Define the state interface
interface EventAgentState {
  event_title: string;
  event_description: string;
  start_time: string; // ISO string
  end_time: string; // ISO string
  all_day: boolean;
  location: string;
  event_type: string;
  case_id?: string;
  client_id?: string;
  assigned_to: string[];
  status: string;
  progress_percentage: number;
}

// Default state
const defaultState: EventAgentState = {
  event_title: "",
  event_description: "",
  start_time: new Date().toISOString(),
  end_time: new Date(new Date().setHours(new Date().getHours() + 1)).toISOString(),
  all_day: false,
  location: "",
  event_type: "meeting",
  case_id: undefined,
  client_id: undefined,
  assigned_to: [],
  status: "scheduled",
  progress_percentage: 0,
};

// SmartTitleField Component
function SmartTitleField({ value, onChange, setState }: {
  value: string;
  onChange: (value: string) => void;
  setState: (state: Partial<EventAgentState>) => void;
}) {
  const [localValue, setLocalValue] = React.useState(value);

  // Note: You'll need to implement useDebounce or use a library like lodash
  const debouncedValue = useDebounce(localValue, 500);

  React.useEffect(() => {
    if (debouncedValue.length > 10) {
      const parsedData = parseTitle(debouncedValue);
      setState({
        event_type: parsedData.eventType || "meeting",
        start_time: parsedData.date ? new Date(parsedData.date).toISOString() : undefined,
      });
    }
  }, [debouncedValue, setState]);

  return (
    <div className="relative">
      <Textarea
        value={localValue}
        onChange={(e) => {
          setLocalValue(e.target.value);
          onChange(e.target.value);
        }}
        placeholder="Describe your event (e.g., 'Deposition with Dr. Smith re: medical report')"
        className="text-lg resize-none min-h-[80px]"
      />
      <div className="absolute bottom-2 right-2">
        <Badge variant="outline" className="bg-blue-50 text-blue-700">AI Enabled</Badge>
      </div>
    </div>
  );
}

// Simple title parsing function (customize as needed)
function parseTitle(title: string) {
  const lowercaseTitle = title.toLowerCase();
  const eventType = lowercaseTitle.includes("meeting") ? "meeting" :
                    lowercaseTitle.includes("deposition") ? "deposition" :
                    lowercaseTitle.includes("court") ? "court" :
                    lowercaseTitle.includes("deadline") ? "deadline" : "other";
  let date = null;
  if (lowercaseTitle.includes("tomorrow")) {
    date = new Date();
    date.setDate(date.getDate() + 1);
  } else if (lowercaseTitle.includes("next week")) {
    date = new Date();
    date.setDate(date.getDate() + 7);
  }
  return { eventType, date };
}

// Assume useDebounce hook (implement or use a library)
function useDebounce(value: string, delay: number) {
  const [debouncedValue, setDebouncedValue] = React.useState(value);
  React.useEffect(() => {
    const timer = setTimeout(() => setDebouncedValue(value), delay);
    return () => clearTimeout(timer);
  }, [value, delay]);
  return debouncedValue;
}

export default function NewEventPage() {
  const router = useRouter();
  const supabase = createBrowserSupabaseClient();
  const [isSubmitting, setIsSubmitting] = React.useState(false);
  const [cases, setCases] = React.useState<Array<{
    id: string;
    title: string;
    status: string;
  }>>([]);
  const [clients, setClients] = React.useState<Array<{
    id: string;
    first_name: string;
    last_name: string;
    email: string | null;
  }>>([]);
  const [users, setUsers] = React.useState<Array<{
    id: string;
    email: string;
    first_name: string | null;
    last_name: string | null;
  }>>([]);

  const { state, setState } = useCoAgent<EventAgentState>({
    name: "event_agent",
    initialState: defaultState,
  });

  // Fetch related data for dropdowns
  React.useEffect(() => {
    const fetchRelatedData = async () => {
      try {
        // Fetch cases from tenants schema
        try {
          const { data: casesData, error: casesError } = await supabase
            .schema('tenants')
            .from('cases')
            .select('id, title, status')
            .order('title');
          if (casesData && !casesError) {
            setCases(casesData);
          }
        } catch (_error) {
          console.warn('Cases table not available:', error);
        }

        // Fetch clients from tenants schema
        try {
          const { data: clientsData, error: clientsError } = await supabase
            .schema('tenants')
            .from('clients')
            .select('id, first_name, last_name, email')
            .order('last_name');
          if (clientsData && !clientsError) {
            setClients(clientsData);
          }
        } catch (_error) {
          console.warn('Clients table not available:', error);
        }

        // Fetch users from tenants schema
        try {
          const { data: usersData, error: usersError } = await supabase
            .schema('tenants')
            .from('users')
            .select('id, email, first_name, last_name')
            .order('last_name');
          if (usersData && !usersError) {
            setUsers(usersData);
          }
        } catch (_error) {
          console.warn('Users table not available:', error);
        }
      } catch (_error) {
        console.error("Error fetching related data:", error);
      }
    };
    fetchRelatedData();
  }, [supabase]);

  const onSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      const eventData = {
        title: state.event_title,
        description: state.event_description || "",
        start_time: state.start_time,
        end_time: state.end_time,
        all_day: state.all_day,
        location: state.location || "",
        event_type: state.event_type,
        case_id: state.case_id === "none" ? undefined : state.case_id,
        client_id: state.client_id === "none" ? undefined : state.client_id,
        assigned_to: state.assigned_to || [],
        status: state.status,
      };

      const response = await fetch("/api/events", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(eventData),
      });

      if (!response.ok) throw new Error("Failed to create event");

      router.push("/calendar");
      router.refresh();
    } catch (_error) {
      console.error("Error creating event:", error);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="container mx-auto py-6">
      <div className="flex items-center justify-between mb-6">
        <h1 className="text-3xl font-bold tracking-tight">Create New Event</h1>
        <Button variant="outline" onClick={() => router.back()}>Cancel</Button>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-[1fr_300px] gap-8">
        {/* Form */}
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Event Details</CardTitle>
            </CardHeader>
            <CardContent>
              <form onSubmit={onSubmit} className="space-y-6">
                {/* Event Description */}
                <div>
                  <label className="block text-sm font-medium">Event Description</label>
                  <SmartTitleField
                    value={state.event_title}
                    onChange={(value) => setState({ ...state, event_title: value })}
                    setState={(updates) => setState({ ...state, ...updates })}
                  />
                  <p className="text-sm text-muted-foreground mt-1">
                    Describe your event in natural language. AI will help extract details.
                  </p>
                </div>

                {/* Event Type */}
                <div>
                  <label className="block text-sm font-medium">Event Type</label>
                  <Select
                    value={state.event_type}
                    onValueChange={(value) => setState({ ...state, event_type: value })}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select event type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="meeting">Meeting</SelectItem>
                      <SelectItem value="court">Court Appearance</SelectItem>
                      <SelectItem value="deposition">Deposition</SelectItem>
                      <SelectItem value="deadline">Deadline</SelectItem>
                      <SelectItem value="other">Other</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {/* Start Time */}
                <div>
                  <label className="block text-sm font-medium">Start Time</label>
                  <Input
                    type="datetime-local"
                    value={state.start_time ? new Date(state.start_time).toISOString().slice(0, 16) : ""}
                    onChange={(e) => setState({ ...state, start_time: new Date(e.target.value).toISOString() })}
                  />
                </div>

                {/* End Time */}
                <div>
                  <label className="block text-sm font-medium">End Time</label>
                  <Input
                    type="datetime-local"
                    value={state.end_time ? new Date(state.end_time).toISOString().slice(0, 16) : ""}
                    onChange={(e) => setState({ ...state, end_time: new Date(e.target.value).toISOString() })}
                  />
                </div>

                {/* All Day */}
                <div className="flex items-center space-x-2">
                  <Checkbox
                    checked={state.all_day}
                    onCheckedChange={(checked) => setState({ ...state, all_day: checked as boolean })}
                  />
                  <label className="text-sm font-medium">All-day event</label>
                </div>

                {/* Location */}
                <div>
                  <label className="block text-sm font-medium">Location</label>
                  <Input
                    value={state.location}
                    onChange={(e) => setState({ ...state, location: e.target.value })}
                    placeholder="Enter location"
                  />
                </div>

                {/* Description */}
                <div>
                  <label className="block text-sm font-medium">Description</label>
                  <Textarea
                    value={state.event_description}
                    onChange={(e) => setState({ ...state, event_description: e.target.value })}
                    placeholder="Add details about this event"
                  />
                </div>

                {/* Related Case */}
                <div>
                  <label className="block text-sm font-medium">Related Case</label>
                  <Select
                    value={state.case_id || "none"}
                    onValueChange={(value) => setState({ ...state, case_id: value === "none" ? undefined : value })}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select a case" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="none">None</SelectItem>
                      {cases.map((caseItem) => (
                        <SelectItem key={caseItem.id} value={caseItem.id}>
                          {caseItem.title}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                {/* Related Client */}
                <div>
                  <label className="block text-sm font-medium">Related Client</label>
                  <Select
                    value={state.client_id || "none"}
                    onValueChange={(value) => setState({ ...state, client_id: value === "none" ? undefined : value })}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select a client" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="none">None</SelectItem>
                      {clients.map((client) => (
                        <SelectItem key={client.id} value={client.id}>
                          {client.first_name} {client.last_name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                {/* Submit */}
                <Button type="submit" disabled={isSubmitting}>
                  {isSubmitting ? "Creating..." : "Create Event"}
                </Button>
              </form>
            </CardContent>
          </Card>
        </div>

        {/* Event AI Assistant */}
        <div>
          <EventAgentChat />
        </div>
      </div>
    </div>
  );
}
