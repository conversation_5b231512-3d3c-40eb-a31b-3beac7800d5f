//frontend/src/app/(authenticated)/clients/page.tsx
'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import { useRouter } from 'next/navigation'
import { Client } from '@/lib/types/client'
// Use a simple date formatter instead of date-fns to avoid compatibility issues
const formatDate = (dateString: string): string => {
  const date = new Date(dateString);
  return date.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
  });
}
import { Plus, Search, UserPlus } from 'lucide-react'

import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import { Skeleton } from '@/components/ui/skeleton'
import { db } from '@/lib/supabase/client'

// Use imported Client type instead of redefining

export default function ClientsPage() {
  const router = useRouter()
  const [searchQuery, setSearchQuery] = useState('')
  const [clients, setClients] = useState<Client[]>([])
  const [loading, setLoading] = useState(true)
  const [totalClients, setTotalClients] = useState(0)
  const [activeClients, setActiveClients] = useState(0)
  const [totalCases, setTotalCases] = useState(0)

  // Fetch clients on component mount
  useEffect(() => {
    const fetchClients = async () => {
      try {
        setLoading(true)
        const result = await db.clients.getAll({
          searchTerm: searchQuery.length > 2 ? searchQuery : undefined
        })

        // Process clients to match our UI needs
        const processedClients = result.clients.map((client: any) => ({
          id: client.id,
          first_name: client.first_name || '',
          last_name: client.last_name || '',
          name: `${client.first_name || ''} ${client.last_name || ''}`.trim(),
          email: client.email || '',
          phone: client.phone_primary || '',
          status: client.status || 'inactive',
          client_type: client.client_type || 'individual',
          cases: client.case_count || 0,
          createdAt: client.created_at || new Date().toISOString(),
          intake_date: client.intake_date
        }))

        setClients(processedClients)
        setTotalClients(result.total)
        setActiveClients(processedClients.filter((c: Client) => c.status === 'active').length)
        setTotalCases(processedClients.reduce((acc: number, client: Client) => acc + (client.cases || 0), 0))
        setLoading(false)
      } catch (_error) {
        console.error('Error fetching clients:', error)
        setLoading(false)
      }
    }

    fetchClients()
  }, [searchQuery])

  // Debounced search
  useEffect(() => {
    const timer = setTimeout(() => {
      // Only trigger search if query is empty or has at least 3 characters
      if (searchQuery === '' || searchQuery.length >= 3) {
        // The search will be triggered by the dependency in the first useEffect
      }
    }, 300)

    return () => clearTimeout(timer)
  }, [searchQuery])

  // Filter clients based on search query (client-side filtering for immediate feedback)
  const filteredClients = clients.filter(client =>
    (client.first_name + ' ' + client.last_name).toLowerCase().includes(searchQuery.toLowerCase()) ||
    (client.email || '').toLowerCase().includes(searchQuery.toLowerCase()) ||
    ((client.phone || '') as string).includes(searchQuery)
  )

  return (
    <div className="container max-w-7xl mx-auto py-8 px-4">
      <div className="mb-8 flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Clients</h1>
          <p className="text-muted-foreground mt-1">
            Manage your client relationships
          </p>
        </div>
        <Link href="/clients/intake">
          <Button>
            <UserPlus className="mr-2 h-4 w-4" />
            New Intake
          </Button>
        </Link>
      </div>

      {/* Search and filters */}
      <div className="mb-6">
        <div className="relative">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search clients..."
            className="pl-8"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>
      </div>

      {/* Client Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Total Clients</CardTitle>
          </CardHeader>
          <CardContent>
            {loading ? (
              <Skeleton className="h-8 w-16" />
            ) : (
              <>
                <div className="text-2xl font-bold">{totalClients}</div>
                <p className="text-xs text-muted-foreground mt-1">
                  All client records
                </p>
              </>
            )}
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Active Clients</CardTitle>
          </CardHeader>
          <CardContent>
            {loading ? (
              <Skeleton className="h-8 w-16" />
            ) : (
              <>
                <div className="text-2xl font-bold">{activeClients}</div>
                <p className="text-xs text-muted-foreground mt-1">
                  Currently working with
                </p>
              </>
            )}
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Total Cases</CardTitle>
          </CardHeader>
          <CardContent>
            {loading ? (
              <Skeleton className="h-8 w-16" />
            ) : (
              <>
                <div className="text-2xl font-bold">{totalCases}</div>
                <p className="text-xs text-muted-foreground mt-1">
                  Across all clients
                </p>
              </>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Clients Table */}
      <Card>
        <CardHeader>
          <CardTitle>Client List</CardTitle>
          <CardDescription>
            View and manage all your clients
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Name</TableHead>
                <TableHead>Email</TableHead>
                <TableHead>Phone</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Cases</TableHead>
                <TableHead>Added</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {loading ? (
                // Loading state - show skeleton rows
                Array(3).fill(0).map((_, i) => (
                  <TableRow key={`skeleton-${i}`}>
                    <TableCell><Skeleton className="h-5 w-32" /></TableCell>
                    <TableCell><Skeleton className="h-5 w-40" /></TableCell>
                    <TableCell><Skeleton className="h-5 w-24" /></TableCell>
                    <TableCell><Skeleton className="h-5 w-16" /></TableCell>
                    <TableCell><Skeleton className="h-5 w-8" /></TableCell>
                    <TableCell><Skeleton className="h-5 w-20" /></TableCell>
                  </TableRow>
                ))
              ) : filteredClients.length > 0 ? (
                filteredClients.map((client) => (
                  <TableRow
                    key={client.id}
                    className="cursor-pointer"
                    onClick={() => router.push(`/clients/${client.id}`)}
                  >
                    <TableCell className="font-medium">{`${client.first_name} ${client.last_name}`}</TableCell>
                    <TableCell>{client.email}</TableCell>
                    <TableCell>{client.phone || 'N/A'}</TableCell>
                    <TableCell>
                      <span className={`inline-block px-2 py-1 rounded-full text-xs ${
                        client.status === 'active'
                          ? 'bg-green-100 text-green-800'
                          : client.status === 'pending'
                            ? 'bg-yellow-100 text-yellow-800'
                            : 'bg-gray-100 text-gray-800'
                      }`}>
                        {client.status === 'active' ? 'Active' :
                         client.status === 'pending' ? 'Pending' : 'Inactive'}
                      </span>
                    </TableCell>
                    <TableCell>{client.cases || client.case_count || 0}</TableCell>
                    <TableCell>{formatDate(client.createdAt || client.created_at)}</TableCell>
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell colSpan={6} className="text-center py-4">
                    No clients found. Try a different search or{' '}
                    <Link href="/clients/intake" className="underline text-primary">
                      add a new client
                    </Link>
                    .
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  )
}
