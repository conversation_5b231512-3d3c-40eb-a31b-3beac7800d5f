'use client'

import { useState, useEffect, useCallback } from 'react'
import { useRouter } from 'next/navigation'
import {
  Briefcase,
  Plus,
  Search,
  Filter,
  Calendar,
  Users,
  Clock,
  FileText,
  AlertCircle,
  CheckCircle,
  RefreshCw,
  MoreHorizontal,
  ChevronLeft,
  ChevronRight
} from 'lucide-react'

import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { useToast } from '@/components/ui/use-toast'
import { useSupabase } from '@/lib/supabase/provider'
import { useAuthenticatedFetch } from '@/hooks/useAuthenticatedFetch'
// Import Button for pagination fallback
import type { MouseEvent } from 'react'

// Define Case interface to type the data properly
interface Case {
  id: string;
  title: string;
  client_id: string;  // Match Supabase field name convention
  client_name: string; // From the backend processing
  status: string;
  metadata: {
    practiceArea?: string;
    priority?: string;
    [key: string]: any;
  };
  description: string | null;
  sensitive: boolean;
  created_at: string | null; // For filed date
  updated_at: string | null;
  creator?: {
    id: string;
    email: string;
    first_name: string | null;
    last_name: string | null;
  }[];
  // Virtual properties that might be calculated
  documentCount?: number;
  nextDeadline?: string | null;
  nextDeadlineTitle?: string | null;
}

// Define API response shape for /api/cases
interface CasesApiResponse {
  cases: Case[];
  totalPages: number;
}

// Define API response shape for /api/cases/stats
interface CasesStatsApiResponse {
  totalCases: number;
  activeCases: number;
  upcomingDeadlines: number;
  documentCount: number;
}

// Status and practice area options
const CASE_STATUSES = [
  { id: 'all', label: 'All Statuses' },
  { id: 'active', label: 'Active' },
  { id: 'pending', label: 'Pending' },
  { id: 'inactive', label: 'Inactive' },
  { id: 'closed', label: 'Closed' }
];

const PRACTICE_AREAS = [
  { id: 'all', label: 'All Practice Areas' },
  { id: 'personal-injury', label: 'Personal Injury' },
  { id: 'medical-malpractice', label: 'Medical Malpractice' },
  { id: 'employment', label: 'Employment' },
  { id: 'real-estate', label: 'Real Estate' }
];

export default function CasesPage() {
  const router = useRouter()
  const { supabase } = useSupabase()
  const { toast } = useToast()
  const { authedFetch, isReady } = useAuthenticatedFetch();

  // States
  const [searchQuery, setSearchQuery] = useState('')
  const [statusFilter, setStatusFilter] = useState('all')
  const [practiceAreaFilter, setPracticeAreaFilter] = useState('all')
  const [cases, setCases] = useState<Case[]>([])
  const [loading, setLoading] = useState(true)
  const [page, setPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)

  // Stats
  const [stats, setStats] = useState({
    totalCases: 0,
    activeCases: 0,
    upcomingDeadlines: 0,
    recentDocuments: 0
  })

  // Fetch cases and stats
  const fetchData = useCallback(async () => {
    setLoading(true);
    if (!authedFetch || !isReady) {
      console.warn('CasesPage: authedFetch not ready for fetching data.');
      toast({ title: 'Error', description: 'Could not fetch case data. Service unavailable.', variant: 'destructive' });
      setLoading(false);
      return;
    }

    try {
      // Build query string for filters
      const queryParams = new URLSearchParams();

      if (searchQuery) {
        queryParams.append('search', searchQuery)
      }

      if (statusFilter !== 'all') {
        queryParams.append('status', statusFilter)
      }

      if (practiceAreaFilter !== 'all') {
        queryParams.append('practiceArea', practiceAreaFilter)
      }

      queryParams.append('page', page.toString())
      queryParams.append('limit', '10') // 10 cases per page

      const queryString = queryParams.toString();
      const casesUrl = `/api/cases${queryString ? `?${queryString}` : ''}`;
      const statsUrl = '/api/cases/statistics';

      // Fetch cases and stats in parallel
      const [casesData, statsData] = await Promise.all([
        authedFetch<CasesApiResponse>(casesUrl), // Specify response type
        authedFetch<CasesStatsApiResponse>(statsUrl) // Specify response type
      ]);

      console.log('Cases Data:', casesData);
      console.log('Stats Data:', statsData);

      // Update state - Optional chaining added for safety
      setCases(casesData?.cases || [])
      setTotalPages(casesData?.totalPages || 1)

      setStats({
        totalCases: statsData?.totalCases || 0,
        activeCases: statsData?.activeCases || 0,
        upcomingDeadlines: statsData?.upcomingDeadlines || 0,
        recentDocuments: statsData?.documentCount || 0
      })
    } catch (_error) {
      console.error('Failed to fetch data:', error)
      // Check if error is an instance of Error before accessing message
      const errorMessage = error instanceof Error ? error.message : 'Failed to load cases';
      toast({
        title: 'Error',
        description: errorMessage,
        variant: 'destructive'
      })
      // Set empty cases on error
      setCases([])
      setTotalPages(1)
    } finally {
      setLoading(false)
    }
  }, [searchQuery, statusFilter, practiceAreaFilter, page, authedFetch, isReady, toast]);

  useEffect(() => {
    fetchData();
  }, [fetchData]);

  // Handle search
  const handleSearch = () => {
    setPage(1) // Reset to first page when searching
  }

  // Format date
  const formatDate = (dateString: string | null) => {
    if (!dateString) return 'None'

    try {
      const date = new Date(dateString)
      return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
      })
    } catch (_e) {
      console.error('Date parsing error:', e)
      return 'Invalid date'
    }
  }

  // Calculate days from now
  const getDaysFromNow = (dateString: string | null) => {
    if (!dateString) return null

    const now = new Date()
    const targetDate = new Date(dateString)
    const diffTime = targetDate.getTime() - now.getTime()
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))

    return diffDays
  }

  // Render status badge
  const renderStatusBadge = (status: string) => {
    switch(status) {
      case 'active':
        return <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">Active</Badge>
      case 'pending':
        return <Badge variant="outline" className="bg-yellow-50 text-yellow-700 border-yellow-200">Pending</Badge>
      case 'inactive':
        return <Badge variant="outline" className="bg-gray-50 text-gray-700 border-gray-200">Inactive</Badge>
      case 'closed':
        return <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">Closed</Badge>
      default:
        return <Badge variant="outline">Unknown</Badge>
    }
  }

  // Render priority indicator
  const renderPriority = (priority: string) => {
    switch(priority) {
      case 'high':
        return <div className="flex items-center"><div className="w-2 h-2 rounded-full bg-red-500 mr-2"></div>High</div>
      case 'medium':
        return <div className="flex items-center"><div className="w-2 h-2 rounded-full bg-yellow-500 mr-2"></div>Medium</div>
      case 'low':
        return <div className="flex items-center"><div className="w-2 h-2 rounded-full bg-blue-500 mr-2"></div>Low</div>
      default:
        return <div className="flex items-center"><div className="w-2 h-2 rounded-full bg-gray-500 mr-2"></div>Normal</div>
    }
  }

  return (
    <div className="container max-w-7xl mx-auto py-8 px-4">
      <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between mb-8">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Cases</h1>
          <p className="text-muted-foreground mt-1">
            Manage all your active and pending cases
          </p>
        </div>
        <div className="mt-4 lg:mt-0">
          <Button onClick={() => router.push('/cases/new')}>
            <Plus className="mr-2 h-4 w-4" />
            New Case
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Total Cases</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalCases}</div>
            <p className="text-xs text-muted-foreground mt-1">
              Across all practice areas
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Active Cases</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.activeCases}</div>
            <p className="text-xs text-muted-foreground mt-1">
              Currently in progress
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Upcoming Deadlines</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.upcomingDeadlines}</div>
            <p className="text-xs text-muted-foreground mt-1">
              Across all cases
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Case Documents</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.recentDocuments}</div>
            <p className="text-xs text-muted-foreground mt-1">
              Total case-linked documents
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Search and filters */}
      <Card className="mb-6">
        <CardHeader className="pb-3">
          <CardTitle className="text-lg flex items-center">
            <Filter className="h-4 w-4 mr-2" />
            Search and Filter
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <div className="relative">
                <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search cases or clients..."
                  className="pl-8"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  onKeyDown={(e) => e.key === 'Enter' && handleSearch()}
                />
              </div>
            </div>

            <div>
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="Status" />
                </SelectTrigger>
                <SelectContent>
                  {CASE_STATUSES.map(status => (
                    <SelectItem key={status.id} value={status.id}>
                      {status.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <Select value={practiceAreaFilter} onValueChange={setPracticeAreaFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="Practice Area" />
                </SelectTrigger>
                <SelectContent>
                  {PRACTICE_AREAS.map(area => (
                    <SelectItem key={area.id} value={area.id}>
                      {area.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="md:col-span-3 flex justify-end">
              <Button variant="outline" onClick={handleSearch}>
                <Search className="mr-2 h-4 w-4" />
                Apply Filters
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Cases Table */}
      <Card>
        <CardContent className="pt-6">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Case</TableHead>
                <TableHead>Client</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Practice Area</TableHead>
                <TableHead>Filed Date</TableHead>
                <TableHead>Next Deadline</TableHead>
                <TableHead>Priority</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {loading ? (
                <TableRow>
                  <TableCell colSpan={8} className="text-center py-8">
                    <div className="flex justify-center items-center">
                      <RefreshCw className="h-5 w-5 mr-2 animate-spin" />
                      Loading cases...
                    </div>
                  </TableCell>
                </TableRow>
              ) : cases.length > 0 ? (
                cases.map((caseItem) => {
                  const daysUntilDeadline = getDaysFromNow(caseItem.nextDeadline ?? null)

                  return (
                    <TableRow
                      key={caseItem.id}
                      className="cursor-pointer"
                      onClick={() => router.push(`/cases/${caseItem.id}`)}
                    >
                      <TableCell>
                        <div className="flex items-center">
                          <Briefcase className="h-4 w-4 mr-2 text-muted-foreground" />
                          <span className="font-medium">{caseItem.title}</span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center">
                          <Users className="h-4 w-4 mr-2 text-muted-foreground" />
                          {caseItem.client_name}
                        </div>
                      </TableCell>
                      <TableCell>{renderStatusBadge(caseItem.status)}</TableCell>
                      <TableCell>{caseItem.metadata?.practiceArea || 'N/A'}</TableCell>
                      <TableCell>{formatDate(caseItem.created_at ?? null)}</TableCell>
                      <TableCell>
                        {caseItem.nextDeadline ? (
                          <div>
                            <div className="font-medium">{caseItem.nextDeadlineTitle || 'Next Deadline'}</div>
                            <div className="text-sm text-muted-foreground flex items-center">
                              <Calendar className="h-3 w-3 mr-1" />
                              {formatDate(caseItem.nextDeadline ?? null)}
                              {daysUntilDeadline !== null && (
                                <span className={`ml-2 ${daysUntilDeadline <= 7 ? 'text-red-600 font-medium' : ''}`}>
                                  ({daysUntilDeadline > 0 ? `${daysUntilDeadline} days` : 'Today'})
                                </span>
                              )}
                            </div>
                          </div>
                        ) : (
                          <span className="text-muted-foreground">None</span>
                        )}
                      </TableCell>
                      <TableCell>{renderPriority(caseItem.metadata?.priority || 'medium')}</TableCell>
                      <TableCell>
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="sm" className="h-8 w-8 p-0" onClick={(e) => e.stopPropagation()}>
                              <span className="sr-only">Open menu</span>
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent onClick={(e) => e.stopPropagation()} align="end">
                            <DropdownMenuItem onClick={() => router.push(`/cases/${caseItem.id}`)}>
                              <Briefcase className="mr-2 h-4 w-4" />
                              View Case
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => router.push(`/cases/${caseItem.id}/edit`)}>
                              <FileText className="mr-2 h-4 w-4" />
                              Edit Details
                            </DropdownMenuItem>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem onClick={() => router.push(`/cases/${caseItem.id}/documents`)}>
                              <FileText className="mr-2 h-4 w-4" />
                              View Documents
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => router.push(`/cases/${caseItem.id}/deadlines`)}>
                              <Calendar className="mr-2 h-4 w-4" />
                              Manage Deadlines
                            </DropdownMenuItem>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem onClick={() => router.push(`/clients/${caseItem.client_id}`)}>
                              <Users className="mr-2 h-4 w-4" />
                              View Client
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  )
                })
              ) : (
                <TableRow>
                  <TableCell colSpan={8} className="text-center py-8">
                    <p>No cases found matching your criteria.</p>
                    <Button
                      variant="link"
                      className="mt-2"
                      onClick={() => {
                        setSearchQuery('')
                        setStatusFilter('all')
                        setPracticeAreaFilter('all')
                      }}
                    >
                      Clear all filters
                    </Button>
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </CardContent>

        {/* Pagination */}
        {totalPages > 1 && (
          <CardFooter className="flex justify-center py-4">
            <nav className="mx-auto flex w-full justify-center">
              <ul className="flex flex-row items-center gap-1">
                <li>
                  <Button
                    variant={page === 1 ? 'outline' : 'ghost'}
                    size="sm"
                    className="h-9 w-9"
                    onClick={() => setPage(1)}
                    disabled={page === 1}
                  >
                    <span className="sr-only">Go to first page</span>
                    <span>First</span>
                  </Button>
                </li>

                <li>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-9 w-9"
                    onClick={() => setPage(prev => Math.max(prev - 1, 1))}
                    disabled={page === 1}
                  >
                    <span className="sr-only">Go to previous page</span>
                    <ChevronLeft className="h-4 w-4" />
                  </Button>
                </li>

                <li>
                  <span className="px-4 py-2">
                    Page {page} of {totalPages}
                  </span>
                </li>

                <li>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-9 w-9"
                    onClick={() => setPage(prev => Math.min(prev + 1, totalPages))}
                    disabled={page === totalPages}
                  >
                    <span className="sr-only">Go to next page</span>
                    <ChevronRight className="h-4 w-4" />
                  </Button>
                </li>

                <li>
                  <Button
                    variant={page === totalPages ? 'outline' : 'ghost'}
                    size="sm"
                    className="h-9 w-9"
                    onClick={() => setPage(totalPages)}
                    disabled={page === totalPages}
                  >
                    <span className="sr-only">Go to last page</span>
                    <span>Last</span>
                  </Button>
                </li>
              </ul>
            </nav>
          </CardFooter>
        )}
      </Card>
    </div>
  )
}
