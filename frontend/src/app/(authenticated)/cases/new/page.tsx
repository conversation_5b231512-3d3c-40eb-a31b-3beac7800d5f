'use client'

import { useState, useEffect } from 'react'
import { use<PERSON><PERSON><PERSON> } from 'next/navigation'
import { zodResolver } from '@hookform/resolvers/zod'
import { useForm } from 'react-hook-form'
import * as z from 'zod'
import {
  Briefcase,
  ArrowLeft,
  Users,
  CalendarDays,
  CircleDollarSign,
  FileText,
  Contact,
  Smartphone
} from 'lucide-react'

import { Button } from '@/components/ui/button'
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbSeparator,
} from '@/components/ui/breadcrumb'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Checkbox } from '@/components/ui/checkbox'
import { DatePicker } from '@/components/ui/date-picker'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { useSupabase } from '@/lib/supabase/provider'
import { useToast } from '@/components/ui/use-toast'

// Practice area options
const PRACTICE_AREAS = [
  { id: 'personal-injury', label: 'Personal Injury' },
  { id: 'medical-malpractice', label: 'Medical Malpractice' },
  { id: 'employment', label: 'Employment' },
  { id: 'real-estate', label: 'Real Estate' },
  { id: 'other', label: 'Other' }
];

// Case type options
const CASE_TYPES = [
  { id: 'litigation', label: 'Litigation' },
  { id: 'settlement', label: 'Settlement' },
  { id: 'appeal', label: 'Appeal' },
  { id: 'administrative', label: 'Administrative' },
  { id: 'other', label: 'Other' }
];

// Mock clients data - would be fetched from Supabase
const mockClients = [
  { id: 'client1', name: 'Sarah Johnson' },
  { id: 'client2', name: 'Robert Williams' },
  { id: 'client3', name: 'John Smith' },
  { id: 'client4', name: 'Maria Garcia' },
  { id: 'client5', name: 'David Thompson' }
];

// Mock attorney data - would be fetched from Supabase
const mockAttorneys = [
  { id: 'atty1', name: 'Michael Wilson' },
  { id: 'atty2', name: 'Jessica Martinez' },
  { id: 'atty3', name: 'Thomas Clark' }
];

// Case form schema
const caseFormSchema = z.object({
  title: z.string().min(3, {
    message: "Case title must be at least 3 characters.",
  }),
  clientId: z.string({
    required_error: "Please select a client.",
  }),
  practiceArea: z.string({
    required_error: "Please select a practice area.",
  }),
  caseType: z.string({
    required_error: "Please select a case type.",
  }),
  attorneyId: z.string({
    required_error: "Please select a primary attorney.",
  }),
  filedDate: z.date().optional(),
  incidentDate: z.date().optional(),
  description: z.string().optional(),
  courtName: z.string().optional(),
  caseNumber: z.string().optional(),
  opposingParty: z.string().optional(),
  opposingCounsel: z.string().optional(),
  opposingCounselPhone: z.string().optional(),
  opposingCounselEmail: z.string().optional(),
  settlementAmount: z.string().optional(),
  priority: z.enum(['low', 'medium', 'high']),
  status: z.enum(['active', 'pending', 'inactive']),
  isContingency: z.boolean().default(false),
  contingencyPercentage: z.string().optional(),
  notes: z.string().optional(),
});

export default function NewCasePage() {
  const router = useRouter()
  const { supabase } = useSupabase()
  const { toast } = useToast()

  const [clients, setClients] = useState<any[]>([])
  const [attorneys, setAttorneys] = useState<any[]>([])
  const [isSubmitting, setIsSubmitting] = useState(false)

  // Initialize form
  const form = useForm<z.infer<typeof caseFormSchema>>({
    resolver: zodResolver(caseFormSchema),
    defaultValues: {
      title: '',
      clientId: '',
      practiceArea: '',
      caseType: '',
      attorneyId: '',
      description: '',
      courtName: '',
      caseNumber: '',
      opposingParty: '',
      opposingCounsel: '',
      opposingCounselPhone: '',
      opposingCounselEmail: '',
      settlementAmount: '',
      priority: 'medium',
      status: 'active',
      isContingency: false,
      contingencyPercentage: '33',
      notes: '',
    },
  })

  // Get field values for conditional rendering
  const isContingency = form.watch('isContingency')

  // Load clients and attorneys
  useEffect(() => {
    const fetchData = async () => {
      try {
        // In production, these would be API calls
        // const { data: clientsData } = await supabase
        //   .from('clients')
        //   .select('id, first_name, last_name')

        // Using mock data for now
        setClients(mockClients)
        setAttorneys(mockAttorneys)
      } catch (_error) {
        console.error('Error fetching data:', error)
        toast({
          title: 'Error',
          description: 'Failed to load client and attorney data',
          variant: 'destructive'
        })
      }
    }

    fetchData()
  }, [toast])

  // Handle form submission
  const onSubmit = async (data: z.infer<typeof caseFormSchema>) => {
    setIsSubmitting(true)
    try {
      // In production, this would save to Supabase
      // const { data: newCase, error } = await supabase
      //   .from('cases')
      //   .insert([data])
      //   .select()

      // For demo, simulate a successful creation
      await new Promise((resolve) => setTimeout(resolve, 1000))

      console.log('Form data:', data)

      toast({
        title: 'Case Created',
        description: `${data.title} has been successfully created.`,
      })

      // Redirect to cases list
      router.push('/cases')
    } catch (_error) {
      console.error('Error creating case:', error)
      toast({
        title: 'Error',
        description: 'Failed to create case. Please try again.',
        variant: 'destructive'
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <div className="container max-w-5xl mx-auto py-8 px-4">
      {/* Breadcrumb */}
      <div className="mb-4">
        <Breadcrumb>
          <BreadcrumbList>
            <BreadcrumbItem>
              <BreadcrumbLink href="/cases">Cases</BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbLink href="/cases/new">New Case</BreadcrumbLink>
            </BreadcrumbItem>
          </BreadcrumbList>
        </Breadcrumb>
      </div>

      <div className="mb-8 flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Create New Case</h1>
          <p className="text-muted-foreground mt-1">
            Enter the details for a new legal case
          </p>
        </div>
        <Button variant="outline" onClick={() => router.back()}>
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back
        </Button>
      </div>

      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
          <Tabs defaultValue="basic" className="w-full">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="basic">Basic Information</TabsTrigger>
              <TabsTrigger value="details">Case Details</TabsTrigger>
              <TabsTrigger value="opposing">Opposing Party</TabsTrigger>
            </TabsList>

            {/* Basic Information Tab */}
            <TabsContent value="basic">
              <Card>
                <CardHeader>
                  <CardTitle>Basic Case Information</CardTitle>
                  <CardDescription>
                    Enter the essential information about the case
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <FormField
                      control={form.control}
                      name="title"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Case Title</FormLabel>
                          <FormControl>
                            <Input placeholder="e.g. Johnson v. City Hospital" {...field} />
                          </FormControl>
                          <FormDescription>
                            A descriptive name for the case
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="clientId"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Client</FormLabel>
                          <Select
                            onValueChange={field.onChange}
                            defaultValue={field.value}
                          >
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Select a client" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              {clients.map(client => (
                                <SelectItem key={client.id} value={client.id}>
                                  {client.name}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                          <FormDescription>
                            The client associated with this case
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <FormField
                      control={form.control}
                      name="practiceArea"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Practice Area</FormLabel>
                          <Select
                            onValueChange={field.onChange}
                            defaultValue={field.value}
                          >
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Select a practice area" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              {PRACTICE_AREAS.map(area => (
                                <SelectItem key={area.id} value={area.id}>
                                  {area.label}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                          <FormDescription>
                            The legal practice area for this case
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="caseType"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Case Type</FormLabel>
                          <Select
                            onValueChange={field.onChange}
                            defaultValue={field.value}
                          >
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Select a case type" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              {CASE_TYPES.map(type => (
                                <SelectItem key={type.id} value={type.id}>
                                  {type.label}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                          <FormDescription>
                            The type of legal matter
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <FormField
                      control={form.control}
                      name="attorneyId"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Primary Attorney</FormLabel>
                          <Select
                            onValueChange={field.onChange}
                            defaultValue={field.value}
                          >
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Select attorney" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              {attorneys.map(attorney => (
                                <SelectItem key={attorney.id} value={attorney.id}>
                                  {attorney.name}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                          <FormDescription>
                            Lead attorney on the case
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="status"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Status</FormLabel>
                          <Select
                            onValueChange={field.onChange}
                            defaultValue={field.value}
                          >
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Select status" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              <SelectItem value="active">Active</SelectItem>
                              <SelectItem value="pending">Pending</SelectItem>
                              <SelectItem value="inactive">Inactive</SelectItem>
                            </SelectContent>
                          </Select>
                          <FormDescription>
                            Current status of the case
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="priority"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Priority</FormLabel>
                          <Select
                            onValueChange={field.onChange}
                            defaultValue={field.value}
                          >
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Select priority" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              <SelectItem value="low">Low</SelectItem>
                              <SelectItem value="medium">Medium</SelectItem>
                              <SelectItem value="high">High</SelectItem>
                            </SelectContent>
                          </Select>
                          <FormDescription>
                            Case priority level
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <FormField
                    control={form.control}
                    name="description"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Case Description</FormLabel>
                        <FormControl>
                          <Textarea
                            placeholder="Brief description of the case..."
                            className="min-h-[120px]"
                            {...field}
                          />
                        </FormControl>
                        <FormDescription>
                          Summarize the key aspects of the case
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <FormField
                      control={form.control}
                      name="incidentDate"
                      render={({ field }) => (
                        <FormItem className="flex flex-col">
                          <FormLabel>Incident Date</FormLabel>
                          <DatePicker
                            date={field.value}
                            setDate={field.onChange}
                          />
                          <FormDescription>
                            When the incident occurred
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="filedDate"
                      render={({ field }) => (
                        <FormItem className="flex flex-col">
                          <FormLabel>Filing Date</FormLabel>
                          <DatePicker
                            date={field.value}
                            setDate={field.onChange}
                          />
                          <FormDescription>
                            When the case was filed
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <div>
                    <FormField
                      control={form.control}
                      name="isContingency"
                      render={({ field }) => (
                        <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                          <FormControl>
                            <Checkbox
                              checked={field.value}
                              onCheckedChange={field.onChange}
                            />
                          </FormControl>
                          <div className="space-y-1 leading-none">
                            <FormLabel>Contingency Fee</FormLabel>
                            <FormDescription>
                              This is a contingency fee case
                            </FormDescription>
                          </div>
                        </FormItem>
                      )}
                    />

                    {isContingency && (
                      <div className="mt-4">
                        <FormField
                          control={form.control}
                          name="contingencyPercentage"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Contingency Percentage</FormLabel>
                              <FormControl>
                                <div className="flex items-center">
                                  <Input
                                    type="number"
                                    min="1"
                                    max="100"
                                    {...field}
                                    className="max-w-[100px]"
                                  />
                                  <span className="ml-2">%</span>
                                </div>
                              </FormControl>
                              <FormDescription>
                                Percentage of recovery as fee
                              </FormDescription>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            {/* Case Details Tab */}
            <TabsContent value="details">
              <Card>
                <CardHeader>
                  <CardTitle>Additional Case Details</CardTitle>
                  <CardDescription>
                    Add more specific information about the case
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <FormField
                      control={form.control}
                      name="courtName"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Court Name</FormLabel>
                          <FormControl>
                            <Input placeholder="e.g. Travis County District Court" {...field} />
                          </FormControl>
                          <FormDescription>
                            The court where the case is filed
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="caseNumber"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Case/Docket Number</FormLabel>
                          <FormControl>
                            <Input placeholder="e.g. CV-2025-12345" {...field} />
                          </FormControl>
                          <FormDescription>
                            The official case number
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <FormField
                    control={form.control}
                    name="settlementAmount"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Settlement Amount Target</FormLabel>
                        <FormControl>
                          <div className="relative">
                            <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                              <CircleDollarSign className="h-4 w-4 text-muted-foreground" />
                            </div>
                            <Input className="pl-10" placeholder="e.g. 250000" {...field} />
                          </div>
                        </FormControl>
                        <FormDescription>
                          Target settlement amount (if applicable)
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="notes"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Additional Notes</FormLabel>
                        <FormControl>
                          <Textarea
                            placeholder="Any additional notes about the case..."
                            className="min-h-[150px]"
                            {...field}
                          />
                        </FormControl>
                        <FormDescription>
                          Internal notes, special considerations, etc.
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </CardContent>
              </Card>
            </TabsContent>

            {/* Opposing Party Tab */}
            <TabsContent value="opposing">
              <Card>
                <CardHeader>
                  <CardTitle>Opposing Party Information</CardTitle>
                  <CardDescription>
                    Details about the opposing party and their representation
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <FormField
                    control={form.control}
                    name="opposingParty"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Opposing Party</FormLabel>
                        <FormControl>
                          <Input placeholder="Name of opposing party" {...field} />
                        </FormControl>
                        <FormDescription>
                          The name of the defendant/respondent
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="opposingCounsel"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Opposing Counsel</FormLabel>
                        <FormControl>
                          <Input placeholder="Name of opposing attorney" {...field} />
                        </FormControl>
                        <FormDescription>
                          Attorney representing the opposing party
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <FormField
                      control={form.control}
                      name="opposingCounselPhone"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Counsel Phone</FormLabel>
                          <FormControl>
                            <div className="relative">
                              <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                                <Smartphone className="h-4 w-4 text-muted-foreground" />
                              </div>
                              <Input className="pl-10" placeholder="Phone number" {...field} />
                            </div>
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="opposingCounselEmail"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Counsel Email</FormLabel>
                          <FormControl>
                            <div className="relative">
                              <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                                <Contact className="h-4 w-4 text-muted-foreground" />
                              </div>
                              <Input className="pl-10" placeholder="Email address" {...field} />
                            </div>
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>

          <div className="flex justify-end gap-4 mt-8">
            <Button variant="outline" type="button" onClick={() => router.back()}>
              Cancel
            </Button>
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting ? (
                <>
                  <div className="h-4 w-4 mr-2 animate-spin rounded-full border-2 border-current border-t-transparent" />
                  Saving...
                </>
              ) : (
                'Create Case'
              )}
            </Button>
          </div>
        </form>
      </Form>
    </div>
  )
}
