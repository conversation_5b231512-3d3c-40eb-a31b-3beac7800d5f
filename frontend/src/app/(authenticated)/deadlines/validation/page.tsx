'use client'

import { useState, useEffect, useCallback } from 'react'
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { AlertCircle, Calendar, CheckCircle, Clock, FileText } from 'lucide-react'
import { DeadlineValidationModal } from './validation-modal'
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { useAuthenticatedFetch } from '@/hooks/useAuthenticatedFetch'
import { Deadline } from '@/types/deadlines'; // Import the shared Deadline type

// Define a type for the validation stats
interface ValidationStats {
  counts: {
    pending: number;
    validated: number;
    rejected: number;
  };
  percentages: {
    pending: number;
    validated: number;
    rejected: number;
  };
  total: number;
}

// Define API response for pending deadlines
interface PendingDeadlinesApiResponse {
  deadlines?: Deadline[];
}

export default function DeadlineValidationPage() {
  const [pendingDeadlines, setPendingDeadlines] = useState<Deadline[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [stats, setStats] = useState<ValidationStats>({
    counts: { pending: 0, validated: 0, rejected: 0 },
    percentages: { pending: 0, validated: 0, rejected: 0 },
    total: 0
  })
  const [selectedDeadline, setSelectedDeadline] = useState<Deadline | null>(null)
  const [isModalOpen, setIsModalOpen] = useState(false)
  const { authedFetch, isReady } = useAuthenticatedFetch()

  useEffect(() => {
    fetchPendingDeadlines()
    fetchValidationStats()
  }, [])

  const fetchPendingDeadlines = useCallback(async () => {
    setIsLoading(true)
    if (!authedFetch || !isReady) {
      console.warn('fetchPendingDeadlines: authedFetch not ready.')
      setIsLoading(false)
      return
    }
    try {
      // Use generic type with authedFetch
      const data = await authedFetch<PendingDeadlinesApiResponse>('/api/deadlines/validation/pending')
      // Access the 'deadlines' property directly from the parsed response
      setPendingDeadlines(data?.deadlines || [])
    } catch (_error) {
      console.error('Error fetching pending deadlines:', error)
    } finally {
      setIsLoading(false)
    }
  }, [authedFetch, isReady])

  const fetchValidationStats = useCallback(async () => {
    try {
      // Use generic type with authedFetch
      const data = await authedFetch<ValidationStats>('/api/deadlines/validation/stats')
      // Use the parsed data directly
      // Add null check for safety, although ValidationStats expects non-null properties
      if (data) {
        setStats(data)
      }
    } catch (_error) {
      console.error('Error fetching validation stats:', error)
    }
  }, [authedFetch, isReady])

  async function handleValidation(deadlineId: string, action: 'validate' | 'reject', note = '') {
    try {
      await authedFetch(`/api/deadlines/validation/${deadlineId}`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ action, note })
      })

      // Refresh data
      fetchPendingDeadlines()
      fetchValidationStats()

      // Close modal if open
      setIsModalOpen(false)
    } catch (_error) {
      console.error(`Error ${action} deadline:`, error)
    }
  }

  function openValidationModal(deadline: Deadline) {
    setSelectedDeadline(deadline)
    setIsModalOpen(true)
  }

  function getPriorityVariant(priority: string | undefined): "default" | "destructive" | "outline" | "secondary" {
    switch (priority?.toLowerCase()) {
      case 'critical': return 'destructive'
      case 'high': return 'default'
      case 'medium': return 'secondary'
      case 'low': return 'outline'
      default: return 'secondary'
    }
  }

  function formatDate(dateString: string | undefined): string {
    if (!dateString) return 'No date'
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  }

  return (
    <div className="container mx-auto py-6 max-w-7xl">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Deadline Validation</h1>
        <Button
          variant="outline"
          onClick={() => {
            fetchPendingDeadlines()
            fetchValidationStats()
          }}
        >
          Refresh
        </Button>
      </div>

      <Tabs defaultValue="pending" className="w-full mb-8">
        <TabsList className="mb-4">
          <TabsTrigger value="pending">Pending Validation</TabsTrigger>
          <TabsTrigger value="stats">Validation Stats</TabsTrigger>
        </TabsList>

        <TabsContent value="pending">
          {/* Pending Deadlines List */}
          {isLoading ? (
            <div className="flex justify-center py-12">
              <p>Loading deadlines...</p>
            </div>
          ) : pendingDeadlines.length === 0 ? (
            <Card>
              <CardContent className="py-12">
                <p className="text-center text-muted-foreground">No pending deadlines to validate</p>
              </CardContent>
            </Card>
          ) : (
            <div className="space-y-4">
              {pendingDeadlines.map((deadline) => (
                <Card key={deadline.id} className="border-l-4 border-l-amber-400 hover:shadow-md transition-shadow">
                  <CardHeader className="pb-2">
                    <div className="flex justify-between items-start">
                      <div>
                        <CardTitle className="text-lg">{deadline.description}</CardTitle>
                        <CardDescription className="flex items-center mt-1">
                          <Calendar className="h-4 w-4 mr-1" />
                          {formatDate(deadline.due_date)}
                        </CardDescription>
                      </div>
                      <Badge variant={getPriorityVariant(deadline.priority)}>
                        {deadline.priority || 'Medium'}
                      </Badge>
                    </div>
                  </CardHeader>

                  <CardContent className="pb-2">
                    <div className="text-sm grid grid-cols-1 md:grid-cols-2 gap-2">
                      <div>
                        <p><strong>Jurisdiction:</strong> {deadline.jurisdiction}</p>
                        {deadline.legal_basis && (
                          <p><strong>Legal Basis:</strong> {deadline.legal_basis}</p>
                        )}
                      </div>
                      <div>
                        {deadline.document_id && (
                          <p className="flex items-center text-muted-foreground">
                            <FileText className="h-3 w-3 mr-1" />
                            {deadline.documents?.title || 'Document ' + deadline.document_id.substring(0, 8)}
                          </p>
                        )}
                        {deadline.case_id && (
                          <p className="text-muted-foreground">
                            <strong>Case:</strong> {deadline.cases?.title || deadline.case_id.substring(0, 8)}
                          </p>
                        )}
                      </div>
                    </div>
                  </CardContent>

                  <CardFooter className="flex justify-end space-x-2 pt-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => openValidationModal(deadline)}
                    >
                      Review
                    </Button>
                    <Button
                      variant="destructive"
                      size="sm"
                      onClick={() => handleValidation(deadline.id, 'reject')}
                    >
                      Reject
                    </Button>
                    <Button
                      variant="default"
                      size="sm"
                      onClick={() => handleValidation(deadline.id, 'validate')}
                    >
                      Validate
                    </Button>
                  </CardFooter>
                </Card>
              ))}
            </div>
          )}
        </TabsContent>

        <TabsContent value="stats">
          {/* Stats Cards */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-8">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-amber-500 flex items-center">
                  <Clock className="mr-2 h-4 w-4" />
                  Pending
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-3xl font-bold">{stats.counts.pending}</p>
                <p className="text-muted-foreground">{stats.percentages.pending}% of total</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-green-500 flex items-center">
                  <CheckCircle className="mr-2 h-4 w-4" />
                  Validated
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-3xl font-bold">{stats.counts.validated}</p>
                <p className="text-muted-foreground">{stats.percentages.validated}% of total</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-red-500 flex items-center">
                  <AlertCircle className="mr-2 h-4 w-4" />
                  Rejected
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-3xl font-bold">{stats.counts.rejected}</p>
                <p className="text-muted-foreground">{stats.percentages.rejected}% of total</p>
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>Total Deadlines</CardTitle>
              <CardDescription>
                Overall statistics for all deadlines in the system
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="w-full bg-secondary rounded-full h-4 mb-4">
                <div
                  className="bg-green-500 h-4 rounded-l-full"
                  style={{ width: `${stats.percentages.validated}%` }}
                ></div>
              </div>

              <div className="grid grid-cols-3 gap-4 text-center">
                <div>
                  <p className="text-sm text-muted-foreground">Total</p>
                  <p className="text-xl font-bold">{stats.total}</p>
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">Validated</p>
                  <p className="text-xl font-bold text-green-500">{stats.percentages.validated}%</p>
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">Pending</p>
                  <p className="text-xl font-bold text-amber-500">{stats.percentages.pending}%</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Validation Modal */}
      <DeadlineValidationModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        deadline={selectedDeadline}
        onValidate={(note: string) => selectedDeadline && handleValidation(selectedDeadline.id, 'validate', note)}
        onReject={(note: string) => selectedDeadline && handleValidation(selectedDeadline.id, 'reject', note)}
      />
    </div>
  )
}
