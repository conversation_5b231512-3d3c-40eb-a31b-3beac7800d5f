'use client'

import { useState, useEffect } from 'react'
import { useRout<PERSON> } from 'next/navigation'
import {
  Clock,
  CheckCircle,
  AlertCircle,
  FileText,
  RefreshCcw,
  ArrowLeft,
  FileUp,
  BarChart3
} from 'lucide-react'

import { Button } from '@/components/ui/button'
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
  CardFooter
} from '@/components/ui/card'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbSeparator,
} from '@/components/ui/breadcrumb'
import {
  <PERSON><PERSON>,
  <PERSON>bsContent,
  TabsList,
  TabsTrigger,
} from "@/components/ui/tabs"
import { Progress } from '@/components/ui/progress'
import { Badge } from '@/components/ui/badge'
import { useSupabase } from '@/lib/supabase/provider'
import { useToast } from '@/components/ui/use-toast'

// Define interfaces for job and step structures
interface ProcessingStep {
  id: string;
  name: string;
  status: 'pending' | 'processing' | 'completed' | 'error';
  message?: string;
}

interface ProcessingJob {
  id: string;
  documentId: string;
  documentTitle: string;
  status: 'processing' | 'completed' | 'error';
  progress: number;
  steps: ProcessingStep[];
  startedAt: string;
  eta: string | null;
  completedAt?: string | null;
}

// Sample data - would be replaced with actual data from Supabase
const mockProcessingJobs: ProcessingJob[] = [
  {
    id: 'job1',
    documentId: 'doc3',
    documentTitle: 'Police Report - Smith Accident',
    startedAt: '2025-04-05T09:20:00Z',
    status: 'processing',
    progress: 45,
    eta: '2025-04-05T09:35:00Z',
    steps: [
      { id: 'step1', name: 'extraction', status: 'completed', message: 'Text extracted successfully' },
      { id: 'step2', name: 'analysis', status: 'processing', message: 'Analyzing document content' },
      { id: 'step3', name: 'classification', status: 'pending', message: 'Pending classification' },
      { id: 'step4', name: 'indexing', status: 'pending', message: 'Pending indexing' }
    ]
  },
  {
    id: 'job2',
    documentId: 'doc6',
    documentTitle: 'Medical Invoice - Williams',
    startedAt: '2025-04-05T09:15:00Z',
    status: 'processing',
    progress: 75,
    eta: '2025-04-05T09:25:00Z',
    steps: [
      { id: 'step5', name: 'extraction', status: 'completed', message: 'Text extracted successfully' },
      { id: 'step6', name: 'analysis', status: 'completed', message: 'Analysis completed' },
      { id: 'step7', name: 'classification', status: 'processing', message: 'Classifying document type' },
      { id: 'step8', name: 'indexing', status: 'pending', message: 'Pending indexing' }
    ]
  },
  {
    id: 'job3',
    documentId: 'doc7',
    documentTitle: 'Witness Statement - Jones Case',
    startedAt: '2025-04-05T09:00:00Z',
    status: 'error',
    progress: 30,
    eta: null,
    steps: [
      { id: 'step9', name: 'extraction', status: 'completed', message: 'Text extracted successfully' },
      { id: 'step10', name: 'analysis', status: 'error', message: 'Error analyzing content: Poor image quality' },
      { id: 'step11', name: 'classification', status: 'pending', message: 'Pending classification' },
      { id: 'step12', name: 'indexing', status: 'pending', message: 'Pending indexing' }
    ]
  }
];

const mockCompletedJobs: ProcessingJob[] = [
  {
    id: 'job4',
    documentId: 'doc1',
    documentTitle: 'Medical Records - Johnson',
    startedAt: '2025-04-01T10:30:00Z',
    status: 'completed',
    progress: 100,
    eta: null,
    completedAt: '2025-04-01T10:45:00Z',
    steps: [
      { id: 'step13', name: 'extraction', status: 'completed', message: 'Text extracted successfully' },
      { id: 'step14', name: 'analysis', status: 'completed', message: 'Analysis completed' },
      { id: 'step15', name: 'classification', status: 'completed', message: 'Classified as medical record' },
      { id: 'step16', name: 'indexing', status: 'completed', message: 'Document indexed successfully' }
    ]
  },
  {
    id: 'job5',
    documentId: 'doc2',
    documentTitle: 'Insurance Claim Form',
    startedAt: '2025-04-03T14:45:00Z',
    status: 'completed',
    progress: 100,
    eta: null,
    completedAt: '2025-04-03T15:00:00Z',
    steps: [
      { id: 'step17', name: 'extraction', status: 'completed', message: 'Text extracted successfully' },
      { id: 'step18', name: 'analysis', status: 'completed', message: 'Analysis completed' },
      { id: 'step19', name: 'classification', status: 'completed', message: 'Classified as insurance document' },
      { id: 'step20', name: 'indexing', status: 'completed', message: 'Document indexed successfully' }
    ]
  }
];

// Processing statistics
const mockStats = {
  processingCount: 2,
  completedCount: 15,
  errorCount: 1,
  averageProcessingTime: 840, // seconds
  successRate: 93.75 // percentage
};

export default function ProcessingStatusPage() {
  const router = useRouter()
  const { supabase } = useSupabase()
  const { toast } = useToast()

  const [activeJobs, setActiveJobs] = useState<ProcessingJob[]>([])
  const [completedJobs, setCompletedJobs] = useState<ProcessingJob[]>([])
  const [stats, setStats] = useState<any>(null)
  const [loading, setLoading] = useState(true)
  const [refreshing, setRefreshing] = useState(false)

  // Fetch processing jobs
  useEffect(() => {
    const fetchJobs = async () => {
      setLoading(true)
      try {
        // In production, this would make an API call
        // const response = await fetch('/api/documents/processing');
        // const data = await response.json();

        // Using mock data for now
        setActiveJobs(mockProcessingJobs)
        setCompletedJobs(mockCompletedJobs)
        setStats(mockStats)
      } catch (_error) {
        console.error('Error fetching processing jobs:', error)
        toast({
          title: 'Error',
          description: 'Failed to load processing jobs',
          variant: 'destructive'
        })
      } finally {
        setLoading(false)
      }
    }

    fetchJobs()

    // Set up polling for active jobs (every 10 seconds)
    const pollingInterval = setInterval(fetchJobs, 10000)

    return () => clearInterval(pollingInterval)
  }, [toast])

  // Handle manual refresh
  const handleRefresh = async () => {
    setRefreshing(true)
    try {
      // In production, this would make an API call
      // const response = await fetch('/api/documents/processing');
      // const data = await response.json();

      // Using mock data with a delay to simulate refresh
      await new Promise(resolve => setTimeout(resolve, 800))

      // Simulate a slight change in the data for visual feedback
      const updatedJobs = [...mockProcessingJobs]
      updatedJobs.forEach(job => {
        if (job.status === 'processing') {
          job.progress = Math.min(job.progress + 10, 95)
          if (job.progress > 60) {
            // Move to the next step if progress is good
            for (let i = 0; i < job.steps.length; i++) {
              if (job.steps[i].status === 'processing') {
                job.steps[i].status = 'completed'
                if (i < job.steps.length - 1) {
                  job.steps[i + 1].status = 'processing'
                }
                break
              }
            }
          }
        }
      })

      setActiveJobs(updatedJobs)

      toast({
        title: 'Refreshed',
        description: 'Processing status updated',
      })
    } catch (_error) {
      toast({
        title: 'Error',
        description: 'Failed to refresh processing status',
        variant: 'destructive'
      })
    } finally {
      setRefreshing(false)
    }
  }

  // Handle retry for failed jobs
  const handleRetry = async (jobId: string) => {
    try {
      // In production, this would make an API call
      // await fetch(`/api/documents/processing/${jobId}/retry`, { method: 'POST' });

      // Simulate retry success
      const updatedJobs = activeJobs.map(job => {
        if (job.id === jobId) {
          // Assert the returned object matches the ProcessingJob type
          return {
            ...job,
            status: 'processing',
            progress: 10,
            steps: job.steps.map((step: ProcessingStep) => ({
              ...step,
              status: step.status === 'error' ? 'processing' : step.status as ProcessingStep['status'],
              message: step.status === 'error' ? 'Retrying...' : step.message
            }))
          } as ProcessingJob;
        }
        return job
      })

      setActiveJobs(updatedJobs)

      toast({
        title: 'Processing Restarted',
        description: 'The document processing has been restarted.',
      })
    } catch (_error) {
      toast({
        title: 'Error',
        description: 'Failed to restart processing',
        variant: 'destructive'
      })
    }
  }

  // Format date
  const formatDate = (dateString: string) => {
    if (!dateString) return 'N/A'
    const date = new Date(dateString)
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  // Format time duration
  const formatDuration = (seconds: number) => {
    if (seconds < 60) return `${seconds} seconds`
    const minutes = Math.floor(seconds / 60)
    if (minutes < 60) return `${minutes} minute${minutes === 1 ? '' : 's'}`
    const hours = Math.floor(minutes / 60)
    const remainingMinutes = minutes % 60
    return `${hours} hour${hours === 1 ? '' : 's'}${remainingMinutes > 0 ? ` ${remainingMinutes} minute${remainingMinutes === 1 ? '' : 's'}` : ''}`
  }

  // Render status icon
  const renderStatusIcon = (status: string) => {
    switch(status) {
      case 'completed':
        return <CheckCircle className="h-5 w-5 text-green-500" />
      case 'processing':
        return <Clock className="h-5 w-5 text-blue-500 animate-pulse" />
      case 'error':
        return <AlertCircle className="h-5 w-5 text-red-500" />
      default:
        return <Clock className="h-5 w-5 text-muted-foreground" />
    }
  }

  // Render step status
  const renderStepStatus = (status: string) => {
    switch(status) {
      case 'completed':
        return <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">Completed</Badge>
      case 'processing':
        return <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200 animate-pulse">Processing</Badge>
      case 'error':
        return <Badge variant="outline" className="bg-red-50 text-red-700 border-red-200">Failed</Badge>
      case 'pending':
        return <Badge variant="outline">Pending</Badge>
      default:
        return <Badge variant="outline">Unknown</Badge>
    }
  }

  return (
    <div className="container max-w-7xl mx-auto py-8 px-4">
      {/* Breadcrumb */}
      <div className="mb-4">
        <Breadcrumb>
          <BreadcrumbList>
            <BreadcrumbItem>
              <BreadcrumbLink href="/document-center">Document Center</BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbLink href="/document-center/processing">Processing Status</BreadcrumbLink>
            </BreadcrumbItem>
          </BreadcrumbList>
        </Breadcrumb>
      </div>

      <div className="mb-8 flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Processing Status</h1>
          <p className="text-muted-foreground mt-1">
            Monitor document processing jobs and view processing history
          </p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" onClick={() => router.back()}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back
          </Button>
          <Button
            variant="outline"
            onClick={handleRefresh}
            disabled={refreshing}
          >
            <RefreshCcw className={`mr-2 h-4 w-4 ${refreshing ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
          <Button onClick={() => router.push('/document-center/upload')}>
            <FileUp className="mr-2 h-4 w-4" />
            Upload More
          </Button>
        </div>
      </div>

      {/* Processing Stats */}
      {stats && (
        <div className="grid grid-cols-2 md:grid-cols-5 gap-4 mb-8">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">
                Processing
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-blue-600">{stats.processingCount}</div>
              <p className="text-xs text-muted-foreground mt-1">
                Documents in queue
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">
                Completed
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">{stats.completedCount}</div>
              <p className="text-xs text-muted-foreground mt-1">
                Successfully processed
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">
                Errors
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-red-600">{stats.errorCount}</div>
              <p className="text-xs text-muted-foreground mt-1">
                Processing failures
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">
                Avg. Process Time
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{formatDuration(stats.averageProcessingTime)}</div>
              <p className="text-xs text-muted-foreground mt-1">
                Per document
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">
                Success Rate
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.successRate}%</div>
              <p className="text-xs text-muted-foreground mt-1">
                Overall completion rate
              </p>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Processing Jobs Tabs */}
      <Tabs defaultValue="active" className="mb-8">
        <TabsList>
          <TabsTrigger value="active" className="flex items-center">
            <Clock className="mr-2 h-4 w-4" />
            Active Jobs ({activeJobs.length})
          </TabsTrigger>
          <TabsTrigger value="completed" className="flex items-center">
            <CheckCircle className="mr-2 h-4 w-4" />
            Completed ({completedJobs.length})
          </TabsTrigger>
        </TabsList>

        <TabsContent value="active" className="mt-4">
          <Card>
            <CardContent className="pt-6">
              {loading ? (
                <div className="flex justify-center items-center py-12">
                  <Clock className="h-5 w-5 mr-2 animate-spin" />
                  Loading processing jobs...
                </div>
              ) : activeJobs.length > 0 ? (
                <div className="space-y-6">
                  {activeJobs.map(job => (
                    <div key={job.id} className="border rounded-lg p-4">
                      <div className="flex items-start justify-between mb-4">
                        <div className="flex items-start gap-3">
                          <div className="mt-1">
                            {renderStatusIcon(job.status)}
                          </div>
                          <div>
                            <h3 className="font-medium">{job.documentTitle}</h3>
                            <p className="text-sm text-muted-foreground">
                              Started: {formatDate(job.startedAt)}
                              {job.eta && job.status === 'processing' &&
                                ` • Est. completion: ${formatDate(job.eta)}`}
                            </p>
                          </div>
                        </div>
                        {job.status === 'error' && (
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => handleRetry(job.id)}
                          >
                            <RefreshCcw className="mr-2 h-3 w-3" />
                            Retry
                          </Button>
                        )}
                      </div>

                      {job.status === 'processing' && (
                        <div className="mb-4">
                          <div className="flex justify-between text-sm mb-1">
                            <span>Processing...</span>
                            <span>{job.progress}%</span>
                          </div>
                          <Progress value={job.progress} className="h-2" />
                        </div>
                      )}

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        {job.steps.map((step: ProcessingStep, index: number) => (
                          <div key={index} className="flex justify-between items-center border rounded p-3">
                            <div>
                              <p className="font-medium capitalize">{step.name}</p>
                              <p className="text-sm text-muted-foreground">{step.message}</p>
                            </div>
                            <div>
                              {renderStepStatus(step.status)}
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-12">
                  <BarChart3 className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                  <h3 className="text-lg font-medium mb-2">No Active Processing Jobs</h3>
                  <p className="text-muted-foreground mb-4">
                    All documents have completed processing.
                  </p>
                  <Button onClick={() => router.push('/document-center/upload')}>
                    Upload Documents
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="completed" className="mt-4">
          <Card>
            <CardContent className="pt-6">
              {loading ? (
                <div className="flex justify-center items-center py-12">
                  <Clock className="h-5 w-5 mr-2 animate-spin" />
                  Loading completed jobs...
                </div>
              ) : completedJobs.length > 0 ? (
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Document</TableHead>
                      <TableHead>Started</TableHead>
                      <TableHead>Completed</TableHead>
                      <TableHead>Duration</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {completedJobs.map(job => {
                      const startTime = new Date(job.startedAt).getTime()
                      const endTime = job.completedAt ? new Date(job.completedAt).getTime() : 0
                      const durationMs = endTime - startTime
                      const durationSec = Math.floor(durationMs / 1000)

                      return (
                        <TableRow key={job.id}>
                          <TableCell>
                            <div className="flex items-center">
                              <FileText className="h-4 w-4 mr-2 text-muted-foreground" />
                              <span>{job.documentTitle}</span>
                            </div>
                          </TableCell>
                          <TableCell>{formatDate(job.startedAt)}</TableCell>
                          <TableCell>{job.completedAt ? formatDate(job.completedAt) : 'N/A'}</TableCell>
                          <TableCell>{formatDuration(durationSec)}</TableCell>
                          <TableCell>
                            <div className="flex items-center">
                              <CheckCircle className="h-4 w-4 mr-1 text-green-500" />
                              <span>Completed</span>
                            </div>
                          </TableCell>
                          <TableCell>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => router.push(`/document-center/library/${job.documentId}`)}
                            >
                              View Document
                            </Button>
                          </TableCell>
                        </TableRow>
                      )
                    })}
                  </TableBody>
                </Table>
              ) : (
                <div className="text-center py-12">
                  <CheckCircle className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                  <h3 className="text-lg font-medium mb-2">No Completed Jobs</h3>
                  <p className="text-muted-foreground mb-4">
                    Your completed processing jobs will appear here.
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Processing Information */}
      <Card>
        <CardHeader>
          <CardTitle>About Document Processing</CardTitle>
          <CardDescription>
            How our document processing system works
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
              <div>
                <h3 className="font-medium mb-2">Text Extraction</h3>
                <p className="text-sm text-muted-foreground">
                  Documents go through advanced OCR to extract all text content,
                  ensuring all information is available for search and analysis.
                </p>
              </div>

              <div>
                <h3 className="font-medium mb-2">Document Analysis</h3>
                <p className="text-sm text-muted-foreground">
                  AI analyzes the content to identify key information like dates,
                  parties involved, case details, and other relevant data points.
                </p>
              </div>

              <div>
                <h3 className="font-medium mb-2">Classification</h3>
                <p className="text-sm text-muted-foreground">
                  Documents are automatically categorized based on content, making
                  them easier to find and organize within your case files.
                </p>
              </div>

              <div>
                <h3 className="font-medium mb-2">Indexing</h3>
                <p className="text-sm text-muted-foreground">
                  The final step adds the document to the searchable database,
                  allowing you to quickly find documents by their content.
                </p>
              </div>
            </div>
          </div>
        </CardContent>
        <CardFooter className="border-t pt-6">
          <div className="text-sm text-muted-foreground">
            <p>
              Document processing typically takes 1-3 minutes depending on document size and complexity.
              For files with poor scan quality, some manual verification may be required.
            </p>
          </div>
        </CardFooter>
      </Card>
    </div>
  )
}
