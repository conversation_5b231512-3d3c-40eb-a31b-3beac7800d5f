import { useEffect, useState, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import ProactiveMessage from '@/components/proactive/ProactiveMessage';
import { useSupabase } from '@/lib/supabase/provider';
import { Activity, Insight } from '@/lib/types';
import { useToast } from '@/components/ui/use-toast';
import { useAuthenticatedFetch } from '@/hooks/useAuthenticatedFetch';

/**
 * ProactiveContainer
 *
 * Fetches recent user activities and displays proactive messages/insights.
 *
 * Manages proactive messaging in the dashboard
 * Handles user authentication state and conditional rendering
 */
export default function ProactiveContainer() {
  const [user, setUser] = useState<any>(null);
  const [activities, setActivities] = useState<Activity[]>([]); // Keep for backward compatibility
  const [insights, setInsights] = useState<Insight[]>([]); // New state for insights
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [useAi, setUseAi] = useState<boolean>(true); // Toggle for AI-powered insights
  const { supabase, getCurrentUser } = useSupabase();
  const { authedFetch, isReady } = useAuthenticatedFetch();
  const router = useRouter();
  const { toast } = useToast();

  // Define the expected structure of the API response
  interface ActivityInsightsApiResponse {
    insights?: Insight[];
    activities?: Activity[];
    meta?: {
      aiGenerated?: boolean;
    };
  }

  useEffect(() => {
    // Fetch current user information
    const fetchUser = async () => {
      setIsLoading(true); // Start loading when fetching user
      try {
        const userData = await getCurrentUser();
        if (userData) {
          setUser(userData);
        }
      } catch (_error) {
        console.error('Error fetching user:', error);
        setError('Failed to load user data.');
      } finally {
        // setIsLoading(false); // Loading continues until activities are fetched
      }
    };

    fetchUser();
    // Clear previous state on component mount/user change
    setActivities([]);
    setInsights([]);
    setError(null);
  }, [getCurrentUser]);

  // Effect to fetch activities once user is loaded
  const fetchActivities = useCallback(async () => {
    if (!user) {
      return; // Don't fetch if user is not loaded
    }
    if (!authedFetch || !isReady) {
      console.warn('ProactiveContainer: authedFetch not ready for fetchActivities.');
      // Optionally set error state
      setIsLoading(false);
      return;
    }

    setIsLoading(true);
    setError(null);
    try {
      // Fetch data from the enhanced API endpoint with AI toggle
      const data = await authedFetch<ActivityInsightsApiResponse>(`/api/activity/insights?daysBack=7&limit=15&useAi=${useAi}`);

      // Handle the new API response format with metadata
      if (data?.insights) {
        setInsights(data.insights || []);
        console.log('[ProactiveContainer] Fetched Insights:', data.insights);

        // Log if insights were AI-generated
        if (data.meta?.aiGenerated) {
          console.log('[ProactiveContainer] Insights were AI-generated');
        }
      } else if (data?.activities) {
        // Backward compatibility
        setActivities(data.activities || []);
        console.log('[ProactiveContainer] Fetched Activities:', data.activities);
      }

    } catch (err) {
      console.error('Error fetching insights:', err);
      setError(err instanceof Error ? err.message : 'Failed to load activity insights.');
      setActivities([]); // Clear activities on error
      setInsights([]); // Clear insights on error
    } finally {
      setIsLoading(false);
    }
  }, [user, useAi, authedFetch, isReady]); // Add dependencies

  useEffect(() => {
    fetchActivities();
  }, [fetchActivities]); // Depend on the callback

  /**
   * Handle user feedback on insights
   */
  const handleFeedbackSubmit = useCallback(async (feedbackData: {
    feedbackId: string;
    insightId: string;
    action: string;
    rating?: number;
    comment?: string;
  }) => {
    if (!authedFetch || !isReady) {
        console.warn('ProactiveContainer: authedFetch not ready for handleFeedbackSubmit.');
        toast({ title: 'Error', description: 'Could not submit feedback. Service not ready.', variant: 'destructive' });
        return;
      }
    try {
      // Submit feedback to the API
      await authedFetch('/api/activity/insights', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(feedbackData),
      });

      // Show success toast
      toast({
        title: 'Feedback Received',
        description: 'Thank you for your feedback on this insight.',
        variant: 'default',
      });

      // If the user rated the insight poorly, offer to disable AI
      if (feedbackData.action === 'rated' && feedbackData.rating && feedbackData.rating < 3) {
        // Show toast with option to disable AI
        toast({
          title: 'Would you prefer standard insights?',
          description: 'You can switch to rule-based insights instead of AI-generated ones.',
          variant: 'destructive',
          action: (
            <button
              className="bg-white text-black px-3 py-1 rounded-md text-xs font-medium"
              onClick={() => setUseAi(false)}
            >
              Switch to Standard
            </button>
          ),
        });
      }
    } catch (_error) {
      console.error('Error submitting feedback:', error);
      toast({
        title: 'Feedback Error',
        description: 'Unable to submit your feedback. Please try again.',
        variant: 'destructive',
      });
    }
  }, [authedFetch, isReady, toast, setUseAi]); // Add dependencies

  // Don't render anything if user isn't loaded yet
  if (!user) {
    // Optionally show a placeholder or compact loading state
    return <div className="text-xs text-gray-500">Loading user...</div>;
  }

  /**
   * Toggle between AI and rule-based insights
   */
  const toggleAiInsights = () => {
    setUseAi(!useAi);
    toast({
      title: `${!useAi ? 'AI' : 'Standard'} Insights Enabled`,
      description: `Switched to ${!useAi ? 'AI-powered' : 'rule-based'} insights generation.`,
    });
  };

  // --- Display Logic ---
  // TODO: Enhance this section to render insights based on fetched activities
  // For now, shows loading/error states and a basic placeholder if data is loaded.

  if (isLoading && !user) {
    // Handles the initial loading state before user is fetched
    return <div className="text-sm text-gray-600">Loading insights...</div>;
  }

  if (isLoading && user) {
    // Handles the loading state when user is known but activities are fetching
    return <div className="text-sm text-gray-600">Fetching recent activities...</div>;
  }

  if (error) {
    return <div className="text-sm text-red-600">Error loading insights: {error}</div>;
  }

  // Render ProactiveMessage with the fetched insights and activities
  return (
    <div className="mt-4 mb-6">
      <div className="flex justify-end mb-2">
        <button
          onClick={toggleAiInsights}
          className={`text-xs px-3 py-1 rounded-full ${useAi
            ? 'bg-primary/10 text-primary hover:bg-primary/20'
            : 'bg-secondary text-secondary-foreground hover:bg-secondary/80'}`}
        >
          {useAi ? 'Using AI Insights' : 'Using Standard Insights'}
        </button>
      </div>

      <ProactiveMessage
        userId={user.id}
        userName={user.email?.split('@')[0]}
        activities={activities} // Pass raw activities
        insights={insights} // Pass processed insights
        isLoading={isLoading} // Pass loading state
        onFeedbackSubmit={handleFeedbackSubmit} // Pass feedback handler
      />
    </div>
  );
}
