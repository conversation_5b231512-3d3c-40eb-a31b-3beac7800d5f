'use client';

import { useEffect, useState } from 'react';
import { useSupabase } from '@/lib/supabase/provider';
import { useUser } from '@/contexts/UserContext';
import { SubscriptionService, SubscriptionPlanDTO, TenantSubscriptionDTO } from '@/lib/services/subscription-service';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Loader2, Check, AlertCircle } from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { useRouter } from 'next/navigation';

export default function PlansPage() {
  const router = useRouter();
  const { supabase } = useSupabase();
  const { user, tenantId } = useUser();
  const [plans, setPlans] = useState<SubscriptionPlanDTO[]>([]);
  const [subscription, setSubscription] = useState<TenantSubscriptionDTO | null>(null);
  const [loading, setLoading] = useState(true);
  const [selectedPlan, setSelectedPlan] = useState<SubscriptionPlanDTO | null>(null);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [changing, setChanging] = useState(false);
  const { toast } = useToast();

  useEffect(() => {
    if (!tenantId) return;

    const subscriptionService = new SubscriptionService(supabase);

    async function fetchData(currentTenantId: string) {
      try {
        const [plansData, subscriptionData] = await Promise.all([
          subscriptionService.getPlans(),
          subscriptionService.getTenantSubscription(currentTenantId)
        ]);

        setPlans(plansData);
        setSubscription(subscriptionData);
      } catch (_error) {
        console.error('Error fetching data:', error);
        toast({
          title: 'Error',
          description: 'Failed to load plans information',
          variant: 'destructive',
        });
      } finally {
        setLoading(false);
      }
    }

    fetchData(tenantId);
  }, [tenantId, toast, supabase]);

  const handleChangePlan = async () => {
    if (!selectedPlan || !tenantId) return;

    setChanging(true);

    try {
      const subscriptionService = new SubscriptionService(supabase);

      if (!subscription) {
        // Create a new trial subscription
        await subscriptionService.createTrialSubscription(
          tenantId,
          selectedPlan.id
        );

        toast({
          title: 'Trial Started',
          description: `Your 14-day trial of ${selectedPlan.name} has started.`,
        });
      } else {
        // Change existing subscription
        await subscriptionService.changePlan(
          subscription.id,
          selectedPlan.id
        );

        toast({
          title: 'Plan Changed',
          description: `Your subscription has been changed to ${selectedPlan.name}.`,
        });
      }

      // Redirect to subscription page
      router.push('/settings/subscription');
    } catch (_error) {
      console.error('Error changing plan:', error);
      toast({
        title: 'Error',
        description: 'Failed to change subscription plan',
        variant: 'destructive',
      });
    } finally {
      setChanging(false);
      setDialogOpen(false);
    }
  };

  const isCurrentPlan = (planId: string) => {
    return subscription?.planId === planId;
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Subscription Plans</CardTitle>
          <CardDescription>
            Choose the plan that best fits your needs
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="monthly">
            <TabsList className="mb-6">
              <TabsTrigger value="monthly">Monthly Billing</TabsTrigger>
              <TabsTrigger value="yearly">Yearly Billing (Save 15%)</TabsTrigger>
            </TabsList>

            <TabsContent value="monthly" className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                {plans.map(plan => (
                  <Card key={plan.id} className={`overflow-hidden ${isCurrentPlan(plan.id) ? 'border-primary' : ''}`}>
                    <CardHeader className="pb-2">
                      <div className="flex justify-between items-start">
                        <div>
                          <CardTitle>{plan.name}</CardTitle>
                          <CardDescription>{plan.description}</CardDescription>
                        </div>
                        {isCurrentPlan(plan.id) && (
                          <Badge className="bg-primary">Current Plan</Badge>
                        )}
                      </div>
                    </CardHeader>
                    <CardContent className="pb-2">
                      <div className="mb-4">
                        <span className="text-3xl font-bold">${plan.basePriceMonthly}</span>
                        <span className="text-muted-foreground">/month</span>
                      </div>

                      <div className="space-y-2">
                        <h4 className="font-medium">Features:</h4>
                        <ul className="space-y-1 text-sm">
                          {plan.features && typeof plan.features === 'object' && plan.features !== null && 'practiceAreas' in plan.features && Array.isArray(plan.features.practiceAreas) && (
                            <li className="flex items-start">
                              <Check className="h-4 w-4 text-green-500 mr-2 mt-0.5" />
                              <span>
                                <span className="font-medium">{plan.features.practiceAreas.length}</span> Practice {plan.features.practiceAreas.length === 1 ? 'Area' : 'Areas'}: {plan.features.practiceAreas.join(', ')}
                              </span>
                            </li>
                          )}

                          {plan.features && typeof plan.features === 'object' && plan.features !== null && 'states' in plan.features && Array.isArray(plan.features.states) && (
                            <li className="flex items-start">
                              <Check className="h-4 w-4 text-green-500 mr-2 mt-0.5" />
                              <span>
                                <span className="font-medium">{plan.features.states.length}</span> {plan.features.states.length === 1 ? 'State' : 'States'}: {plan.features.states.join(', ')}
                              </span>
                            </li>
                          )}

                          {plan.features && typeof plan.features === 'object' && plan.features !== null && 'maxUsers' in plan.features && typeof plan.features.maxUsers === 'number' && (
                            <li className="flex items-center">
                              <Check className="h-4 w-4 text-green-500 mr-2" />
                              <span>
                                <span className="font-medium">{plan.features.maxUsers}</span> Users
                              </span>
                            </li>
                          )}

                          {plan.features && typeof plan.features === 'object' && plan.features !== null && 'maxCases' in plan.features && typeof plan.features.maxCases === 'number' && (
                            <li className="flex items-center">
                              <Check className="h-4 w-4 text-green-500 mr-2" />
                              <span>
                                <span className="font-medium">{plan.features.maxCases}</span> Cases
                              </span>
                            </li>
                          )}

                          {plan.features && typeof plan.features === 'object' && plan.features !== null && 'maxStorage' in plan.features && typeof plan.features.maxStorage === 'number' && (
                            <li className="flex items-center">
                              <Check className="h-4 w-4 text-green-500 mr-2" />
                              <span>
                                <span className="font-medium">{plan.features.maxStorage}GB</span> Storage
                              </span>
                            </li>
                          )}

                          {plan.features && typeof plan.features === 'object' && plan.features !== null && 'hasIntakeAgent' in plan.features && plan.features.hasIntakeAgent === true && (
                            <li className="flex items-center">
                              <Check className="h-4 w-4 text-green-500 mr-2" />
                              <span>Intake Agent</span>
                            </li>
                          )}

                          {plan.features && typeof plan.features === 'object' && plan.features !== null && 'hasResearchAgent' in plan.features && plan.features.hasResearchAgent === true && (
                            <li className="flex items-center">
                              <Check className="h-4 w-4 text-green-500 mr-2" />
                              <span>Research Agent</span>
                            </li>
                          )}

                          {plan.features && typeof plan.features === 'object' && plan.features !== null && 'hasDocumentAgent' in plan.features && plan.features.hasDocumentAgent === true && (
                            <li className="flex items-center">
                              <Check className="h-4 w-4 text-green-500 mr-2" />
                              <span>Document Agent</span>
                            </li>
                          )}

                          {plan.features && typeof plan.features === 'object' && plan.features !== null && 'hasCalendarAgent' in plan.features && plan.features.hasCalendarAgent === true && (
                            <li className="flex items-center">
                              <Check className="h-4 w-4 text-green-500 mr-2" />
                              <span>Calendar Agent</span>
                            </li>
                          )}
                        </ul>
                      </div>
                    </CardContent>
                    <CardFooter>
                      <Dialog open={dialogOpen && selectedPlan?.id === plan.id} onOpenChange={(open) => {
                        setDialogOpen(open);
                        if (!open) setSelectedPlan(null);
                      }}>
                        <DialogTrigger asChild>
                          <Button
                            className="w-full"
                            variant={isCurrentPlan(plan.id) ? "outline" : "default"}
                            disabled={isCurrentPlan(plan.id)}
                            onClick={() => setSelectedPlan(plan)}
                          >
                            {isCurrentPlan(plan.id) ? 'Current Plan' : 'Select Plan'}
                          </Button>
                        </DialogTrigger>
                        <DialogContent>
                          <DialogHeader>
                            <DialogTitle>Change to {plan.name} Plan</DialogTitle>
                            <DialogDescription>
                              {subscription
                                ? 'Your subscription will be updated immediately.'
                                : 'You will start a 14-day free trial.'}
                            </DialogDescription>
                          </DialogHeader>
                          <div className="py-4">
                            <h4 className="font-medium mb-2">Plan includes:</h4>
                            <ul className="space-y-1 text-sm">
                              {plan.features && typeof plan.features === 'object' && plan.features !== null && 'practiceAreas' in plan.features && Array.isArray(plan.features.practiceAreas) && (
                                <li className="flex items-start">
                                  <Check className="h-4 w-4 text-green-500 mr-2 mt-0.5" />
                                  <span>
                                    <span className="font-medium">{plan.features.practiceAreas.length}</span> Practice {plan.features.practiceAreas.length === 1 ? 'Area' : 'Areas'}: {plan.features.practiceAreas.join(', ')}
                                  </span>
                                </li>
                              )}

                              {plan.features && typeof plan.features === 'object' && plan.features !== null && 'states' in plan.features && Array.isArray(plan.features.states) && (
                                <li className="flex items-start">
                                  <Check className="h-4 w-4 text-green-500 mr-2 mt-0.5" />
                                  <span>
                                    <span className="font-medium">{plan.features.states.length}</span> {plan.features.states.length === 1 ? 'State' : 'States'}: {plan.features.states.join(', ')}
                                  </span>
                                </li>
                              )}
                            </ul>
                          </div>
                          <div className="bg-muted p-3 rounded-md flex items-start space-x-3">
                            <AlertCircle className="h-5 w-5 text-blue-500 mt-0.5" />
                            <div className="text-sm">
                              <p>You will be charged ${plan.basePriceMonthly} per month.</p>
                              {!subscription && (
                                <p className="mt-1">Your first payment will be after the 14-day trial period.</p>
                              )}
                            </div>
                          </div>
                          <DialogFooter>
                            <Button variant="outline" onClick={() => setDialogOpen(false)}>Cancel</Button>
                            <Button onClick={handleChangePlan} disabled={changing}>
                              {changing && <Loader2 className="h-4 w-4 mr-2 animate-spin" />}
                              {subscription ? 'Change Plan' : 'Start Trial'}
                            </Button>
                          </DialogFooter>
                        </DialogContent>
                      </Dialog>
                    </CardFooter>
                  </Card>
                ))}
              </div>
            </TabsContent>

            <TabsContent value="yearly" className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                {plans.map(plan => (
                  <Card key={plan.id} className={`overflow-hidden ${isCurrentPlan(plan.id) ? 'border-primary' : ''}`}>
                    <CardHeader className="pb-2">
                      <div className="flex justify-between items-start">
                        <div>
                          <CardTitle>{plan.name}</CardTitle>
                          <CardDescription>{plan.description}</CardDescription>
                        </div>
                        {isCurrentPlan(plan.id) && (
                          <Badge className="bg-primary">Current Plan</Badge>
                        )}
                      </div>
                    </CardHeader>
                    <CardContent className="pb-2">
                      <div className="mb-4">
                        <span className="text-3xl font-bold">${plan.basePriceYearly / 12}</span>
                        <span className="text-muted-foreground">/month</span>
                        <div className="text-sm text-muted-foreground">
                          billed annually (${plan.basePriceYearly}/year)
                        </div>
                      </div>

                      {/* Same features as monthly tab */}
                      <div className="space-y-2">
                        <h4 className="font-medium">Features:</h4>
                        <ul className="space-y-1 text-sm">
                          {plan.features && typeof plan.features === 'object' && plan.features !== null && 'practiceAreas' in plan.features && Array.isArray(plan.features.practiceAreas) && (
                            <li className="flex items-start">
                              <Check className="h-4 w-4 text-green-500 mr-2 mt-0.5" />
                              <span>
                                <span className="font-medium">{plan.features.practiceAreas.length}</span> Practice {plan.features.practiceAreas.length === 1 ? 'Area' : 'Areas'}: {plan.features.practiceAreas.join(', ')}
                              </span>
                            </li>
                          )}

                          {plan.features && typeof plan.features === 'object' && plan.features !== null && 'states' in plan.features && Array.isArray(plan.features.states) && (
                            <li className="flex items-start">
                              <Check className="h-4 w-4 text-green-500 mr-2 mt-0.5" />
                              <span>
                                <span className="font-medium">{plan.features.states.length}</span> {plan.features.states.length === 1 ? 'State' : 'States'}: {plan.features.states.join(', ')}
                              </span>
                            </li>
                          )}

                          {plan.features && typeof plan.features === 'object' && plan.features !== null && 'maxUsers' in plan.features && typeof plan.features.maxUsers === 'number' && (
                            <li className="flex items-center">
                              <Check className="h-4 w-4 text-green-500 mr-2" />
                              <span>
                                <span className="font-medium">{plan.features.maxUsers}</span> Users
                              </span>
                            </li>
                          )}

                          {plan.features && typeof plan.features === 'object' && plan.features !== null && 'maxCases' in plan.features && typeof plan.features.maxCases === 'number' && (
                            <li className="flex items-center">
                              <Check className="h-4 w-4 text-green-500 mr-2" />
                              <span>
                                <span className="font-medium">{plan.features.maxCases}</span> Cases
                              </span>
                            </li>
                          )}

                          {plan.features && typeof plan.features === 'object' && plan.features !== null && 'maxStorage' in plan.features && typeof plan.features.maxStorage === 'number' && (
                            <li className="flex items-center">
                              <Check className="h-4 w-4 text-green-500 mr-2" />
                              <span>
                                <span className="font-medium">{plan.features.maxStorage}GB</span> Storage
                              </span>
                            </li>
                          )}

                          {plan.features && typeof plan.features === 'object' && plan.features !== null && 'hasIntakeAgent' in plan.features && plan.features.hasIntakeAgent === true && (
                            <li className="flex items-center">
                              <Check className="h-4 w-4 text-green-500 mr-2" />
                              <span>Intake Agent</span>
                            </li>
                          )}

                          {plan.features && typeof plan.features === 'object' && plan.features !== null && 'hasResearchAgent' in plan.features && plan.features.hasResearchAgent === true && (
                            <li className="flex items-center">
                              <Check className="h-4 w-4 text-green-500 mr-2" />
                              <span>Research Agent</span>
                            </li>
                          )}

                          {plan.features && typeof plan.features === 'object' && plan.features !== null && 'hasDocumentAgent' in plan.features && plan.features.hasDocumentAgent === true && (
                            <li className="flex items-center">
                              <Check className="h-4 w-4 text-green-500 mr-2" />
                              <span>Document Agent</span>
                            </li>
                          )}

                          {plan.features && typeof plan.features === 'object' && plan.features !== null && 'hasCalendarAgent' in plan.features && plan.features.hasCalendarAgent === true && (
                            <li className="flex items-center">
                              <Check className="h-4 w-4 text-green-500 mr-2" />
                              <span>Calendar Agent</span>
                            </li>
                          )}
                        </ul>
                      </div>
                    </CardContent>
                    <CardFooter>
                      <Dialog open={dialogOpen && selectedPlan?.id === plan.id} onOpenChange={(open) => {
                        setDialogOpen(open);
                        if (!open) setSelectedPlan(null);
                      }}>
                        <DialogTrigger asChild>
                          <Button
                            className="w-full"
                            variant={isCurrentPlan(plan.id) ? "outline" : "default"}
                            disabled={isCurrentPlan(plan.id)}
                            onClick={() => setSelectedPlan(plan)}
                          >
                            {isCurrentPlan(plan.id) ? 'Current Plan' : 'Select Plan'}
                          </Button>
                        </DialogTrigger>
                        <DialogContent>
                          <DialogHeader>
                            <DialogTitle>Change to {plan.name} Plan</DialogTitle>
                            <DialogDescription>
                              {subscription
                                ? 'Your subscription will be updated immediately.'
                                : 'You will start a 14-day free trial.'}
                            </DialogDescription>
                          </DialogHeader>
                          <div className="py-4">
                            <h4 className="font-medium mb-2">Plan includes:</h4>
                            <ul className="space-y-1 text-sm">
                              {plan.features && typeof plan.features === 'object' && plan.features !== null && 'practiceAreas' in plan.features && Array.isArray(plan.features.practiceAreas) && (
                                <li className="flex items-start">
                                  <Check className="h-4 w-4 text-green-500 mr-2 mt-0.5" />
                                  <span>
                                    <span className="font-medium">{plan.features.practiceAreas.length}</span> Practice {plan.features.practiceAreas.length === 1 ? 'Area' : 'Areas'}: {plan.features.practiceAreas.join(', ')}
                                  </span>
                                </li>
                              )}

                              {plan.features && typeof plan.features === 'object' && plan.features !== null && 'states' in plan.features && Array.isArray(plan.features.states) && (
                                <li className="flex items-start">
                                  <Check className="h-4 w-4 text-green-500 mr-2 mt-0.5" />
                                  <span>
                                    <span className="font-medium">{plan.features.states.length}</span> {plan.features.states.length === 1 ? 'State' : 'States'}: {plan.features.states.join(', ')}
                                  </span>
                                </li>
                              )}
                            </ul>
                          </div>
                          <div className="bg-muted p-3 rounded-md flex items-start space-x-3">
                            <AlertCircle className="h-5 w-5 text-blue-500 mt-0.5" />
                            <div className="text-sm">
                              <p>You will be charged ${plan.basePriceYearly} per year.</p>
                              {!subscription && (
                                <p className="mt-1">Your first payment will be after the 14-day trial period.</p>
                              )}
                            </div>
                          </div>
                          <DialogFooter>
                            <Button variant="outline" onClick={() => setDialogOpen(false)}>Cancel</Button>
                            <Button onClick={handleChangePlan} disabled={changing}>
                              {changing && <Loader2 className="h-4 w-4 mr-2 animate-spin" />}
                              {subscription ? 'Change Plan' : 'Start Trial'}
                            </Button>
                          </DialogFooter>
                        </DialogContent>
                      </Dialog>
                    </CardFooter>
                  </Card>
                ))}
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
}
