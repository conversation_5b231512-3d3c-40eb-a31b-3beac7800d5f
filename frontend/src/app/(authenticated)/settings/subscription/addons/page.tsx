'use client';

import { useEffect, useState, useMemo } from 'react';
import { useSupabase } from '@/lib/supabase/provider';
import { useUser } from '@/contexts/UserContext';
import { SubscriptionService, SubscriptionAddonDTO, TenantSubscriptionDTO } from '@/lib/services/subscription-service';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Loader2, Plus, Check, Info } from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";

export default function AddonsPage() {
  const { supabase } = useSupabase();
  const { user, tenantId } = useUser();
  const subscriptionService = useMemo(() => new SubscriptionService(supabase), [supabase]);
  const [addons, setAddons] = useState<SubscriptionAddonDTO[]>([]);
  const [subscription, setSubscription] = useState<TenantSubscriptionDTO | null>(null);
  const [loading, setLoading] = useState(true);
  const [selectedAddon, setSelectedAddon] = useState<SubscriptionAddonDTO | null>(null);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [adding, setAdding] = useState(false);
  const { toast } = useToast();

  useEffect(() => {
    if (!tenantId) return; // tenantId is guaranteed string here

    async function fetchData(currentTenantId: string) {
      try {
        const [addonsData, subscriptionData] = await Promise.all([
          subscriptionService.getAddons(),
          subscriptionService.getTenantSubscription(currentTenantId)
        ]);

        setAddons(addonsData);
        setSubscription(subscriptionData);
      } catch (_error) {
        console.error('Error fetching data:', error);
        toast({
          title: 'Error',
          description: 'Failed to load add-ons information',
          variant: 'destructive',
        });
      } finally {
        setLoading(false);
      }
    }

    fetchData(tenantId); // Call with the non-null tenantId
  }, [tenantId, toast, subscriptionService]);

  const handleAddAddon = async () => {
    if (!selectedAddon || !subscription || !tenantId) return;

    setAdding(true);

    try {
      await subscriptionService.addAddon({
        subscriptionId: subscription.id,
        tenantId: tenantId,
        addonId: selectedAddon.id
      });

      toast({
        title: 'Add-on Added',
        description: `${selectedAddon.name} has been added to your subscription.`,
      });

      // Refresh subscription data
      const updatedSubscription = await subscriptionService.getTenantSubscription(tenantId);
      setSubscription(updatedSubscription);

      setDialogOpen(false);
    } catch (_error) {
      console.error('Error adding add-on:', error);
      toast({
        title: 'Error',
        description: 'Failed to add the selected add-on',
        variant: 'destructive',
      });
    } finally {
      setAdding(false);
    }
  };

  const isAddonActive = (addonId: string) => {
    return subscription?.addons?.some(
      addon => addon.addonId === addonId && addon.status === 'active'
    );
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }

  if (!subscription) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>No Active Subscription</CardTitle>
          <CardDescription>
            You need an active subscription to manage add-ons.
          </CardDescription>
        </CardHeader>
      </Card>
    );
  }

  // Group add-ons by category
  const addonsByCategory = addons.reduce((acc, addon) => {
    if (!acc[addon.category]) {
      acc[addon.category] = [];
    }
    acc[addon.category].push(addon);
    return acc;
  }, {} as Record<string, SubscriptionAddonDTO[]>);

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Subscription Add-ons</CardTitle>
          <CardDescription>
            Enhance your subscription with additional features
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="all">
            <TabsList className="mb-4">
              <TabsTrigger value="all">All Add-ons</TabsTrigger>
              {Object.keys(addonsByCategory).map(category => (
                <TabsTrigger key={category} value={category}>
                  {category.charAt(0).toUpperCase() + category.slice(1)}
                </TabsTrigger>
              ))}
            </TabsList>

            <TabsContent value="all" className="space-y-4">
              {addons.length === 0 ? (
                <p className="text-center text-muted-foreground py-4">No add-ons available</p>
              ) : (
                addons.map(addon => (
                  <Card key={addon.id} className="overflow-hidden">
                    <CardHeader className="pb-2">
                      <div className="flex justify-between items-start">
                        <div>
                          <CardTitle className="text-base">{addon.name}</CardTitle>
                          <CardDescription>{addon.description}</CardDescription>
                        </div>
                        <Badge>{addon.category}</Badge>
                      </div>
                    </CardHeader>
                    <CardContent className="pb-2">
                      <div className="flex justify-between items-center">
                        <div className="text-sm">
                          <span className="font-medium">${subscription.billingCycle === 'monthly' ? addon.priceMonthly : addon.priceYearly}</span>
                          <span className="text-muted-foreground">/{subscription.billingCycle === 'monthly' ? 'month' : 'year'}</span>
                        </div>
                        {isAddonActive(addon.id) ? (
                          <Badge className="bg-green-500">
                            <Check className="h-3 w-3 mr-1" /> Active
                          </Badge>
                        ) : (
                          <Dialog open={dialogOpen && selectedAddon?.id === addon.id} onOpenChange={(open) => {
                            setDialogOpen(open);
                            if (!open) setSelectedAddon(null);
                          }}>
                            <DialogTrigger asChild>
                              <Button
                                size="sm"
                                onClick={() => setSelectedAddon(addon)}
                              >
                                <Plus className="h-4 w-4 mr-1" /> Add
                              </Button>
                            </DialogTrigger>
                            <DialogContent>
                              <DialogHeader>
                                <DialogTitle>Add {addon.name}</DialogTitle>
                                <DialogDescription>
                                  This add-on will be added to your current subscription.
                                </DialogDescription>
                              </DialogHeader>
                              <div className="py-4">
                                <h4 className="font-medium mb-2">Features included:</h4>
                                <ul className="space-y-1 text-sm">
                                  {addon.features && Object.entries(addon.features).map(([key, value]) => (
                                    <li key={key} className="flex items-center">
                                      <Check className="h-4 w-4 text-green-500 mr-2" />
                                      <span>
                                        {key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}: <span className="font-medium">{String(value)}</span>
                                      </span>
                                    </li>
                                  ))}
                                </ul>
                              </div>
                              <div className="bg-muted p-3 rounded-md flex items-start space-x-3">
                                <Info className="h-5 w-5 text-blue-500 mt-0.5" />
                                <div className="text-sm">
                                  <p>You will be charged ${subscription.billingCycle === 'monthly' ? addon.priceMonthly : addon.priceYearly} per {subscription.billingCycle === 'monthly' ? 'month' : 'year'}.</p>
                                  <p className="mt-1">This will be added to your next billing cycle.</p>
                                </div>
                              </div>
                              <DialogFooter>
                                <Button variant="outline" onClick={() => setDialogOpen(false)}>Cancel</Button>
                                <Button onClick={handleAddAddon} disabled={adding}>
                                  {adding && <Loader2 className="h-4 w-4 mr-2 animate-spin" />}
                                  Confirm
                                </Button>
                              </DialogFooter>
                            </DialogContent>
                          </Dialog>
                        )}
                      </div>
                    </CardContent>
                  </Card>
                ))
              )}
            </TabsContent>

            {Object.entries(addonsByCategory).map(([category, categoryAddons]) => (
              <TabsContent key={category} value={category} className="space-y-4">
                {categoryAddons.map(addon => (
                  <Card key={addon.id} className="overflow-hidden">
                    <CardHeader className="pb-2">
                      <div className="flex justify-between items-start">
                        <div>
                          <CardTitle className="text-base">{addon.name}</CardTitle>
                          <CardDescription>{addon.description}</CardDescription>
                        </div>
                      </div>
                    </CardHeader>
                    <CardContent className="pb-2">
                      <div className="flex justify-between items-center">
                        <div className="text-sm">
                          <span className="font-medium">${subscription.billingCycle === 'monthly' ? addon.priceMonthly : addon.priceYearly}</span>
                          <span className="text-muted-foreground">/{subscription.billingCycle === 'monthly' ? 'month' : 'year'}</span>
                        </div>
                        {isAddonActive(addon.id) ? (
                          <Badge className="bg-green-500">
                            <Check className="h-3 w-3 mr-1" /> Active
                          </Badge>
                        ) : (
                          <Dialog open={dialogOpen && selectedAddon?.id === addon.id} onOpenChange={(open) => {
                            setDialogOpen(open);
                            if (!open) setSelectedAddon(null);
                          }}>
                            <DialogTrigger asChild>
                              <Button
                                size="sm"
                                onClick={() => setSelectedAddon(addon)}
                              >
                                <Plus className="h-4 w-4 mr-1" /> Add
                              </Button>
                            </DialogTrigger>
                            <DialogContent>
                              <DialogHeader>
                                <DialogTitle>Add {addon.name}</DialogTitle>
                                <DialogDescription>
                                  This add-on will be added to your current subscription.
                                </DialogDescription>
                              </DialogHeader>
                              <div className="py-4">
                                <h4 className="font-medium mb-2">Features included:</h4>
                                <ul className="space-y-1 text-sm">
                                  {addon.features && Object.entries(addon.features).map(([key, value]) => (
                                    <li key={key} className="flex items-center">
                                      <Check className="h-4 w-4 text-green-500 mr-2" />
                                      <span>
                                        {key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}: <span className="font-medium">{String(value)}</span>
                                      </span>
                                    </li>
                                  ))}
                                </ul>
                              </div>
                              <div className="bg-muted p-3 rounded-md flex items-start space-x-3">
                                <Info className="h-5 w-5 text-blue-500 mt-0.5" />
                                <div className="text-sm">
                                  <p>You will be charged ${subscription.billingCycle === 'monthly' ? addon.priceMonthly : addon.priceYearly} per {subscription.billingCycle === 'monthly' ? 'month' : 'year'}.</p>
                                  <p className="mt-1">This will be added to your next billing cycle.</p>
                                </div>
                              </div>
                              <DialogFooter>
                                <Button variant="outline" onClick={() => setDialogOpen(false)}>Cancel</Button>
                                <Button onClick={handleAddAddon} disabled={adding}>
                                  {adding && <Loader2 className="h-4 w-4 mr-2 animate-spin" />}
                                  Confirm
                                </Button>
                              </DialogFooter>
                            </DialogContent>
                          </Dialog>
                        )}
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </TabsContent>
            ))}
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
}
