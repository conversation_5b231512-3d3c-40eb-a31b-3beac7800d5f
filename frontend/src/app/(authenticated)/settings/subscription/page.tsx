'use client';

import { useEffect, useState } from 'react';
import { useSupabase } from '@/lib/supabase/provider';
import { useUser } from '@/contexts/UserContext';
import { SubscriptionService, TenantSubscriptionDTO } from '@/lib/services/subscription-service';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Loader2, CheckCircle, AlertCircle, Calendar } from 'lucide-react';
import { format } from 'date-fns';
import Link from 'next/link';
import { useToast } from '@/components/ui/use-toast';

export default function SubscriptionPage() {
  const { supabase } = useSupabase();
  const { user, tenantId } = useUser();
  const [subscription, setSubscription] = useState<TenantSubscriptionDTO | null>(null);
  const [loading, setLoading] = useState(true);
  const { toast } = useToast();

  useEffect(() => {
    if (!tenantId) return;

    const subscriptionService = new SubscriptionService(supabase);

    async function fetchSubscription(currentTenantId: string) {
      try {
        const data = await subscriptionService.getTenantSubscription(currentTenantId);
        setSubscription(data);
      } catch (_error) {
        console.error('Error fetching subscription:', error);
        toast({
          title: 'Error',
          description: 'Failed to load subscription information',
          variant: 'destructive',
        });
      } finally {
        setLoading(false);
      }
    }

    fetchSubscription(tenantId);
  }, [tenantId, toast, supabase]);

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'active':
        return <Badge className="bg-green-500">Active</Badge>;
      case 'trialing':
        return <Badge className="bg-blue-500">Trial</Badge>;
      case 'past_due':
        return <Badge className="bg-yellow-500">Past Due</Badge>;
      case 'canceled':
        return <Badge className="bg-red-500">Canceled</Badge>;
      default:
        return <Badge>{status}</Badge>;
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }

  if (!subscription) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>No Active Subscription</CardTitle>
          <CardDescription>
            You don't have an active subscription. Please contact support or choose a plan.
          </CardDescription>
        </CardHeader>
        <CardFooter>
          <Link href="/settings/subscription/plans">
            <Button>View Plans</Button>
          </Link>
        </CardFooter>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <div className="flex justify-between items-start">
            <div>
              <CardTitle>Current Subscription</CardTitle>
              <CardDescription>
                Manage your subscription and add-ons
              </CardDescription>
            </div>
            {getStatusBadge(subscription.status)}
          </div>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <h3 className="text-lg font-medium">
              {subscription.plan?.name || 'Unknown Plan'}
            </h3>
            <p className="text-sm text-muted-foreground">
              {/* Display plan description if available */}
            </p>
          </div>

          {subscription.status === 'trialing' && (
            <div className="bg-blue-50 p-4 rounded-md flex items-start space-x-3">
              <Calendar className="h-5 w-5 text-blue-500 mt-0.5" />
              <div>
                <h4 className="font-medium text-blue-700">Trial Period</h4>
                <p className="text-sm text-blue-600">
                  Your trial ends on {subscription.trialEnd ? format(new Date(subscription.trialEnd), 'MMMM d, yyyy') : 'Unknown'}
                </p>
              </div>
            </div>
          )}

          {subscription.status === 'canceled' && (
            <div className="bg-red-50 p-4 rounded-md flex items-start space-x-3">
              <AlertCircle className="h-5 w-5 text-red-500 mt-0.5" />
              <div>
                <h4 className="font-medium text-red-700">Subscription Canceled</h4>
                <p className="text-sm text-red-600">
                  Your subscription has been canceled
                  {subscription.canceledAt ? ` on ${format(new Date(subscription.canceledAt), 'MMMM d, yyyy')}` : ''}.
                  Access will end on {format(new Date(subscription.currentPeriodEnd), 'MMMM d, yyyy')}.
                </p>
                <Button className="mt-2" size="sm">
                  <Link href="/settings/subscription/plans">Renew Subscription</Link>
                </Button>
              </div>
            </div>
          )}

          <div className="border-t pt-4">
            <h4 className="font-medium mb-2">Billing Details</h4>
            <div className="grid grid-cols-2 gap-2 text-sm">
              <div>Billing Cycle:</div>
              <div className="font-medium">{subscription.billingCycle === 'monthly' ? 'Monthly' : 'Yearly'}</div>

              <div>Current Period:</div>
              <div className="font-medium">
                {format(new Date(subscription.currentPeriodStart), 'MMM d, yyyy')} - {format(new Date(subscription.currentPeriodEnd), 'MMM d, yyyy')}
              </div>
            </div>
          </div>

          {subscription.addons && subscription.addons.length > 0 && (
            <div className="border-t pt-4">
              <h4 className="font-medium mb-2">Add-ons</h4>
              <ul className="space-y-2">
                {subscription.addons?.map((addon) => (
                  <li key={addon.id} className="flex justify-between items-center p-2 bg-gray-50 rounded-md">
                    <div>
                      <span className="font-medium">{addon.addon?.name}</span>
                      {addon.quantity > 1 && <span className="ml-2 text-sm">× {addon.quantity}</span>}
                    </div>
                    <Badge variant={addon.status === 'active' ? 'default' : 'outline'}>
                      {addon.status === 'active' ? 'Active' : 'Canceled'}
                    </Badge>
                  </li>
                ))}
              </ul>
            </div>
          )}

          <div className="border-t pt-4">
            <h4 className="font-medium mb-2">Plan Features</h4>
            <ul className="space-y-1">
              {subscription.plan?.features && Object.entries(subscription.plan.features).map(([key, value]) => {
                if (typeof value === 'boolean' && value) {
                  return (
                    <li key={key} className="flex items-center">
                      <CheckCircle className="h-4 w-4 text-green-500 mr-2" />
                      <span>{key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}</span>
                    </li>
                  );
                } else if (Array.isArray(value) && value.length > 0) {
                  return (
                    <li key={key} className="flex items-start">
                      <CheckCircle className="h-4 w-4 text-green-500 mr-2 mt-0.5" />
                      <div>
                        <span>{key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}: </span>
                        <span className="font-medium">{value.join(', ')}</span>
                      </div>
                    </li>
                  );
                } else if (typeof value === 'number' || typeof value === 'string') {
                  return (
                    <li key={key} className="flex items-center">
                      <CheckCircle className="h-4 w-4 text-green-500 mr-2" />
                      <span>
                        {key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}: <span className="font-medium">{value}</span>
                      </span>
                    </li>
                  );
                }
                return null;
              })}
            </ul>
          </div>
        </CardContent>
        <CardFooter className="flex justify-between">
          <Link href="/settings/subscription/plans">
            <Button variant="outline">Change Plan</Button>
          </Link>
          <Link href="/settings/subscription/addons">
            <Button>Manage Add-ons</Button>
          </Link>
        </CardFooter>
      </Card>
    </div>
  );
}
