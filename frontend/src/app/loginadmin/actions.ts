'use server'

// Use createServerClient from @supabase/ssr for Server Actions
import { createServerClient, type CookieOptions } from '@supabase/ssr'
import { cookies } from 'next/headers'
import { redirect } from 'next/navigation'
import { Database } from '@/lib/supabase/database.types'

export async function loginAdminAction(formData: FormData): Promise<{ error: string } | void> {
  const email = formData.get('email') as string
  const password = formData.get('password') as string

  if (!email || !password) {
    return { error: 'Email and password are required.' }
  }

  // Get the cookie store
  const cookieStore = await cookies()

  // Use createServerClient from @supabase/ssr, configured for Server Actions
  const supabase = createServerClient<Database>(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        get(name: string) {
          return cookieStore.get(name)?.value
        },
        set(name: string, value: string, options: CookieOptions) {
          try {
            cookieStore.set({ name, value, ...options })
          } catch (_error) {
            // The `set` method was called from a Server Component.
            // This can be ignored if you have middleware refreshing
            // user sessions.
          }
        },
        remove(name: string, options: CookieOptions) {
          try {
            cookieStore.set({ name, value: '', ...options }) // Use set with empty value for remove
          } catch (_error) {
            // The `delete` (`set` with empty value) method was called from a Server Component.
            // This can be ignored if you have middleware refreshing
            // user sessions.
          }
        },
      },
    }
  )

  console.log(`Server Action: Attempting login for ${email}`)

  const { data: { user } } = await supabase.auth.signInWithPassword({
    email,
    password,
  })

  if (!user) {
     console.error('Server Action: No user returned after successful sign-in attempt.')
     return { error: 'Login failed: No user data received.' }
  }

  console.log(`Server Action: Sign-in successful for user ${user.id}, email: ${user.email}`)

  // Check if the user is a superadmin
  const allowedAdmins = ['<EMAIL>', '<EMAIL>']
  const userEmail = user.email // Email should exist if login succeeded

  if (!userEmail || !allowedAdmins.includes(userEmail)) {
    console.log(`Server Action: User ${userEmail} is not an allowed admin. Signing out.`)
    // Sign out the non-admin user before returning error
    await supabase.auth.signOut()
    return { error: 'You do not have permission to access the admin dashboard.' }
  }

  // If login is successful and user is an admin, redirect to the intermediate page
  console.log(`Server Action: Admin user ${userEmail} authenticated. Redirecting to /auth/post-login.`)
  redirect('/auth/post-login')
}
