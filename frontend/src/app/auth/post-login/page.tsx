import { createServerClient, type CookieOptions } from '@supabase/ssr';
import { cookies } from 'next/headers';
import { redirect } from 'next/navigation';
import { Database } from '@/lib/supabase/database.types';
import { UserRole } from '@/lib/types'; // Assuming UserRole type is defined here

// This page is a server component to handle redirection server-side
export default async function PostLoginPage() {
  const cookieStore = await cookies(); // Await the cookies() call
  const supabase = createServerClient<Database>(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        get(name: string) {
          return cookieStore.get(name)?.value;
        },
        set(name: string, value: string, options: CookieOptions) {
          // Setting cookies might be needed if the token needs refreshing during getUser
          try {
             cookieStore.set({ name, value, ...options });
          } catch (_error) {
            console.error('Failed to set cookie in post-login route:', error);
          }
        },
        remove(name: string, options: CookieOptions) {
           try {
             cookieStore.delete({ name, ...options });
           } catch (_error) {
            console.error('Failed to remove cookie in post-login route:', error);
          }
        }
      },
    }
  );

  const { data: { user }, error } = await supabase.auth.getUser();

  if (error || !user) {
    // Handle error or no user found - redirect back to login
    console.error('Error fetching user post-login or user not found:', error);
    return redirect('/login'); // Use return redirect()
  }

  // Check for Superadmin email
  if (user.email === '<EMAIL>') {
    return redirect('/superadmin');
  }

  // Check for Partner role for regular admin dashboard
  // Assumes role is stored in app_metadata or user_metadata
  const userRole = user.app_metadata?.role || user.user_metadata?.role as UserRole;
  if (userRole === 'partner') {
    return redirect('/admin');
  }

  // Default redirect for other authenticated users (e.g., clients, other staff)
  // Adjust this default as needed (e.g., /client-portal, /dashboard)
  console.log(`User ${user.email} with role ${userRole} redirected to default /dashboard`);
  return redirect('/dashboard'); // Default fallback

  // Component must return JSX or null, although unreachable due to redirects
  // return null;
}
