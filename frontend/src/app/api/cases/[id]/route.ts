// @ts-nocheck - API route type issues
// frontend/src/app/api/cases/[id]/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { withAuth, AuthUser, UserRole } from '@/lib/auth/server-exports';
import { createServices } from '@/lib/services';
import { z } from 'zod';
import type { SupabaseClient } from '@supabase/supabase-js';
import { Database } from '@/lib/database.types';

// Define interfaces for case data
interface Case {
  id: string;
  title: string;
  description?: string | null;
  sensitive?: boolean;
  client_id: string;
  metadata?: Record<string, any>;
  status?: 'pending' | 'active' | 'closed' | 'rejected';
  rejection_reason?: string | null;
  created_at?: string;
  updated_at?: string;
  [key: string]: any;
}

interface UpdateCaseData {
  title?: string;
  description?: string | null;
  sensitive?: boolean;
  metadata?: Record<string, any>;
  status?: 'pending' | 'active' | 'closed' | 'rejected';
  rejection_reason?: string | null;
}

// We'll use the schema from the case service
/*
const UpdateCaseSchema = z.object({
  title: z.string().min(1, 'Title is required').optional(),
  description: z.string().nullable().optional(),
  sensitive: z.boolean().optional(),
  metadata: z.record(z.any()).optional(),
  status: z.enum(['pending', 'active', 'closed', 'rejected']).optional(),
  rejection_reason: z.string().nullable().optional(),
});
*/

// GET /api/cases/[id] - Fetch a specific case by ID
export const GET = withAuth(
  [UserRole.Partner, UserRole.Attorney, UserRole.Paralegal, UserRole.Staff],async (
  req: NextRequest,
  user: AuthUser,
  supabase: SupabaseClient<Database, "public", Database['public']>,
  context: Record<string, unknown>
): Promise<Response> => {
  try {
    const params = context.params as { id: string } | undefined;

    if (!params || !params.id) {
      return NextResponse.json({ error: 'No parameters provided' }, { status: 400 });
    }

    const { id } = params;

    if (!id || !z.string().uuid().safeParse(id).success) {
      return NextResponse.json({ error: 'Invalid case ID' }, { status: 400 });
    }

    // Create services instance
    const services = createServices(supabase, user.tenantId);

    try {
      // Get case with related data using the service
      const caseData = await services.cases.getById(id);

      return NextResponse.json(caseData);
    } catch (_error) {
      if (error instanceof Error && error.message === 'Case not found') {
        return NextResponse.json({ error: 'Case not found' }, { status: 404 });
      }
      throw error;
    }
  } catch (_error) {
    console.error('Error in GET /api/cases/[id]:', error);
    return NextResponse.json({ error: 'Internal server error', details: String(error) }, { status: 500 });
  }
});

// PUT /api/cases/[id] - Update a case
export const PUT = withAuth(
  [UserRole.Partner, UserRole.Attorney, UserRole.Paralegal, UserRole.Staff],async (
  req: NextRequest,
  user: AuthUser,
  supabase: SupabaseClient<Database, "public", Database['public']>,
  context: Record<string, unknown>
): Promise<Response> => {
  try {
    const params = context.params as { id: string } | undefined;

    if (!params || !params.id) {
      return NextResponse.json({ error: 'No parameters provided' }, { status: 400 });
    }

    const { id } = params;

    if (!id || !z.string().uuid().safeParse(id).success) {
      return NextResponse.json({ error: 'Invalid case ID' }, { status: 400 });
    }

    const body = await req.json() as UpdateCaseData;

    // Create services instance
    const services = createServices(supabase, user.tenantId);

    try {
      // Update case using the service
      const updatedCase = await services.cases.update(id, body, user.id);
      return NextResponse.json(updatedCase);
    } catch (_error) {
      if (error instanceof Error && error.message === 'Case not found') {
        return NextResponse.json({ error: 'Case not found' }, { status: 404 });
      } else if (error instanceof z.ZodError) {
        return NextResponse.json({ error: 'Validation failed', details: error.errors }, { status: 400 });
      }
      throw error;
    }
  } catch (_error) {
    console.error('Error in PUT /api/cases/[id]:', error);
    if (error instanceof z.ZodError) {
      return NextResponse.json({ error: 'Validation failed', details: error.errors }, { status: 400 });
    }
    return NextResponse.json({ error: 'Internal server error', details: String(error) }, { status: 500 });
  }
});

// DELETE /api/cases/[id] - Delete a case
export const DELETE = withAuth(
  [UserRole.Partner, UserRole.Attorney, UserRole.Paralegal, UserRole.Staff],async (
  req: NextRequest,
  user: AuthUser,
  supabase: SupabaseClient<Database, "public", Database['public']>,
  context: Record<string, unknown>
): Promise<Response> => {
  try {
    const params = context.params as { id: string } | undefined;

    if (!params || !params.id) {
      return NextResponse.json({ error: 'No parameters provided' }, { status: 400 });
    }

    const { id } = params;

    if (!id || !z.string().uuid().safeParse(id).success) {
      return NextResponse.json({ error: 'Invalid case ID' }, { status: 400 });
    }

    // Create services instance
    const services = createServices(supabase, user.tenantId);

    try {
      // Delete case using the service
      const success = await services.cases.delete(id, user.id);

      if (success) {
        return NextResponse.json({ success: true, message: 'Case deleted successfully' });
      } else {
        return NextResponse.json({ error: 'Case not found' }, { status: 404 });
      }
    } catch (_error) {
      if (error instanceof Error && error.message === 'Case not found') {
        return NextResponse.json({ error: 'Case not found' }, { status: 404 });
      } else if (error instanceof Error && error.message.includes('Case has associated tasks')) {
        return NextResponse.json({ error: 'Cannot delete case with associated tasks' }, { status: 409 });
      }
      throw error;
    }
  } catch (_error) {
    console.error('Error in DELETE /api/cases/[id]:', error);
    return NextResponse.json({ error: 'Internal server error', details: String(error) }, { status: 500 });
  }
});
