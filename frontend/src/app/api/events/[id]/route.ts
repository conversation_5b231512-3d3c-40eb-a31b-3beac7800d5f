// @ts-nocheck - API route type issues
// frontend/src/app/api/events/[id]/route.ts
import { withAuth, UserRole } from '@/lib/auth/server-exports';
import { NextRequest, NextResponse } from 'next/server';
import type { AuthUser } from '@/lib/auth/server-exports';
import type { SupabaseClient } from '@supabase/supabase-js';
import * as z from 'zod';
import { createServices } from '@/lib/services';
import { EventSchema } from '@/lib/services/calendar-event-services';

// Define the expected context type for this dynamic route
interface EventRouteContext {
  params: {
    id: string;
  };
}

// GET /api/events/[id] - Get a specific calendar event by ID
export const GET = withAuth(
  [UserRole.Partner, UserRole.Attorney, UserRole.Paralegal, UserRole.Staff],
  async (
  req: NextRequest,
  user: AuthUser,
  supabase: SupabaseClient,
  context: EventRouteContext
) => {
  try {
    const { id } = context.params;

    if (!id || !z.string().uuid().safeParse(id).success) {
      return NextResponse.json({ error: 'Invalid event ID format' }, { status: 400 });
    }

    // Create services with the authenticated user's tenant ID
    const services = createServices(supabase, user.tenantId);

    // Use the calendar event service to get the event with all its related data
    const event = await services.calendarEvents.getById(id);

    if (!event) {
      return NextResponse.json({ error: 'Calendar event not found' }, { status: 404 });
    }

    return NextResponse.json(event);
  } catch (_error) {
    console.error('Error in GET /api/events/[id]:', error);
    return NextResponse.json({ error: 'Internal server error', details: String(error) }, { status: 500 });
  }
});

// PUT /api/events/[id] - Update a calendar event
export const PUT = withAuth(
  [UserRole.Partner, UserRole.Attorney, UserRole.Paralegal, UserRole.Staff],
  async (
  req: NextRequest,
  user: AuthUser,
  supabase: SupabaseClient,
  context: EventRouteContext
) => {
  try {
    const { id } = context.params;

    // Validate UUID format
    if (!z.string().uuid().safeParse(id).success) {
      return NextResponse.json({ error: 'Invalid event ID format' }, { status: 400 });
    }

    // Get request body
    const data = await req.json();

    // Validate the update data
    const validationResult = EventSchema.partial().safeParse(data);
    if (!validationResult.success) {
      return NextResponse.json({
        error: 'Validation failed',
        details: validationResult.error.errors
      }, { status: 400 });
    }

    // Create services with the authenticated user's tenant ID
    const services = createServices(supabase, user.tenantId);

    // Update the event using the calendar event service
    const updatedEvent = await services.calendarEvents.update(id, user.id, validationResult.data);

    if (!updatedEvent) {
      return NextResponse.json({ error: 'Calendar event not found or update failed' }, { status: 404 });
    }

    return NextResponse.json(updatedEvent);
  } catch (_error) {
    console.error('Error in PUT /api/events/[id]:', error);
    if (error instanceof z.ZodError) {
      return NextResponse.json({ error: 'Validation failed', details: error.errors }, { status: 400 });
    }
    return NextResponse.json({ error: 'Internal server error', details: String(error) }, { status: 500 });
  }
});

// DELETE /api/events/[id] - Delete a calendar event
export const DELETE = withAuth(
  [UserRole.Partner, UserRole.Attorney, UserRole.Paralegal, UserRole.Staff],
  async (
  req: NextRequest,
  user: AuthUser,
  supabase: SupabaseClient,
  context: EventRouteContext
) => {
  try {
    const { id } = context.params;

    if (!id || !z.string().uuid().safeParse(id).success) {
      return NextResponse.json({ error: 'Invalid event ID format' }, { status: 400 });
    }

    // Create services with the authenticated user's tenant ID
    const services = createServices(supabase, user.tenantId);

    // Delete the event
    const deleted = await services.calendarEvents.delete(id);

    if (!deleted) {
      return NextResponse.json({ error: 'Calendar event not found or deletion failed' }, { status: 404 });
    }

    return NextResponse.json({ success: true });
  } catch (_error) {
    console.error('Error in DELETE /api/events/[id]:', error);
    return NextResponse.json({ error: 'Internal server error', details: String(error) }, { status: 500 });
  }
});
