// @ts-nocheck - API route type issues
/**
 * API route for generating insights from prioritized activities
 * Uses prioritization scores from ML model to focus on high-priority activities
 */
import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';
import { OpenAI } from 'openai'; // Import OpenAI type
import { z } from 'zod';
import { Activity } from '@/lib/ml/features';
import { activityPriorityService, PriorityLevel } from '@/lib/services/activity-priority-service';
import { insightsService } from '@/lib/services/insights-service';

// Environment variables validation
const envSchema = z.object({
  NEXT_PUBLIC_SUPABASE_URL: z.string().url(),
  NEXT_PUBLIC_SUPABASE_ANON_KEY: z.string().min(1),
  CRON_SECRET: z.string().min(1).optional(),
  OPENAI_API_KEY: z.string().min(1).optional(),
});

// Validate environment variables with fallbacks for build time
const env = envSchema.safeParse({
  NEXT_PUBLIC_SUPABASE_URL: process.env.NEXT_PUBLIC_SUPABASE_URL || 'https://placeholder.supabase.co',
  NEXT_PUBLIC_SUPABASE_ANON_KEY: process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || 'placeholder-anon-key',
  CRON_SECRET: process.env.CRON_SECRET || 'placeholder-cron-secret',
  OPENAI_API_KEY: process.env.OPENAI_API_KEY || 'placeholder-openai-key',
});

if (!env.success) {
  console.error('Environment validation failed:', env.error);
}

// Initialize Supabase client with fallback for build time
const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL || 'https://placeholder.supabase.co',
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || 'placeholder-anon-key'
);

// Initialize OpenAI client
let openai: OpenAI | null = null; // Use the imported OpenAI type
if (process.env.OPENAI_API_KEY) {
  openai = new OpenAI({
    apiKey: process.env.OPENAI_API_KEY,
  });
} else {
  console.warn('OPENAI_API_KEY not set. Insight generation disabled.');
}

// Interface for prioritized activities
interface PrioritizedActivity {
  id: string;
  tenant_id: string;
  user_id: string | null;
  type: string;
  target_id: string | null;
  target_type: string | null;
  metadata: {
    priority_level: PriorityLevel;
    priority_score: number;
    [key: string]: unknown; // Replace 'any' with 'unknown' for index signature
  };
  created_at: string;
}

// Interface for generated insights
interface Insight {
  id: string;
  tenant_id: string;
  user_id: string | null;
  title: string;
  description: string;
  priority: PriorityLevel;
  source_activities: string[];
  actions: string[]; // Replace 'any[]' with 'string[]'
  metadata: Record<string, unknown>; // Replace 'any' with 'Record<string, unknown>'
  created_at: string;
}

/**
 * Generate insights for a specific tenant using prioritized activities
 */
async function generateInsightsForTenant(tenantId: string, openai: OpenAI): Promise<number> {
  try {
    console.log(`Generating insights for tenant: ${tenantId}`);

    // Get prioritized activities for this tenant
    const { data: activities, error } = await supabase
      .from('activities')
      .select('*')
      .eq('tenant_id', tenantId)
      .not('metadata->priority_processed', 'is', null)
      .order('created_at', { ascending: false })
      .limit(100);

    if (error) {
      throw new Error(`Error fetching activities: ${error.message}`);
    }

    if (!activities || activities.length === 0) {
      console.log(`No prioritized activities found for tenant: ${tenantId}`);
      return 0;
    }

    // Filter and group activities
    const prioritizedActivities = activities
      .filter(a => a.metadata?.priority_processed)
      .sort((a, b) =>
        (b.metadata?.priority_score || 0) - (a.metadata?.priority_score || 0)
      );

    // Get priority reasoning for each activity
    for (const activity of prioritizedActivities) {
      // Only process if we don't already have reasoning
      if (!activity.metadata?.priority_reasoning) {
        try {
          const reasoning = await insightsService.getPriorityReasoning(activity as Activity);

          // Update activity with reasoning
          await supabase
            .from('activities')
            .update({
              metadata: {
                ...activity.metadata,
                priority_reasoning: reasoning.reasoning,
                priority_features: reasoning.features
              }
            })
            .eq('id', activity.id)
            .eq('tenant_id', tenantId);
        } catch (_error) {
          console.error(`Error getting priority reasoning for activity ${activity.id}:`, error);
        }
      }
    }

    // Group by case
    const caseGroups = new Map<string, PrioritizedActivity[]>();
    // Group by document
    const documentGroups = new Map<string, PrioritizedActivity[]>();

    // Organize activities into groups
    for (const activity of prioritizedActivities) {
      if (activity.target_type === 'case' && activity.target_id) {
        const existing = caseGroups.get(activity.target_id) || [];
        existing.push(activity as PrioritizedActivity);
        caseGroups.set(activity.target_id, existing);
      } else if (activity.target_type === 'document' && activity.target_id) {
        const existing = documentGroups.get(activity.target_id) || [];
        existing.push(activity as PrioritizedActivity);
        documentGroups.set(activity.target_id, existing);
      }
    }

    // Process high-priority case groups first
    let insightsGenerated = 0;

    // Process case groups
    for (const [caseId, caseActivities] of caseGroups.entries()) {
      // Only process cases with high-priority activities or multiple activities
      if (
        caseActivities.some(a => a.metadata.priority_level === PriorityLevel.HIGH) ||
        caseActivities.length >= 3
      ) {
        const insight = await generateCaseInsight(tenantId, caseId, caseActivities, openai);
        if (insight) {
          await saveInsight(insight);
          insightsGenerated++;
        }
      }
    }

    // Process document groups (similar logic)
    for (const [documentId, documentActivities] of documentGroups.entries()) {
      if (
        documentActivities.some(a => a.metadata.priority_level === PriorityLevel.HIGH) ||
        documentActivities.length >= 3
      ) {
        const insight = await generateDocumentInsight(tenantId, documentId, documentActivities, openai);
        if (insight) {
          await saveInsight(insight);
          insightsGenerated++;
        }
      }
    }

    console.log(`Generated ${insightsGenerated} insights for tenant: ${tenantId}`);
    return insightsGenerated;
  } catch (error: unknown) {
    console.error('Error generating insights for tenant:', error);
    let errorMessage = 'Unknown error generating insights';
    if (error instanceof Error) {
      errorMessage = error.message;
    } else if (typeof error === 'string') {
      errorMessage = error;
    }
    return 0;
  }
}

/**
 * Generate an insight for a case based on its activities
 */
async function generateCaseInsight(
  tenantId: string,
  caseId: string,
  activities: PrioritizedActivity[],
  openai: OpenAI
): Promise<Insight | null> {
  try {
    // Get case details
    const { data: caseData, error: caseError } = await supabase
      .from('cases')
      .select('*')
      .eq('id', caseId)
      .eq('tenant_id', tenantId)
      .single();

    if (caseError || !caseData) {
      console.error(`Error fetching case ${caseId}:`, caseError);
      return null;
    }

    // Determine highest priority from activities
    const highestPriority = activities.reduce((highest, activity) => {
      const activityPriority = activity.metadata.priority_level;
      if (activityPriority === PriorityLevel.HIGH) return PriorityLevel.HIGH;
      if (activityPriority === PriorityLevel.MEDIUM && highest !== PriorityLevel.HIGH)
        return PriorityLevel.MEDIUM;
      return highest;
    }, PriorityLevel.LOW);

    // Format activity data for AI
    const activityDescriptions = activities.map(a => {
      return {
        type: a.type,
        timestamp: a.created_at,
        priority: a.metadata.priority_level,
        details: a.metadata.description || `${a.type} activity`,
        user: a.user_id
      };
    });

    // Use OpenAI to generate the insight
    const response = await openai.chat.completions.create({
      model: 'gpt-4-turbo-preview',
      messages: [
        {
          role: 'system',
          content: `You are a legal assistant that generates insights based on case activities.
                    Focus on actionable insights, deadlines, and important next steps.
                    Be concise and specific. Format your response as JSON with "title" and "description"
                    fields, and an "actions" array of suggested actions.`
        },
        {
          role: 'user',
          content: JSON.stringify({
            case: {
              id: caseId,
              title: caseData.title,
              status: caseData.status,
              priority: highestPriority,
              metadata: caseData.metadata
            },
            activities: activityDescriptions
          })
        }
      ],
      response_format: { type: 'json_object' }
    });

    const aiResponse = JSON.parse(response.choices[0].message.content);

    // Build the insight object
    const insight: Insight = {
      id: crypto.randomUUID(),
      tenant_id: tenantId,
      user_id: null, // System-generated
      title: aiResponse.title,
      description: aiResponse.description,
      priority: highestPriority,
      source_activities: activities.map(a => a.id),
      actions: aiResponse.actions || [],
      metadata: {
        case_id: caseId,
        case_title: caseData.title,
        case_status: caseData.status,
        ai_generated: true,
        model: 'gpt-4-turbo-preview',
        generation_method: 'ml_prioritized',
        priority_reasoning: activities.map(a => a.metadata?.priority_reasoning || '').filter(Boolean).join(' '),
        ml_model_version: 'activity_classifier_20250413'  // Reference to our trained model
      },
      created_at: new Date().toISOString()
    };

    return insight;
  } catch (error: unknown) {
    console.error(`Error generating case insight for ${caseId}:`, error);
    let errorMessage = 'Unknown error generating case insight';
    if (error instanceof Error) {
      errorMessage = error.message;
    } else if (typeof error === 'string') {
      errorMessage = error;
    }
    return null;
  }
}

/**
 * Generate an insight for a document based on its activities
 */
async function generateDocumentInsight(
  tenantId: string,
  documentId: string,
  activities: PrioritizedActivity[],
  openai: OpenAI
): Promise<Insight | null> {
  try {
    // Get document details
    const { data: docData, error: docError } = await supabase
      .from('documents')
      .select('*')
      .eq('id', documentId)
      .eq('tenant_id', tenantId)
      .single();

    if (docError || !docData) {
      console.error(`Error fetching document ${documentId}:`, docError);
      return null;
    }

    // Similar logic to case insights
    const highestPriority = activities.reduce((highest, activity) => {
      const activityPriority = activity.metadata.priority_level;
      if (activityPriority === PriorityLevel.HIGH) return PriorityLevel.HIGH;
      if (activityPriority === PriorityLevel.MEDIUM && highest !== PriorityLevel.HIGH)
        return PriorityLevel.MEDIUM;
      return highest;
    }, PriorityLevel.LOW);

    // Format activity data for AI
    const activityDescriptions = activities.map(a => {
      return {
        type: a.type,
        timestamp: a.created_at,
        priority: a.metadata.priority_level,
        details: a.metadata.description || `${a.type} activity`,
        user: a.user_id
      };
    });

    // Use OpenAI to generate the insight
    const response = await openai.chat.completions.create({
      model: 'gpt-4-turbo-preview',
      messages: [
        {
          role: 'system',
          content: `You are a legal assistant that generates insights based on document activities.
                    Focus on actionable insights related to document review, analysis, and required actions.
                    Be concise and specific. Format your response as JSON with "title" and "description"
                    fields, and an "actions" array of suggested actions.`
        },
        {
          role: 'user',
          content: JSON.stringify({
            document: {
              id: documentId,
              title: docData.title,
              type: docData.type,
              status: docData.status,
              metadata: docData.metadata
            },
            activities: activityDescriptions
          })
        }
      ],
      response_format: { type: 'json_object' }
    });

    const aiResponse = JSON.parse(response.choices[0].message.content);

    // Build the insight object
    const insight: Insight = {
      id: crypto.randomUUID(),
      tenant_id: tenantId,
      user_id: null, // System-generated
      title: aiResponse.title,
      description: aiResponse.description,
      priority: highestPriority,
      source_activities: activities.map(a => a.id),
      actions: aiResponse.actions || [],
      metadata: {
        document_id: documentId,
        document_title: docData.title,
        document_type: docData.type,
        ai_generated: true,
        model: 'gpt-4-turbo-preview',
        generation_method: 'ml_prioritized',
        priority_reasoning: activities.map(a => a.metadata?.priority_reasoning || '').filter(Boolean).join(' '),
        ml_model_version: 'activity_classifier_20250413'  // Reference to our trained model
      },
      created_at: new Date().toISOString()
    };

    return insight;
  } catch (error: unknown) {
    console.error(`Error generating document insight for ${documentId}:`, error);
    let errorMessage = 'Unknown error generating document insight';
    if (error instanceof Error) {
      errorMessage = error.message;
    } else if (typeof error === 'string') {
      errorMessage = error;
    }
    return null;
  }
}

/**
 * Save an insight to the database
 */
async function saveInsight(insight: Insight): Promise<boolean> {
  try {
    const { error } = await supabase
      .from('insights')
      .insert(insight);

    if (error) {
      console.error('Error saving insight:', error);
      return false;
    }

    return true;
  } catch (error: unknown) {
    console.error('Error saving insight:', error);
    let errorMessage = 'Unknown error saving insight';
    if (error instanceof Error) {
      errorMessage = error.message;
    } else if (typeof error === 'string') {
      errorMessage = error;
    }
    return false;
  }
}

/**
 * Main API handler
 */
export async function GET(_req: NextRequest) {
  // Verify cron secret for security
  const authHeader = req.headers.get('authorization');
  if (!authHeader || !authHeader.startsWith('Bearer ') || authHeader.split(' ')[1] !== process.env.CRON_SECRET) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  try {
    // Handle tenant-specific processing if specified
    const tenantId = req.nextUrl.searchParams.get('tenantId');

    if (tenantId) {
      // Generate insights for specific tenant
      if (!openai) {
        return NextResponse.json(
          { error: 'OpenAI client not initialized. Check API key.' },
          { status: 500 }
        );
      }
      const insightCount = await generateInsightsForTenant(tenantId, openai);
      return NextResponse.json({
        success: true,
        message: `Generated ${insightCount} insights for tenant ${tenantId}`,
      });
    } else {
      // Generate for all active tenants
      const { data: tenants, error: tenantError } = await supabase
        .from('firms')
        .select('tenant_id')
        .eq('status', 'active');

      if (tenantError) {
        throw new Error(`Error fetching tenants: ${tenantError.message}`);
      }

      if (!tenants || tenants.length === 0) {
        return NextResponse.json({ message: 'No active tenants found' });
      }

      // Process each tenant
      const results = [];
      for (const tenant of tenants) {
        if (!openai) {
          return NextResponse.json(
            { error: 'OpenAI client not initialized. Check API key.' },
            { status: 500 }
          );
        }
        const insightCount = await generateInsightsForTenant(tenant.tenant_id, openai);
        results.push({
          tenantId: tenant.tenant_id,
          insightsGenerated: insightCount,
        });
      }

      return NextResponse.json({
        success: true,
        message: `Generated insights for ${results.length} tenants`,
        results,
      });
    }
  } catch (error: unknown) {
    console.error('Error generating insights:', error);
    let errorMessage = 'Unknown error generating insights';
    if (error instanceof Error) {
      errorMessage = error.message;
    } else if (typeof error === 'string') {
      errorMessage = error;
    }
    return NextResponse.json(
      { error: 'Failed to generate insights', details: errorMessage },
      { status: 500 }
    );
  }
}

// For serverless function warmup
export async function HEAD() {
  return new NextResponse(null, { status: 200 });
}
