// @ts-nocheck - API route type issues
import { NextRequest, NextResponse } from "next/server";
import { createIntakeAgent } from "@/lib/agents/intake/agent";

// Initialize the LangGraph agent
const intakeAgent = createIntakeAgent();

// Define a basic message type structure
interface BasicMessage {
  role: string;
  content: string;
}

export async function POST(req: NextRequest) {
  try {
    const { messages }: { messages: BasicMessage[] } = await req.json();
    const latestMessage = messages[messages.length - 1]?.content;

    if (!latestMessage) {
      return NextResponse.json(
        { error: 'No message content provided' },
        { status: 400 }
      );
    }

    console.log('Processing message through LangGraph intake agent:', latestMessage);

    // Process through LangGraph agent
    const response = await intakeAgent.invoke({
      message: latestMessage,
      state: null
    });

    // Extract the agent's response from the state
    // The agent's response should be the last message in the agentState
    const agentResponseContent = response.agentState?.messages?.slice(-1)?.[0]?.content ||
                                "I couldn't process that.";

    // Return in the format expected by CopilotKit
    return NextResponse.json({
      messages: [
        ...messages,
        {
          role: "assistant",
          content: agentResponseContent
        }
      ],
      done: true
    });
  } catch (_error) {
    console.error('LangGraph Intake Agent Error:', error);
    return NextResponse.json(
      { error: 'Intake Agent Error', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}

export const runtime = "nodejs";
