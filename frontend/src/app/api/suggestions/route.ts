// @ts-nocheck - API route type issues
import { NextRequest, NextResponse } from 'next/server';
import OpenAI from 'openai';

// Define interface for request body
interface SuggestionRequest {
  sectionTitle: string;
  sectionContent: string;
  selectedText?: string;
}

export async function POST(request: NextRequest): Promise<Response> {
  try {
    const body = await request.json() as SuggestionRequest;
    const { sectionTitle, sectionContent, selectedText } = body;

    if (!sectionTitle || !sectionContent) {
      return NextResponse.json({ error: 'Missing required fields: sectionTitle and sectionContent' }, { status: 400 });
    }

    // Check for OpenAI API key
    const apiKey = process.env.OPENAI_API_KEY;
    if (!apiKey) {
      return NextResponse.json({
        error: 'OpenAI API key is not configured'
      }, { status: 500 });
    }

    // Initialize OpenAI client
    const openai = new OpenAI({
      apiKey: apiKey,
    });

    // --- Construct the Prompt ---
    const promptLines = [
      `You are an expert legal document assistant. Please provide a concise suggestion to improve the following section of a legal document.`,
      `Section Title: ${sectionTitle}`,
    ];

    if (selectedText) {
      promptLines.push(`Focus specifically on improving this selected text: "${selectedText}"`);
      promptLines.push(`Full Section Content (for context): "${sectionContent}"`);
    } else {
      promptLines.push(`Section Content: "${sectionContent}"`);
      promptLines.push(`Provide a general suggestion for improving the entire section.`);
    }
    promptLines.push(`Keep the suggestion brief and actionable.`);

    const prompt = promptLines.join('\n\n');

    // --- Call OpenAI API ---
    console.log("Sending prompt to OpenAI:", prompt); // Optional: Log the prompt for debugging

    const completion = await openai.chat.completions.create({
      model: "gpt-3.5-turbo", // Or your preferred model
      messages: [{ role: "user", content: prompt }],
      max_tokens: 100, // Limit the response length
      temperature: 0.7, // Adjust creativity vs. determinism
    });

    const suggestion = completion.choices[0]?.message?.content?.trim() || "No suggestion available.";

    console.log("Received suggestion from OpenAI:", suggestion); // Optional: Log the result

    // --- Return the Suggestion ---
    return NextResponse.json({ suggestion });

  } catch (_error) {
    console.error('Error fetching suggestion from OpenAI:', error);
    // Avoid exposing detailed errors to the client
    let errorMessage = 'Failed to get suggestion';
    if (error instanceof Error) {
        // You might want more specific error handling based on OpenAI errors
        errorMessage = `An error occurred: ${error.message}`;
    }
    return NextResponse.json({ error: errorMessage }, { status: 500 });
  }
}
