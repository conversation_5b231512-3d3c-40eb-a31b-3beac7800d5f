// @ts-nocheck - API route type issues
import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';
import { sendSecurityAlertEmail } from '@/lib/notifications/email';
import { sendSecurityAlertSms } from '@/lib/notifications/sms';

/**
 * API endpoint for sending security notifications
 * This endpoint is protected and should only be accessible to authenticated users with proper permissions
 */
export async function POST(_req: NextRequest) {
  try {
    // Create a Supabase client with service role
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_KEY!
    );

    // Get the request body
    const { userId, title, message, severity, channels } = await req.json();

    if (!userId || !title || !message || !severity) {
      return NextResponse.json(
        { error: 'Missing required parameters: userId, title, message, severity' },
        { status: 400 }
      );
    }

    // Get user's contact information and alert preferences
    const { data: user, error: userError } = await supabase
      .from('users')
      .select('email, phone')
      .eq('id', userId)
      .single();

    if (userError) {
      console.error('Error fetching user:', userError);
      return NextResponse.json({ error: userError.message }, { status: 500 });
    }

    const { data: alertConfig, error: configError } = await supabase
      .schema('security')
      .from('alert_configs')
      .select('*')
      .eq('user_id', userId)
      .maybeSingle();

    if (configError) {
      console.error('Error fetching alert configuration:', configError);
      return NextResponse.json({ error: configError.message }, { status: 500 });
    }

    // Use default config if none exists
    const config = alertConfig || {
      email: true,
      in_app: true,
      sms: false,
      min_severity: 'medium'
    };

    // Check if the alert meets the minimum severity threshold
    const severityLevels: Record<string, number> = { low: 1, medium: 2, high: 3, critical: 4 };
    const configSeverity = severityLevels[config.min_severity as keyof typeof severityLevels] || 1;
    const alertSeverity = severityLevels[severity as keyof typeof severityLevels] || 1;

    if (alertSeverity < configSeverity) {
      return NextResponse.json({
        success: false,
        message: `Alert severity ${severity} below threshold ${config.min_severity}, not sending`
      });
    }

    // Create the alert in the database (in-app notification)
    if ((channels?.includes('in_app') || !channels) && config.in_app) {
      const { error: alertError } = await supabase
        .schema('security')
        .from('alerts')
        .insert({
          user_id: userId,
          title,
          message,
          severity,
          read: false,
          created_at: new Date().toISOString()
        });

      if (alertError) {
        console.error('Error creating alert:', alertError);
      }
    }

    // Send email notification
    if ((channels?.includes('email') || !channels) && config.email && user.email) {
      await sendSecurityAlertEmail(
        user.email,
        title,
        message,
        severity
      );
    }

    // Send SMS notification
    if ((channels?.includes('sms') || !channels) && config.sms && user.phone) {
      await sendSecurityAlertSms(
        user.phone,
        title,
        message,
        severity
      );
    }

    // Log the notification
    await supabase
      .schema('security')
      .from('events')
      .insert({
        event_type: 'system.notification_sent',
        event_category: 'system',
        user_id: userId,
        details: {
          title,
          severity,
          channels: {
            in_app: (channels?.includes('in_app') || !channels) && config.in_app,
            email: (channels?.includes('email') || !channels) && config.email,
            sms: (channels?.includes('sms') || !channels) && config.sms
          }
        },
        created_at: new Date().toISOString()
      });

    return NextResponse.json({ success: true });
  } catch (_err) {
    console.error('Error in notify API:', err);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
