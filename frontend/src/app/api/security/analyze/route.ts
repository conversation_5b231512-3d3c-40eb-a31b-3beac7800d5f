// @ts-nocheck - API route type issues
import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';
import { analyzeSecurityEvent, logAnomalyEvent } from '@/lib/security/anomaly-detection';

/**
 * API endpoint for analyzing security events for anomalies
 * This endpoint is protected and should only be accessible to authenticated users with proper permissions
 */
export async function POST(_req: NextRequest) {
  try {
    // Create a Supabase client with service role
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_KEY!
    );

    // Get the request body
    const { eventType, details, userId, eventId } = await req.json();

    if (!eventType || !userId) {
      return NextResponse.json(
        { error: 'Missing required parameters: eventType, userId' },
        { status: 400 }
      );
    }

    // Analyze the security event for anomalies
    const anomalyScore = await analyzeSecurityEvent(
      eventType,
      { ...details, eventId },
      userId
    );

    // If the anomaly score is high enough, log it as a security event
    if (anomalyScore.score >= 25) {
      await logAnomalyEvent(anomalyScore);
    }

    return NextResponse.json({ anomalyScore });
  } catch (_err) {
    console.error('Error in security analyze API:', err);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
