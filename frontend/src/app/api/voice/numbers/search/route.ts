import { NextRequest, NextResponse } from 'next/server';
import { createServerClient } from '@supabase/ssr';
import { cookies } from 'next/headers';
import { Database } from '@/lib/supabase/database.types';

export async function POST(request: NextRequest) {
  try {
    const cookieStore = await cookies();
    
    // Create Supabase client
    const supabase = createServerClient<Database>(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            return cookieStore.get(name)?.value;
          },
        },
      }
    );

    // Get the current session
    const { data: { session }, error: sessionError } = await supabase.auth.getSession();
    
    if (sessionError || !session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const { country_code, area_code, limit = 20 } = body;

    // TODO: Replace with actual Telnyx API call
    // For now, return mock available numbers
    const mockAvailableNumbers = [
      {
        phone_number: '+1212555' + Math.floor(Math.random() * 10000).toString().padStart(4, '0'),
        country_code: country_code || 'US',
        number_type: 'local',
        monthly_cost: 100, // in cents
        features: ['voice', 'sms'],
        locality: 'New York',
        region: 'NY',
      },
      {
        phone_number: '+1212555' + Math.floor(Math.random() * 10000).toString().padStart(4, '0'),
        country_code: country_code || 'US',
        number_type: 'local',
        monthly_cost: 100, // in cents
        features: ['voice', 'sms'],
        locality: 'New York',
        region: 'NY',
      },
      {
        phone_number: '+1800555' + Math.floor(Math.random() * 10000).toString().padStart(4, '0'),
        country_code: country_code || 'US',
        number_type: 'toll-free',
        monthly_cost: 200, // in cents
        features: ['voice', 'sms'],
        locality: 'Toll Free',
        region: 'US',
      },
    ];

    // Filter by area code if provided
    const filteredNumbers = area_code 
      ? mockAvailableNumbers.filter(num => num.phone_number.includes(area_code))
      : mockAvailableNumbers;

    return NextResponse.json({ 
      numbers: filteredNumbers.slice(0, limit) 
    });

  } catch (_error) {
    console.error('Error searching phone numbers:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
