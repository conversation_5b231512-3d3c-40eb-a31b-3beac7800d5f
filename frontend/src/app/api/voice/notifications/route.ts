import { NextRequest, NextResponse } from 'next/server';
import { createServerClient } from '@supabase/ssr';
import { cookies } from 'next/headers';
import { Database } from '@/lib/supabase/database.types';

export async function GET(_request: NextRequest) {
  try {
    const cookieStore = await cookies();
    
    // Create Supabase client
    const supabase = createServerClient<Database>(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            return cookieStore.get(name)?.value;
          },
        },
      }
    );

    // Get the current session
    const { data: { session }, error: sessionError } = await supabase.auth.getSession();
    
    if (sessionError || !session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get tenant ID from JWT
    const tenantId = session.user.user_metadata?.tenant_id;
    if (!tenantId) {
      return NextResponse.json({ error: 'Tenant ID not found' }, { status: 400 });
    }

    // TODO: Replace with actual failed notifications query
    // For now, return mock data
    const mockNotifications = [
      {
        id: '1',
        tenant_id: tenantId,
        notification_type: 'webhook',
        status: 'failed',
        error_message: 'Connection timeout to webhook endpoint',
        retry_count: 2,
        max_retries: 3,
        next_retry_at: new Date(Date.now() + 300000).toISOString(),
        target_endpoint: 'https://example.com/webhook',
        created_at: new Date(Date.now() - 1800000).toISOString(),
        updated_at: new Date(Date.now() - 900000).toISOString(),
      },
      {
        id: '2',
        tenant_id: tenantId,
        notification_type: 'call_failed',
        status: 'retrying',
        error_message: 'Call failed due to busy signal',
        retry_count: 1,
        max_retries: 3,
        next_retry_at: new Date(Date.now() + 600000).toISOString(),
        created_at: new Date(Date.now() - 3600000).toISOString(),
        updated_at: new Date(Date.now() - 1800000).toISOString(),
      },
    ];

    const stats = {
      total: mockNotifications.length,
      failed: mockNotifications.filter(n => n.status === 'failed').length,
      retrying: mockNotifications.filter(n => n.status === 'retrying').length,
      resolved: mockNotifications.filter(n => n.status === 'resolved').length,
    };

    return NextResponse.json({ 
      notifications: mockNotifications,
      stats 
    });

  } catch (_error) {
    console.error('Error fetching failed notifications:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
