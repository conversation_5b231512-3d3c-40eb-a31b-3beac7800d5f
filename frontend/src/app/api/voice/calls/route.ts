import { NextRequest, NextResponse } from 'next/server';
import { createServerClient } from '@supabase/ssr';
import { cookies } from 'next/headers';
import { Database } from '@/lib/supabase/database.types';

export async function GET(_request: NextRequest) {
  try {
    const cookieStore = await cookies();
    
    // Create Supabase client
    const supabase = createServerClient<Database>(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            return cookieStore.get(name)?.value;
          },
        },
      }
    );

    // Get the current session
    const { data: { session }, error: sessionError } = await supabase.auth.getSession();
    
    if (sessionError || !session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get tenant ID from JWT
    const tenantId = session.user.user_metadata?.tenant_id;
    if (!tenantId) {
      return NextResponse.json({ error: 'Tenant ID not found' }, { status: 400 });
    }

    // TODO: Replace with actual call logs query
    // For now, return mock data
    const mockCalls = [
      {
        id: '1',
        tenant_id: tenantId,
        call_id: 'call_123',
        direction: 'inbound',
        from_number: '+1234567890',
        to_number: '+1987654321',
        status: 'completed',
        duration_seconds: 180,
        start_time: new Date(Date.now() - 3600000).toISOString(),
        end_time: new Date(Date.now() - 3420000).toISOString(),
        created_at: new Date(Date.now() - 3600000).toISOString(),
      },
      {
        id: '2',
        tenant_id: tenantId,
        call_id: 'call_124',
        direction: 'outbound',
        from_number: '+1987654321',
        to_number: '+1234567890',
        status: 'failed',
        duration_seconds: 0,
        start_time: new Date(Date.now() - 7200000).toISOString(),
        created_at: new Date(Date.now() - 7200000).toISOString(),
      },
    ];

    return NextResponse.json({ calls: mockCalls });

  } catch (_error) {
    console.error('Error fetching call logs:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
