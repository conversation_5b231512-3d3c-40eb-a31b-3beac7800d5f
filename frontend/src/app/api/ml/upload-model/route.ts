// @ts-nocheck - API route type issues
import { NextRequest, NextResponse } from 'next/server';
import { Storage } from '@google-cloud/storage';
import { createServerClient } from '@supabase/ssr';
import { cookies } from 'next/headers';
import { z } from 'zod';

// Environment variables validation
const envSchema = z.object({
  GCS_BUCKET_NAME: z.string().min(1).optional(),
  GCS_SERVICE_ACCOUNT_FILE: z.string().min(1).optional(),
  NEXT_PUBLIC_SUPABASE_URL: z.string().url(),
  SUPABASE_SERVICE_KEY: z.string().min(1).optional(), // Service role key for admin operations
});

const env = envSchema.parse({
  GCS_BUCKET_NAME: process.env.GCS_BUCKET_NAME || 'placeholder-bucket',
  GCS_SERVICE_ACCOUNT_FILE: process.env.GCS_SERVICE_ACCOUNT_FILE || 'placeholder-file',
  NEXT_PUBLIC_SUPABASE_URL: process.env.NEXT_PUBLIC_SUPABASE_URL || 'https://placeholder.supabase.co',
  SUPABASE_SERVICE_KEY: process.env.SUPABASE_SERVICE_KEY || 'placeholder-service-key', // Use service role key
});

// Request body validation schema
const FormDataSchema = z.object({
  modelFile: z.instanceof(File),
  modelName: z.string().min(1),
  modelVersion: z.string().min(1),
  tenantId: z.string().uuid(), // Ensure tenant ID is provided
  metrics: z.string().optional(), // Optional JSON string of training metrics
});

export async function POST(_req: NextRequest): Promise<Response> {
  // Initialize Supabase admin client
  const cookieStore = await cookies();
  const supabase = createServerClient(
    env.NEXT_PUBLIC_SUPABASE_URL!,
    env.SUPABASE_SERVICE_KEY!, // Use service role key for admin operations
    {
      cookies: {
        get(name: string) {
          return cookieStore.get(name)?.value;
        },
        set(name: string, value: string, options: any) {
          cookieStore.set(name, value, options);
        },
        remove(name: string, options: any) {
          cookieStore.delete(name);
        },
      },
    }
  );

  let formData;
  try {
    formData = await req.formData();
  } catch (_error) {
    return NextResponse.json({ error: 'Invalid form data' }, { status: 400 });
  }

  const validationResult = FormDataSchema.safeParse({
    modelFile: formData.get('modelFile'),
    modelName: formData.get('modelName'),
    modelVersion: formData.get('modelVersion'),
    tenantId: formData.get('tenantId'),
    metrics: formData.get('metrics'),
  });

  if (!validationResult.success) {
    return NextResponse.json({
      error: 'Invalid form data',
      details: validationResult.error.format()
    }, { status: 400 });
  }

  const { modelFile, modelName, modelVersion, tenantId, metrics } = validationResult.data;

  if (!modelFile || modelFile.size === 0) {
    return NextResponse.json({
      error: 'Model file is required and cannot be empty.'
    }, { status: 400 });
  }

  if (!modelFile.name.endsWith('.onnx')) {
    return NextResponse.json({
      error: 'Invalid file type. Only .onnx files are accepted.'
    }, { status: 400 });
  }

  // Initialize GCS client
  const storage = new Storage({
    keyFilename: env.GCS_SERVICE_ACCOUNT_FILE,
  });
  const bucket = storage.bucket(env.GCS_BUCKET_NAME);

  // Define GCS path: ml-models/{tenant_id}/{model_name}/{model_version}/model.onnx
  const gcsPath = `ml-models/${tenantId}/${modelName}/${modelVersion}/model.onnx`;
  const file = bucket.file(gcsPath);

  try {
    // Get file buffer
    const buffer = Buffer.from(await modelFile.arrayBuffer());

    // Upload to GCS
    await file.save(buffer, {
      metadata: {
        contentType: 'application/octet-stream', // Standard for ONNX files
      },
    });

    console.log(`Model uploaded to GCS: ${gcsPath}`);

    // Parse metrics if provided
    let metricsObject = {};
    if (metrics) {
      try {
        metricsObject = JSON.parse(metrics);
      } catch (_e) {
        console.warn('Failed to parse metrics JSON:', e);
      }
    }

    // Update Supabase ml_models table
    const { data, error: dbError } = await supabase
      .from('ml_models')
      .upsert(
        {
          tenant_id: tenantId,
          model_name: modelName,
          model_version: modelVersion,
          model_gcs_path: gcsPath,
          metrics: metricsObject,
          last_trained_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        },
        {
          onConflict: 'tenant_id, model_name',
        }
      )
      .select()
      .single();

    if (dbError) {
      console.error('Supabase DB Error:', dbError);
      return NextResponse.json({
        error: 'Failed to update model record in database',
        details: dbError.message
      }, { status: 500 });
    }

    console.log('Supabase ml_models table updated:', data);

    return NextResponse.json({
      success: true,
      message: 'Model uploaded and registered successfully.',
      modelId: data.id,
      gcsPath: gcsPath
    }, { status: 200 });

  } catch (error: any) {
    console.error('Model Upload Error:', error);
    return NextResponse.json({
      error: 'Failed to upload model',
      details: error.message
    }, { status: 500 });
  }
}

// Add OPTIONS handler for CORS
export async function OPTIONS(): Promise<Response> {
  return new NextResponse(null, {
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}
