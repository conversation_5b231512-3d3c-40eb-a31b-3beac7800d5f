// @ts-nocheck - API route type issues
import { withAuth } from '@/lib/auth/server-exports'
import { logSecurityEvent, SecurityEventDetails } from '@/lib/security/forensics'
import { NextRequest, NextResponse } from 'next/server'
import { SupabaseClient } from '@supabase/supabase-js'

// GET - List templates (accessible to partner, attorney, staff)
export const GET = withAuth(async (_req: NextRequest, user, supabase: SupabaseClient, context): Promise<Response> => {
  try {
    console.log('Templates API - GET request from user:', user.email);
    console.log(`Templates API - Auth context: User ID: ${user.id}, Role: ${user.role}, Tenant ID: ${user.tenantId}`);

    // Debug JWT claims AND connection information
    try {
      const { data: jwtData, error: jwtError } = await supabase.rpc('debug_jwt_claims');
      console.log('JWT DEBUG - Claims structure:', jwtData);

      // Get auth connection details
      console.log('Testing auth connection debugging...');
      const { data: connData, error: connError } = await supabase.rpc('debug_auth_connection');
      console.log('AUTH CONNECTION DEBUG:', connData);

      // Log to database for inspection
      await supabase.from('auth_debug_logs').insert({ connection_info: connData });

      if (jwtError) {
        console.error('JWT debug error:', jwtError);
      }
      if (connError) {
        console.error('Connection debug error:', connError);
      }
    } catch (debugErr) {
      console.error('Error in debugging:', debugErr);
    }

    const searchParams = new URL(req.url).searchParams
    const practice_area = searchParams.get('practice_area')
    const category = searchParams.get('category')
    const state = searchParams.get('state')

    // Use the table name without schema prefix - Supabase will add it
    let query = supabase
      .from('legal_templates')
      .select('id, name, state, practice_area, category, document_type, created_at, updated_at')

    // Apply filters if provided
    if (practice_area) {
      query = query.eq('practice_area', practice_area)
    }

    if (category) {
      query = query.eq('category', category)
    }

    if (state) {
      query = query.eq('state', state)
    }

    console.log('Templates API - Executing query')
    const { data, error } = await query

    if (error) {
      console.error('Error fetching templates:', error)
      if (error.message.includes('permission denied for table')) {
        console.error('RLS POLICY VIOLATION suspected for legal_templates');
      }
      return NextResponse.json(
        { error: 'Failed to fetch templates', details: error },
        { status: 500 }
      )
    }

    console.log(`Templates API - Found ${data?.length || 0} templates`)
    return NextResponse.json({ templates: data || [] })
  } catch (_err) {
    console.error('Unhandled exception in templates API:', err)
    return NextResponse.json(
      { error: 'Exception in templates API', message: err instanceof Error ? err.message : String(err) },
      { status: 500 }
    )
  }
}, ['partner', 'attorney', 'staff'])

// POST - Create a new template (partner only)
export const POST = withAuth(async (_req: NextRequest, user, supabase: SupabaseClient, context): Promise<Response> => {
  try {
    const body = await req.json()

    // Validate required fields
    const requiredFields = ['name', 'state', 'practice_area', 'category', 'document_type', 'content', 'variables']

    for (const field of requiredFields) {
      if (!body[field]) {
        return NextResponse.json(
          { error: `Missing required field: ${field}` },
          { status: 400 }
        )
      }
    }

    // Create template
    const { data, error } = await supabase
      .from('legal_templates')
      .insert({
        ...body,
        created_by: user.id
      })
      .select()
      .single()

    if (error) {
      console.error('Error creating template:', error)
      return NextResponse.json(
        { error: 'Failed to create template', details: error },
        { status: 500 }
      )
    }

    return NextResponse.json({ template: data })
  } catch (_err) {
    console.error('Unhandled exception in POST templates API:', err)
    return NextResponse.json(
      { error: 'Exception creating template', message: err instanceof Error ? err.message : String(err) },
      { status: 500 }
    )
  }
}, ['partner'])

// PUT - Update an existing template (partner, attorney)
export const PUT = withAuth(async (_req: NextRequest, user, supabase: SupabaseClient, context): Promise<Response> => {
  try {
    const body = await req.json()

    // Validate ID
    if (!body.id) {
      return NextResponse.json(
        { error: 'Missing template ID' },
        { status: 400 }
      )
    }

    // Update template
    const { data, error } = await supabase
      .from('legal_templates')
      .update({
        ...body,
        updated_by: user.id,
        updated_at: new Date().toISOString()
      })
      .eq('id', body.id)
      .select()
      .single()

    if (error) {
      console.error('Error updating template:', error)
      return NextResponse.json(
        { error: 'Failed to update template', details: error },
        { status: 500 }
      )
    }

    return NextResponse.json({ template: data })
  } catch (_err) {
    console.error('Unhandled exception in PUT templates API:', err)
    return NextResponse.json(
      { error: 'Exception updating template', message: err instanceof Error ? err.message : String(err) },
      { status: 500 }
    )
  }
}, ['partner', 'attorney'])

// DELETE - Delete a template (partner only)
export const DELETE = withAuth(async (_req: NextRequest, user, supabase: SupabaseClient, context): Promise<Response> => {
  try {
    const searchParams = new URL(req.url).searchParams
    const id = searchParams.get('id')

    if (!id) {
      return NextResponse.json(
        { error: 'Missing template ID' },
        { status: 400 }
      )
    }

    // Delete template
    const { error } = await supabase
      .from('legal_templates')
      .delete()
      .eq('id', id)

    if (error) {
      console.error('Error deleting template:', error)
      return NextResponse.json(
        { error: 'Failed to delete template', details: error },
        { status: 500 }
      )
    }

    return NextResponse.json({ success: true })
  } catch (_err) {
    console.error('Unhandled exception in DELETE templates API:', err)
    return NextResponse.json(
      { error: 'Exception deleting template', message: err instanceof Error ? err.message : String(err) },
      { status: 500 }
    )
  }
}, ['partner'])
