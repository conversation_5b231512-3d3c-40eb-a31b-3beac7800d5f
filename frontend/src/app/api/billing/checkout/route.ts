import { NextRequest, NextResponse } from 'next/server';
import { createServerClient } from '@supabase/ssr';
import { cookies } from 'next/headers';
import { Database } from '@/lib/supabase/database.types';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const priceId = searchParams.get('price_id');

    if (!priceId) {
      return NextResponse.json(
        { error: 'Missing price_id parameter' },
        { status: 400 }
      );
    }

    const cookieStore = await cookies();

    // Create Supabase client
    const supabase = createServerClient<Database>(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            return cookieStore.get(name)?.value;
          },
        },
      }
    );

    // Get the current session
    const { data: { session }, error: sessionError } = await supabase.auth.getSession();

    if (sessionError || !session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get user tenant ID
    const tenantId = session.user.user_metadata?.tenant_id;
    if (!tenantId) {
      return NextResponse.json({ error: 'No tenant ID found' }, { status: 400 });
    }

    // Check if Stripe is configured
    if (!process.env.STRIPE_SECRET_KEY) {
      console.warn('Stripe not configured, redirecting to sales email');

      // Fallback to email if Stripe is not configured
      const subject = encodeURIComponent('Subscription Upgrade Request');
      const body = encodeURIComponent(
        `Hi,\n\nI would like to upgrade my subscription.\n\nPrice ID: ${priceId}\nTenant ID: ${tenantId}\nUser: ${session.user.email}\n\nPlease contact me with next steps.\n\nThanks!`
      );

      const emailUrl = `mailto:<EMAIL>?subject=${subject}&body=${body}`;
      return NextResponse.redirect(emailUrl);
    }

    // TODO: Implement Stripe checkout session creation
    // This is a placeholder for when Stripe is fully configured

    // For now, redirect to sales email with upgrade request
    const subject = encodeURIComponent('Subscription Upgrade Request');
    const body = encodeURIComponent(
      `Hi,\n\nI would like to upgrade my subscription.\n\nPrice ID: ${priceId}\nTenant ID: ${tenantId}\nUser: ${session.user.email}\n\nPlease contact me with next steps.\n\nThanks!`
    );

    const emailUrl = `mailto:<EMAIL>?subject=${subject}&body=${body}`;
    return NextResponse.redirect(emailUrl);

    /*
    // Future Stripe implementation:

    const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
      apiVersion: '2023-10-16',
    });

    const checkoutSession = await stripe.checkout.sessions.create({
      mode: 'subscription',
      payment_method_types: ['card'],
      line_items: [
        {
          price: priceId,
          quantity: 1,
        },
      ],
      success_url: `${process.env.NEXT_PUBLIC_APP_URL}/subscription/success?session_id={CHECKOUT_SESSION_ID}`,
      cancel_url: `${process.env.NEXT_PUBLIC_APP_URL}/subscription/cancel`,
      customer_email: session.user.email,
      metadata: {
        tenant_id: tenantId,
        user_id: session.user.id,
      },
    });

    return NextResponse.redirect(checkoutSession.url!);
    */

  } catch (_error) {
    console.error('Error creating checkout session:', error);
    return NextResponse.json(
      { error: 'Failed to create checkout session' },
      { status: 500 }
    );
  }
}