import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server-new';
import { enhanceClientWithSchemas } from '@/lib/supabase/schema-client';
import { isAuthenticatedLegacy as isAuthenticated, hasRoleLegacy as hasRole, UserRole } from '@/lib/auth';
import { logSecurityEvent } from '@/lib/security/forensics';

/**
 * GET /api/admin/prompts
 * Get all prompts with their latest versions
 */
export async function GET(request: NextRequest) {
  try {
    // Create a Supabase client
    const supabase = createClient();

    // Get the user from the session
    const { data: { session }, error: sessionError } = await supabase.auth.getSession();

    if (sessionError || !session) {
      console.error('Session error:', sessionError);
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const user = session.user;

    // Check if user is authenticated
    if (!isAuthenticated(user)) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check if user is a superadmin
    if (!hasRole(user, [UserRole.Superadmin])) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    // Get query parameters
    const searchParams = request.nextUrl.searchParams;
    const promptId = searchParams.get('id');

    // If promptId is provided, get a specific prompt with all its versions
    if (promptId) {
      const { data: prompt, error: promptError } = await (supabase as any)
        .schema('security')
        .from('prompts')
        .select('*')
        .eq('id', promptId)
        .single();

      if (promptError) {
        console.error('Error fetching prompt:', promptError);
        return NextResponse.json({ error: 'Failed to fetch prompt' }, { status: 500 });
      }

      const { data: versions, error: versionsError } = await (supabase as any)
        .schema('security')
        .from('prompt_versions')
        .select('*')
        .eq('prompt_id', promptId)
        .order('version', { ascending: false });

      if (versionsError) {
        console.error('Error fetching prompt versions:', versionsError);
        return NextResponse.json({ error: 'Failed to fetch prompt versions' }, { status: 500 });
      }

      return NextResponse.json({ prompt, versions });
    }

    // Otherwise, get all prompts with their latest version
    const { data: prompts, error: promptsError } = await (supabase as any)
      .schema('security')
      .from('prompts')
      .select(`
        *,
        versions:prompt_versions(*)
      `)
      .order('updated_at', { ascending: false });

    if (promptsError) {
      console.error('Error fetching prompts:', promptsError);
      return NextResponse.json({ error: 'Failed to fetch prompts' }, { status: 500 });
    }

    // Process the prompts to include only the latest version
    const processedPrompts = (prompts || []).map((prompt: any) => {
      const versions = prompt.versions || [];
      const latestVersion = versions.length > 0
        ? versions.sort((a: any, b: any) => b.version - a.version)[0]
        : null;

      return {
        ...prompt,
        latestVersion
      };
    });

    return NextResponse.json({ prompts: processedPrompts });
  } catch (_error) {
    console.error('Error in prompts API:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

/**
 * POST /api/admin/prompts
 * Create a new prompt or a new version of an existing prompt
 */
export async function POST(request: NextRequest) {
  try {
    // Create a Supabase client
    const supabase = createClient();

    // Get the user from the session
    const { data: { session }, error: sessionError } = await supabase.auth.getSession();

    if (sessionError || !session) {
      console.error('Session error:', sessionError);
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const user = session.user;

    // Check if user is authenticated
    if (!isAuthenticated(user)) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check if user is a superadmin
    if (!hasRole(user, [UserRole.Superadmin])) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    // Parse request body
    const body = await request.json();
    const { key, description, tags, content, metadata } = body;

    // Validate required fields
    if (!key || !content) {
      return NextResponse.json({ error: 'Key and content are required' }, { status: 400 });
    }

    // Use the security.create_or_update_prompt function
    const { data: result, error: transactionError } = await (supabase as any).rpc('create_or_update_prompt', {
      p_key: key,
      p_description: description || null,
      p_tags: tags || null,
      p_content: content,
      p_metadata: metadata || null,
      p_created_by: user.email || user.id
    });

    if (transactionError) {
      console.error('Error creating/updating prompt:', transactionError);
      return NextResponse.json({ error: 'Failed to create/update prompt' }, { status: 500 });
    }

    // Log the security event
    await logSecurityEvent(supabase, 'prompt.created', {
      promptKey: key,
      userId: user.id,
      userEmail: user.email
    });

    return NextResponse.json({ success: true, data: result });
  } catch (_error) {
    console.error('Error in prompts API:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

/**
 * PATCH /api/admin/prompts
 * Update an existing prompt (not its content, just metadata)
 */
export async function PATCH(request: NextRequest) {
  try {
    // Create a Supabase client
    const supabase = createClient();

    // Get the user from the session
    const { data: { session }, error: sessionError } = await supabase.auth.getSession();

    if (sessionError || !session) {
      console.error('Session error:', sessionError);
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const user = session.user;

    // Check if user is authenticated
    if (!isAuthenticated(user)) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check if user is a superadmin
    if (!hasRole(user, [UserRole.Superadmin])) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    // Parse request body
    const body = await request.json();
    const { id, key, description, tags, is_active } = body;

    // Validate required fields
    if (!id) {
      return NextResponse.json({ error: 'Prompt ID is required' }, { status: 400 });
    }

    // Update the prompt
    const updateData: any = {};
    if (key !== undefined) updateData.key = key;
    if (description !== undefined) updateData.description = description;
    if (tags !== undefined) updateData.tags = tags;
    if (is_active !== undefined) updateData.is_active = is_active;
    updateData.updated_at = new Date().toISOString();

    const { data, error } = await (supabase as any)
      .schema('security')
      .from('prompts')
      .update(updateData)
      .eq('id', id)
      .select()
      .single();

    if (error) {
      console.error('Error updating prompt:', error);
      return NextResponse.json({ error: 'Failed to update prompt' }, { status: 500 });
    }

    // Log the security event
    await logSecurityEvent(supabase, 'prompt.updated', {
      promptId: id,
      userId: user.id,
      userEmail: user.email
    });

    return NextResponse.json({ success: true, data });
  } catch (_error) {
    console.error('Error in prompts API:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

/**
 * DELETE /api/admin/prompts
 * Delete a prompt
 */
export async function DELETE(request: NextRequest) {
  try {
    // Create a Supabase client
    const supabase = createClient();

    // Get the user from the session
    const { data: { session }, error: sessionError } = await supabase.auth.getSession();

    if (sessionError || !session) {
      console.error('Session error:', sessionError);
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const user = session.user;

    // Check if user is authenticated
    if (!isAuthenticated(user)) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check if user is a superadmin
    if (!hasRole(user, [UserRole.Superadmin])) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    // Get prompt ID from query parameters
    const url = new URL(request.url);
    const id = url.searchParams.get('id');

    if (!id) {
      return NextResponse.json({ error: 'Prompt ID is required' }, { status: 400 });
    }

    // Delete the prompt (cascade will delete versions)
    const { error } = await (supabase as any)
      .schema('security')
      .from('prompts')
      .delete()
      .eq('id', id);

    if (error) {
      console.error('Error deleting prompt:', error);
      return NextResponse.json({ error: 'Failed to delete prompt' }, { status: 500 });
    }

    // Log the security event
    await logSecurityEvent(supabase, 'prompt.deleted', {
      promptId: id,
      userId: user.id,
      userEmail: user.email
    });

    return NextResponse.json({ success: true });
  } catch (_error) {
    console.error('Error in prompts API:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}