// @ts-nocheck - API route type issues
import { NextRequest, NextResponse } from 'next/server';
import { Database } from '@/lib/supabase/database.types';
import { createClient, isAuthenticatedLegacy as isAuthenticated, hasRoleLegacy as hasRole } from '@/lib/auth/server-exports';
import { enhanceClientWithSchemas } from '@/lib/supabase/schema-client';

/**
 * GET /api/admin/usage
 * Get usage data for tenants
 */
export async function GET(request: NextRequest) {
  try {
    // Create a Supabase client
    const supabase = createClient();
    // Use the enhanced client with schema support
    const enhancedClient = enhanceClientWithSchemas(supabase);

    // Get the user from the session
    const { data: { session }, error: sessionError } = await supabase.auth.getSession();

    if (sessionError || !session) {
      console.error('Session error:', sessionError);
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const user = session.user;

    // Check if user is authenticated
    if (!isAuthenticated(user)) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check if user is a superadmin
    if (!hasRole(user, ['superadmin'])) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    // Get query parameters
    const searchParams = request.nextUrl.searchParams;
    const tenantId = searchParams.get('tenantId');
    const usageType = searchParams.get('usageType');
    const startDate = searchParams.get('startDate');
    const endDate = searchParams.get('endDate');
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');

    // Calculate offset
    const offset = (page - 1) * limit;

    if (!tenantId) {
      // Get aggregated usage across all tenants
      // Use RPC function instead of direct query with group by
      const { data, count, error } = await supabase
        .rpc('get_resource_usage_summary' as any, {
          p_usage_type: usageType || null,
          p_start_date: startDate || null,
          p_end_date: endDate || null,
          p_limit: limit,
          p_offset: offset
        });

      // All parameters are passed to the RPC function

      if (error) {
        console.error('Error fetching aggregated usage:', error);
        return NextResponse.json({ error: 'Failed to fetch usage data' }, { status: 500 });
      }

      // Get tenant names
      const tenantIds = data?.map((item: any) => item.tenant_id) || [];

      let tenantNames: Record<string, string> = {};

      if (tenantIds.length > 0) {
        const { data: firms, error: firmsError } = await enhancedClient.tenants
          .from('firms')
          .select('tenant_id, name')
          .in('tenant_id', tenantIds);

        if (firmsError) {
          console.error('Error fetching firm names:', firmsError);
        } else {
          tenantNames = Object.fromEntries(firms.map((firm: any) => [firm.tenant_id, firm.name]));
        }
      }

      return NextResponse.json({
        usage: data?.map((item: any) => ({
          ...item,
          tenantName: tenantNames[item.tenant_id] || 'Unknown'
        })),
        pagination: {
          total: count,
          page,
          limit,
          totalPages: Math.ceil((count || 0) / limit)
        }
      });
    } else {
      // Get usage for a specific tenant using RPC
      const { data, count, error } = await supabase
        .rpc('get_tenant_resource_usage' as any, {
          p_tenant_id: tenantId,
          p_usage_type: usageType || null,
          p_start_date: startDate || null,
          p_end_date: endDate || null,
          p_limit: limit,
          p_offset: offset
        });

      // All parameters are passed to the RPC function

      if (error) {
        console.error('Error fetching tenant usage:', error);
        return NextResponse.json({ error: 'Failed to fetch usage data' }, { status: 500 });
      }

      // Get token usage summary if requested
      let tokenSummary = null;
      if (usageType && usageType.startsWith('ai_tokens')) {
        const { data: summary, error: summaryError } = await supabase
          .rpc('get_token_usage_summary', {
            p_tenant_id: tenantId
          });

        if (summaryError) {
          console.error('Error fetching token usage summary:', summaryError);
        } else {
          tokenSummary = summary;
        }
      }

      return NextResponse.json({
        usage: data,
        tokenSummary,
        pagination: {
          total: count,
          page,
          limit,
          totalPages: Math.ceil((count || 0) / limit)
        }
      });
    }
  } catch (_error) {
    console.error('Error in usage API:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

/**
 * POST /api/admin/usage
 * Track usage or perform usage-related operations
 */
export async function POST(request: NextRequest) {
  try {
    // Create a Supabase client
    const supabase = createClient();
    // Use the enhanced client with schema support
    const enhancedClient = enhanceClientWithSchemas(supabase);

    // Get the user from the session
    const { data: { session }, error: sessionError } = await supabase.auth.getSession();

    if (sessionError || !session) {
      console.error('Session error:', sessionError);
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const user = session.user;

    // Check if user is authenticated
    if (!isAuthenticated(user)) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check if user is a superadmin
    if (!hasRole(user, ['superadmin'])) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }
    const body = await request.json();

    // Determine the action to take
    const { action, data } = body;

    if (action === 'trackUsage') {
      // Track resource usage
      const { data: usage, error } = await supabase
        .rpc('increment_resource_usage', {
          p_tenant_id: data.tenantId,
          p_usage_type: data.usageType,
          p_increment_by: data.usageCount,
          p_resource_size_bytes: data.resourceSizeBytes
        });

      if (error) {
        console.error('Error tracking usage:', error);
        return NextResponse.json({ error: 'Failed to track usage' }, { status: 500 });
      }

      return NextResponse.json({ usage });
    }
    else if (action === 'trackTokenUsage') {
      // Track token usage
      const { data: usage, error } = await supabase
        .rpc('track_token_usage', {
          p_tenant_id: data.tenantId,
          p_input_tokens: data.inputTokens,
          p_output_tokens: data.outputTokens,
          p_model_name: data.modelName || 'default',
          p_feature_context: data.featureContext
        });

      if (error) {
        console.error('Error tracking token usage:', error);
        return NextResponse.json({ error: 'Failed to track token usage' }, { status: 500 });
      }

      return NextResponse.json({ usage });
    }
    else if (action === 'getUsageSummary') {
      // Get usage summary by type using RPC
      const { data: summary, error } = await supabase
        .rpc('get_usage_summary_by_type' as any, {
          p_tenant_id: data.tenantId
        });

      if (error) {
        console.error('Error getting usage summary:', error);
        return NextResponse.json({ error: 'Failed to get usage summary' }, { status: 500 });
      }

      return NextResponse.json({ summary });
    }

    return NextResponse.json({ error: 'Invalid action' }, { status: 400 });
  } catch (_error) {
    console.error('Error in usage API:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
