// @ts-nocheck - API route type issues
import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';
import { Database } from '@/lib/supabase/database.types';
import { UsageTrackingService } from '@/lib/services/usage-tracking-service';
import { isTestRequest, getMockApiSession } from '@/lib/testing/api-test-utils';

// Create a Supabase client for the API route with fallback for build time
const supabase = createClient<Database>(
  process.env.NEXT_PUBLIC_SUPABASE_URL || 'https://placeholder.supabase.co',
  process.env.SUPABASE_SERVICE_KEY || 'placeholder-service-key'
);

/**
 * API route for getting tenant usage data
 */
export async function GET(req: NextRequest) {
  try {
    // Check if this is a Cypress test request
    let session;
    let tenantId;

    if (isTestRequest(req)) {
      // Use mock session for Cypress tests
      session = getMockApiSession();
      tenantId = session.user.app_metadata.tenant_id;
      console.log('Using mock session for Cypress test in API route');
    } else {
      // Get real user session
      const { data } = await supabase.auth.getSession();
      session = data.session;

      if (!session) {
        return NextResponse.json(
          { error: 'Unauthorized' },
          { status: 401 }
        );
      }

      // Get tenant ID from JWT claims
      tenantId = session.user.app_metadata.tenant_id;

      if (!tenantId) {
        return NextResponse.json(
          { error: 'Tenant ID not found in user claims' },
          { status: 403 }
        );
      }
    }

    // Get query parameters
    const url = new URL(req.url);
    const usageType = url.searchParams.get('type');
    const startDateParam = url.searchParams.get('startDate');
    const endDateParam = url.searchParams.get('endDate');

    // Set default dates if not provided
    const now = new Date();
    const startDate = startDateParam ? new Date(startDateParam) : new Date(now.getFullYear(), now.getMonth() - 1, 1);
    const endDate = endDateParam ? new Date(endDateParam) : new Date(now.getFullYear(), now.getMonth() + 1, 0);

    // Get usage data
    const usageTrackingService = new UsageTrackingService(supabase);

    if (usageType) {
      // Get usage for a specific type
      const usage = await usageTrackingService.getTenantUsage({
        tenantId,
        usageType,
        startDate,
        endDate,
      });

      return NextResponse.json({ usage });
    } else {
      // Get usage for all types
      const usageTypes = ['document_upload', 'document_processing', 'api_calls', 'storage_usage'];
      const usagePromises = usageTypes.map(type =>
        usageTrackingService.getTenantUsage({
          tenantId,
          usageType: type,
          startDate,
          endDate,
        })
      );

      const usageResults = await Promise.all(usagePromises);

      // Group by usage type
      const usage = usageTypes.reduce((acc, type, index) => {
        acc[type] = usageResults[index];
        return acc;
      }, {} as Record<string, any>);

      return NextResponse.json({ usage });
    }
  } catch (_error) {
    console.error('Error getting usage data:', error);
    return NextResponse.json(
      { error: 'Error getting usage data' },
      { status: 500 }
    );
  }
}

/**
 * API route for getting tenant quota information
 */
export async function POST(req: NextRequest) {
  try {
    // Check if this is a Cypress test request
    let session;
    let tenantId;

    if (isTestRequest(req)) {
      // Use mock session for Cypress tests
      session = getMockApiSession();
      tenantId = session.user.app_metadata.tenant_id;
      console.log('Using mock session for Cypress test in API route');
    } else {
      // Get real user session
      const { data } = await supabase.auth.getSession();
      session = data.session;

      if (!session) {
        return NextResponse.json(
          { error: 'Unauthorized' },
          { status: 401 }
        );
      }

      // Get tenant ID from JWT claims
      tenantId = session.user.app_metadata.tenant_id;

      if (!tenantId) {
        return NextResponse.json(
          { error: 'Tenant ID not found in user claims' },
          { status: 403 }
        );
      }
    }

    // Get request body
    const body = await req.json();
    const { usageType, incrementBy = 1, resourceSizeBytes } = body;

    if (!usageType) {
      return NextResponse.json(
        { error: 'No usage type provided' },
        { status: 400 }
      );
    }

    // Check quota limit
    const usageTrackingService = new UsageTrackingService(supabase);
    const quotaCheck = await usageTrackingService.checkQuotaLimit({
      tenantId,
      usageType,
      incrementBy,
      resourceSizeBytes,
    });

    return NextResponse.json({ quota: quotaCheck });
  } catch (_error) {
    console.error('Error checking quota:', error);
    return NextResponse.json(
      { error: 'Error checking quota' },
      { status: 500 }
    );
  }
}
