// @ts-nocheck - API route type issues
import { NextRequest, NextResponse } from 'next/server';
import { createServerClient, type CookieOptions } from '@supabase/ssr';
import { cookies } from 'next/headers';
import { Database } from '@/lib/supabase/database.types';

/**
 * API endpoint to check if a database function exists
 * GET /api/utils/function-exists?name=function_name
 */
export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams;
    const functionName = searchParams.get('name');

    if (!functionName) {
      return NextResponse.json({ error: 'Function name is required' }, { status: 400 });
    }

    const cookieStore = await cookies(); // Await the cookies() function call

    // Create Supabase client using ssr helper
    const supabase = createServerClient<Database>(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            return cookieStore.get(name)?.value;
          },
          set(name: string, value: string, options: CookieOptions) {
            try {
              cookieStore.set({ name, value, ...options });
            } catch (_error) {
              console.error('Failed to set cookie in function-exists route:', error);
            }
          },
          remove(name: string, options: CookieOptions) {
             try {
              cookieStore.delete({ name, ...options });
            } catch (_error) {
              console.error('Failed to remove cookie in function-exists route:', error);
            }
          }
        },
      }
    );

    // Check if the function exists by attempting to invoke a specific RPC function
    // Ensure 'function_exists' RPC is created in your Supabase SQL editor
    /* Example SQL for function_exists:
       CREATE OR REPLACE FUNCTION function_exists(p_function_name text)
       RETURNS boolean AS $$
       BEGIN
         RETURN EXISTS (
           SELECT 1
           FROM pg_catalog.pg_proc
           WHERE proname = p_function_name
         );
       END;
       $$ LANGUAGE plpgsql;
    */
    const { data, error } = await supabase
      .rpc('function_exists', { p_function_name: functionName });

    if (error) {
      console.error(`Error checking function ${functionName} via RPC:`, error);
       // Consider specific error codes if needed, otherwise treat any RPC error as failure
       return NextResponse.json({ error: 'Failed to check function existence via RPC', details: error.message }, { status: 500 });
    }

    // Assuming the RPC function returns a boolean indicating existence
    const exists = typeof data === 'boolean' ? data : false; // Handle potential non-boolean returns safely
    return NextResponse.json({ exists });

  } catch (err: any) {
    // Get functionName again for error logging, as it's out of scope from the try block
    const functionNameForError = request.nextUrl.searchParams.get('name') || 'unknown';
    console.error(`Unexpected error checking function ${functionNameForError}:`, err);
    return NextResponse.json({ error: 'Internal server error', details: err.message }, { status: 500 });
  }
}
