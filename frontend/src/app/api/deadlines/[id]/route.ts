// @ts-nocheck - API route type issues
// frontend/src/app/api/deadlines/[id]/route.ts
import { withAuth } from '@/lib/auth/server-exports';
import { NextRequest, NextResponse } from 'next/server';
import type { AuthUser } from '@/lib/auth/server-exports';
import type { SupabaseClient } from '@supabase/supabase-js';
import * as z from 'zod';
import { createServices } from '@/lib/services';
import { DeadlineSchema } from '@/lib/services/calendar-deadline-service';

// GET /api/deadlines/[id] - Get a specific calendar deadline by ID
export const GET = withAuth(async (
  req: NextRequest,
  user: AuthUser,
  supabase: SupabaseClient<any, "public", any>,
  context: Record<string, unknown>
): Promise<Response> => {
  try {
    if (!context || !context.params) {
      return NextResponse.json({ error: 'No parameters provided' }, { status: 400 });
    }

    // Get the params object
    const params = context.params as { id: string };
    const { id } = params;

    if (!id || !z.string().uuid().safeParse(id).success) {
      return NextResponse.json({ error: 'Invalid deadline ID format' }, { status: 400 });
    }

    // Create services with the authenticated user's tenant ID
    const services = createServices(supabase, user.tenantId);

    // Use the calendar deadline service to get the deadline with all its related data
    const deadline = await services.calendarDeadlines.getById(id);

    if (!deadline) {
      return NextResponse.json({ error: 'Calendar deadline not found' }, { status: 404 });
    }

    return NextResponse.json(deadline);
  } catch (_error) {
    console.error('Error in GET /api/deadlines/[id]:', error);
    return NextResponse.json({ error: 'Internal server error', details: String(error) }, { status: 500 });
  }
});

// PUT /api/deadlines/[id] - Update a calendar deadline
export const PUT = withAuth(async (
  req: NextRequest,
  user: AuthUser,
  supabase: SupabaseClient<any, "public", any>,
  context: Record<string, unknown>
): Promise<Response> => {
  try {
    if (!context || !context.params) {
      return NextResponse.json({ error: 'No parameters provided' }, { status: 400 });
    }

    // Get the params object
    const params = context.params as { id: string };
    const { id } = params;

    // Validate UUID format
    if (!z.string().uuid().safeParse(id).success) {
      return NextResponse.json({ error: 'Invalid deadline ID format' }, { status: 400 });
    }

    // Get request body
    const data = await req.json();

    // Validate the update data
    const validationResult = DeadlineSchema.partial().safeParse(data);
    if (!validationResult.success) {
      return NextResponse.json({
        error: 'Validation failed',
        details: validationResult.error.errors
      }, { status: 400 });
    }

    // Create services with the authenticated user's tenant ID
    const services = createServices(supabase, user.tenantId);

    // Update the deadline using the calendar deadline service
    const updatedDeadline = await services.calendarDeadlines.update(id, user.id, validationResult.data);

    if (!updatedDeadline) {
      return NextResponse.json({ error: 'Calendar deadline not found or update failed' }, { status: 404 });
    }

    return NextResponse.json(updatedDeadline);
  } catch (_error) {
    console.error('Error in PUT /api/deadlines/[id]:', error);
    if (error instanceof z.ZodError) {
      return NextResponse.json({ error: 'Validation failed', details: error.errors }, { status: 400 });
    }
    return NextResponse.json({ error: 'Internal server error', details: String(error) }, { status: 500 });
  }
});

// PATCH /api/deadlines/[id]/complete - Mark a deadline as completed
export const PATCH = withAuth(async (
  req: NextRequest,
  user: AuthUser,
  supabase: SupabaseClient<any, "public", any>,
  context: Record<string, unknown>
): Promise<Response> => {
  try {
    if (!context || !context.params) {
      return NextResponse.json({ error: 'No parameters provided' }, { status: 400 });
    }

    // Get the params object
    const params = context.params as { id: string };
    const { id } = params;

    if (!id || !z.string().uuid().safeParse(id).success) {
      return NextResponse.json({ error: 'Invalid deadline ID format' }, { status: 400 });
    }

    // Create services with the authenticated user's tenant ID
    const services = createServices(supabase, user.tenantId);

    // Mark the deadline as completed
    const completedDeadline = await services.calendarDeadlines.markAsCompleted(id, user.id);

    if (!completedDeadline) {
      return NextResponse.json({ error: 'Calendar deadline not found or completion failed' }, { status: 404 });
    }

    return NextResponse.json(completedDeadline);
  } catch (_error) {
    console.error('Error in PATCH /api/deadlines/[id]/complete:', error);
    return NextResponse.json({ error: 'Internal server error', details: String(error) }, { status: 500 });
  }
});

// DELETE /api/deadlines/[id] - Delete a calendar deadline
export const DELETE = withAuth(async (
  req: NextRequest,
  user: AuthUser,
  supabase: SupabaseClient<any, "public", any>,
  context: Record<string, unknown>
): Promise<Response> => {
  try {
    if (!context || !context.params) {
      return NextResponse.json({ error: 'No parameters provided' }, { status: 400 });
    }

    // Get the params object
    const params = context.params as { id: string };
    const { id } = params;

    if (!id || !z.string().uuid().safeParse(id).success) {
      return NextResponse.json({ error: 'Invalid deadline ID format' }, { status: 400 });
    }

    // Create services with the authenticated user's tenant ID
    const services = createServices(supabase, user.tenantId);

    // Delete the deadline
    const deleted = await services.calendarDeadlines.delete(id);

    if (!deleted) {
      return NextResponse.json({ error: 'Calendar deadline not found or deletion failed' }, { status: 404 });
    }

    return NextResponse.json({ success: true });
  } catch (_error) {
    console.error('Error in DELETE /api/deadlines/[id]:', error);
    return NextResponse.json({ error: 'Internal server error', details: String(error) }, { status: 500 });
  }
});
