import { cookies } from 'next/headers';
import { NextRequest, NextResponse } from 'next/server';
import { createServerClient, type CookieOptions } from '@supabase/ssr';
import { Database } from '@/lib/supabase/database.types';
import { z } from 'zod';
import { UserRole } from '@/lib/types/auth';

const registerSchema = z.object({
  email: z.string().email({ message: 'Invalid email address' }),
  password: z.string().min(8, { message: 'Password must be at least 8 characters long' }),
  first_name: z.string().optional(),
  last_name: z.string().optional(),
  captchaToken: z.string().optional(),
});

export async function POST(request: NextRequest): Promise<Response> {
  const requestUrl = new URL(request.url);
  const origin = request.headers.get('origin') || process.env.NEXT_PUBLIC_APP_URL;
  const cookieStore = await cookies();

  const supabase = createServerClient<Database>(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        get(name: string) {
          return cookieStore.get(name)?.value;
        },
        set(name: string, value: string, options: CookieOptions) {
          try {
            cookieStore.set({ name, value, ...options });
          } catch (_error) {
            console.error('Failed to set cookie in register route', { name, error });
          }
        },
        remove(name: string, options: CookieOptions) {
          try {
            cookieStore.set({ name, value: '', ...options });
          } catch (_error) {
            console.error('Failed to remove cookie in register route', { name, error });
          }
        },
      },
    }
  );

  try {
    const body = await request.json();
    const validatedData = registerSchema.parse(body);

    const { error } = await supabase.auth.signUp({
      email: validatedData.email,
      password: validatedData.password,
      options: {
        emailRedirectTo: `${origin}/auth/callback`,
        data: {
          first_name: validatedData.first_name,
          last_name: validatedData.last_name,
          role: UserRole.Partner,
          // tenant_id: ???
        },
      },
    });

    if (error) {
      console.error('Supabase SignUp Error:', error);
      let errorMessage = 'Registration failed. Please try again.';
      if (error.message.includes('User already registered')) {
        errorMessage = 'An account with this email already exists.';
        return NextResponse.json({ error: errorMessage }, { status: 409 });
      }
      return NextResponse.json({ error: error.message || errorMessage }, { status: 400 });
    }

    return NextResponse.json({
      message: 'Registration successful. Please check your email to confirm.',
    }, { status: 201 });

  } catch (error: any) {
    if (error instanceof z.ZodError) {
      console.error('Registration validation error:', error.errors);
      return NextResponse.json({ error: 'Invalid input data', details: error.flatten().fieldErrors }, { status: 400 });
    }
    console.error('Unexpected registration error:', error);
    return NextResponse.json({ error: 'An unexpected error occurred during registration.' }, { status: 500 });
  }
}
