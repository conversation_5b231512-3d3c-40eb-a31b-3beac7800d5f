// @ts-nocheck - API route type issues
/**
 * Auth Security API Endpoints
 *
 * Provides endpoints for token security operations:
 * - GET: Get security status for current user
 * - POST: Perform security operations (revoke tokens, etc.)
 */

import { NextRequest, NextResponse } from 'next/server';
import { withAuth, AuthUser } from '@/lib/auth/server-exports';
import { SupabaseClient } from '@supabase/supabase-js';
import {
  revokeToken,
  revokeAllUserTokens,
  parseJwtToken
} from '@/lib/security/token-security';
import { logSecurityEvent } from '@/lib/security/forensics';

/**
 * Get security status for current user
 * Returns information about active sessions and security settings
 */
export const GET = withAuth(
  async (_req: NextRequest, user: AuthUser, supabase: SupabaseClient) => {
    try {
      // Get active sessions for the user
      const { data: sessions, error: sessionsError } = await supabase
        .from('security.token_devices')
        .select('*')
        .eq('user_id', user.id);

      if (sessionsError) {
        console.error('Error fetching sessions:', sessionsError);
        return NextResponse.json(
          { error: 'Failed to fetch security information' },
          { status: 500 }
        );
      }

      // Get device fingerprints for the user
      const { data: devices, error: devicesError } = await supabase
        .from('security.device_fingerprints')
        .select('*')
        .eq('user_id', user.id);

      if (devicesError) {
        console.error('Error fetching devices:', devicesError);
        return NextResponse.json(
          { error: 'Failed to fetch device information' },
          { status: 500 }
        );
      }

      // Get current token info
      const accessToken = req.headers.get('authorization')?.split(' ')[1];
      const tokenInfo = accessToken ? parseJwtToken(accessToken) : null;

      // Log security status check
      await logSecurityEvent('security.status_check', {
        userId: user.id,
        sessionCount: sessions?.length || 0,
        deviceCount: devices?.length || 0
      });

      return NextResponse.json({
        user: {
          id: user.id,
          email: user.email,
          role: user.role
        },
        currentSession: {
          tokenId: tokenInfo?.jti || null,
          expiresAt: tokenInfo?.exp ? new Date(tokenInfo.exp * 1000).toISOString() : null,
          issuedAt: tokenInfo?.iat ? new Date(tokenInfo.iat * 1000).toISOString() : null
        },
        sessions: sessions || [],
        devices: devices || []
      });
    } catch (_error) {
      console.error('Security status error:', error);
      return NextResponse.json(
        { error: 'Failed to fetch security information' },
        { status: 500 }
      );
    }
  },
  // Allow all authenticated users to access their security status
  ['partner', 'attorney', 'paralegal', 'staff', 'client']
);

/**
 * Perform security operations
 * - Revoke specific token
 * - Revoke all tokens except current
 * - Revoke all tokens including current (force logout everywhere)
 */
export const POST = withAuth(
  async (_req: NextRequest, user: AuthUser, supabase: SupabaseClient) => {
    try {
      const { operation, tokenId, reason } = await req.json();

      // Validate operation
      if (!operation) {
        return NextResponse.json(
          { error: 'Missing operation parameter' },
          { status: 400 }
        );
      }

      // Handle different operations
      switch (operation) {
        case 'revoke_token':
          // Validate parameters
          if (!tokenId) {
            return NextResponse.json(
              { error: 'Missing tokenId parameter' },
              { status: 400 }
            );
          }

          // Revoke the specific token
          const success = await revokeToken(
            tokenId,
            user.id,
            reason || 'User initiated revocation'
          );

          if (!success) {
            return NextResponse.json(
              { error: 'Failed to revoke token' },
              { status: 500 }
            );
          }

          // Log token revocation
          await logSecurityEvent('security.token_revoked', {
            userId: user.id,
            tokenId,
            reason: reason || 'User initiated'
          });

          return NextResponse.json({
            success: true,
            message: 'Token revoked successfully'
          });

        case 'revoke_all_except_current':
          // Revoke all tokens except the current one
          const revokeAllExceptSuccess = await revokeAllUserTokens(
            user.id,
            reason || 'User initiated revocation of all other sessions',
            true // Except current token
          );

          if (!revokeAllExceptSuccess) {
            return NextResponse.json(
              { error: 'Failed to revoke tokens' },
              { status: 500 }
            );
          }

          // Log mass token revocation
          await logSecurityEvent('security.all_tokens_revoked_except_current', {
            userId: user.id,
            reason: reason || 'User initiated'
          });

          return NextResponse.json({
            success: true,
            message: 'All other sessions revoked successfully'
          });

        case 'revoke_all':
          // Revoke all tokens including the current one (force logout everywhere)
          const revokeAllSuccess = await revokeAllUserTokens(
            user.id,
            reason || 'User initiated revocation of all sessions',
            false // Include current token
          );

          if (!revokeAllSuccess) {
            return NextResponse.json(
              { error: 'Failed to revoke all tokens' },
              { status: 500 }
            );
          }

          // Log mass token revocation including current
          await logSecurityEvent('security.all_tokens_revoked', {
            userId: user.id,
            reason: reason || 'User initiated'
          });

          return NextResponse.json({
            success: true,
            message: 'All sessions revoked successfully',
            forceLogout: true
          });

        default:
          return NextResponse.json(
            { error: `Unknown operation: ${operation}` },
            { status: 400 }
          );
      }
    } catch (_error) {
      console.error('Security operation error:', error);
      return NextResponse.json(
        { error: 'Failed to perform security operation' },
        { status: 500 }
      );
    }
  },
  // Allow all authenticated users to manage their own security
  ['partner', 'attorney', 'paralegal', 'staff', 'client']
);
