/**
 * Document API route
 *
 * This route demonstrates the service mesh pattern:
 * - Client-to-Next.js: Uses Supabase Auth tokens (user authentication)
 * - Next.js-to-Python: Uses service token (service authentication)
 *
 * This pattern allows us to:
 * 1. Authenticate the user with <PERSON><PERSON><PERSON> Auth on the frontend
 * 2. Pass user context through to the backend even when using service authentication
 * 3. Keep service tokens secure on the server side
 */

import { NextRequest, NextResponse } from 'next/server';
import { createRouteHandlerClient } from '@/lib/supabase/server';
import { cookies } from 'next/headers';

// Backend API URL - using environment variables as specified in user rules
const BACKEND_API_URL = process.env.BACKEND_API_URL || 'http://localhost:8000';
const API_SERVICE_TOKEN = process.env.API_SERVICE_TOKEN;
const SUPABASE_URL = process.env.NEXT_PUBLIC_SUPABASE_URL;

/**
 * GET handler for document endpoint
 *
 * This uses the service token to communicate with the Python backend
 * while preserving user context and enforcing access control.
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Get document ID from route params
    const documentId = params.id;

    // Create a Supabase client for auth
    const supabase = createRouteHandlerClient();

    // Get the user's session
    const { data: { session } } = await supabase.auth.getSession();

    // If no authenticated user, return 401
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get user data (we could store this in the JWT claims as well)
    const userId = session.user.id;
    const userEmail = session.user.email;

    // Get user metadata from Supabase (role, tenant_id, etc.)
    const { data: userMetadata } = await supabase
      .from('users')
      .select('*')
      .eq('auth_id', userId)
      .single();

    // Check if the user profile exists
    if (!userMetadata) {
      console.error('User profile not found for auth user:', userId);
      return NextResponse.json(
        { error: 'User profile not found' },
        { status: 401 }
      );
    }

    // Log the request for audit purposes
    console.log(`Document request: ${documentId} by user ${userEmail} (${userMetadata.role})`);

    // Make request to Python backend using service token
    // This demonstrates the service mesh pattern
    const response = await fetch(`${BACKEND_API_URL}/api/service/documents/${documentId}`, {
      headers: {
        'Authorization': `Bearer ${API_SERVICE_TOKEN}`,
        'Content-Type': 'application/json',
        // We could also pass user context as headers if needed
        'X-User-ID': userId,
        'X-User-Email': userEmail || '',
        'X-User-Role': userMetadata.role || '',
        'X-Tenant-ID': userMetadata.tenant_id || ''
      }
    });

    // Get response data
    const data = await response.json();

    // Handle 404 from backend
    if (response.status === 404) {
      return NextResponse.json(
        { error: 'Document not found' },
        { status: 404 }
      );
    }

    // Handle other errors from backend
    if (!response.ok) {
      return NextResponse.json(
        { error: data.detail || 'Error fetching document' },
        { status: response.status }
      );
    }

    // Check if user has access to the document
    // Even though we're using service token auth with the Python backend,
    // we still need to enforce tenant isolation here
    if (data.tenant_id !== userMetadata.tenant_id) {
      console.warn(`Unauthorized access attempt: User ${userId} attempted to access document ${documentId} from tenant ${data.tenant_id}`);
      return NextResponse.json(
        { error: 'You do not have permission to access this document' },
        { status: 403 }
      );
    }

    // Return the document data
    return NextResponse.json(data);

  } catch (_error) {
    console.error('Error in document API route:', error);
    return NextResponse.json(
      { error: 'Failed to fetch document' },
      { status: 500 }
    );
  }
}

/**
 * PUT handler for updating documents
 *
 * Similar pattern to GET but for updates
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Get document ID from route params
    const documentId = params.id;

    // Parse request body
    const requestData = await request.json();

    // Create a Supabase client for auth
    const supabase = createRouteHandlerClient();

    // Get the user's session
    const { data: { session } } = await supabase.auth.getSession();

    // If no authenticated user, return 401
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get user metadata from Supabase
    const { data: userMetadata } = await supabase
      .from('users')
      .select('*')
      .eq('auth_id', session.user.id)
      .single();

    // Check if user has appropriate role for this operation
    if (!userMetadata || !['partner', 'attorney', 'paralegal'].includes(userMetadata.role)) {
      return NextResponse.json(
        { error: 'You do not have permission to update documents' },
        { status: 403 }
      );
    }

    // Make request to Python backend using service token
    const response = await fetch(`${BACKEND_API_URL}/api/service/documents/${documentId}`, {
      method: 'PUT',
      headers: {
        'Authorization': `Bearer ${API_SERVICE_TOKEN}`,
        'Content-Type': 'application/json',
        'X-User-ID': session.user.id,
        'X-Tenant-ID': userMetadata.tenant_id
      },
      body: JSON.stringify({
        ...requestData,
        // Force tenant_id to match the authenticated user's tenant
        tenant_id: userMetadata.tenant_id,
        // Record who made the update
        updated_by: session.user.id
      })
    });

    // Get response data
    const data = await response.json();

    // Handle errors from backend
    if (!response.ok) {
      return NextResponse.json(
        { error: data.detail || 'Error updating document' },
        { status: response.status }
      );
    }

    // Return the updated document
    return NextResponse.json(data);

  } catch (_error) {
    console.error('Error in document update API route:', error);
    return NextResponse.json(
      { error: 'Failed to update document' },
      { status: 500 }
    );
  }
}
