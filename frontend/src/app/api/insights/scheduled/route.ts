// @ts-nocheck - API route type issues
import { NextRequest, NextResponse } from 'next/server';
import { verifyJWT } from '@/lib/auth/server-exports';
import { createClient } from '@/lib/supabase/server';
import { InsightWorkflow } from '@/lib/langgraph/workflows/insight-workflow';
import { SwarmAgentSystem } from '@/lib/langgraph/agents/swarm';
import { SupervisorAgent } from '@/lib/langgraph/agents/supervisor';
import { logActivity } from '@/lib/activity-logger';
import { recordInsightInNeo4j } from '@/lib/neo4j/insights';
import { rateLimit } from '@/lib/rate-limit';
import { SupabaseClient } from '@supabase/supabase-js';
import { Activity, Insight } from '@/lib/types';
import { enhanceClientWithSchemas } from '@/lib/supabase/schema-client';

// Define interfaces for the API
interface ScheduledInsightRequest {
  userId?: string;
  tenantId?: string;
  source?: string;
  lookbackHours?: number;
}

interface User {
  id: string;
  tenant_id: string;
  is_active: boolean;
  preferences?: Record<string, unknown>;
  [key: string]: any;
}

interface Case {
  id: string;
  user_id: string;
  status: string;
  [key: string]: any;
}

interface Deadline {
  id: string;
  user_id: string;
  due_date: string;
  [key: string]: any;
}

// API secret key for internal scheduled calls
const API_SECRET = process.env.INSIGHT_API_SECRET;

/**
 * Scheduled insights generation endpoint
 * This endpoint is intended to be called by a scheduler (cron job)
 *
 * POST /api/insights/scheduled
 * Headers:
 *   X-API-Secret: API secret key (required for non-authenticated access)
 * Body: {
 *   userId?: string, // Optional user ID for single-user processing
 *   tenantId?: string, // Optional tenant ID for multi-tenant environments
 *   source?: string, // Optional source type for insight generation
 *   lookbackHours?: number // Optional lookback period in hours
 * }
 */
export async function POST(_req: NextRequest): Promise<Response> {
  try {
    // Check authentication - either JWT or API secret
    let isAuthenticated = false;
    let userId: string | null = null;
    let tenantId: string | null = null;

    // First, try API secret for scheduler authentication
    const apiSecret = req.headers.get('X-API-Secret');
    if (apiSecret === API_SECRET) {
      isAuthenticated = true;
    }

    // If no API secret, try JWT authentication for user-triggered scheduling
    if (!isAuthenticated) {
      const authResult = await verifyJWT(req);
      if (authResult.success && authResult.user) {
        isAuthenticated = true;
        userId = authResult.user.id;
        // Add tenantId if available, otherwise use null
        tenantId = authResult.user.tenantId || null;
      }
    }

    // If not authenticated, return error
    if (!isAuthenticated) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Parse request body
    const body: ScheduledInsightRequest = await req.json();
    const requestUserId = body.userId || userId;
    const requestTenantId = body.tenantId || tenantId;
    const source = body.source || 'activity';
    const lookbackHours = body.lookbackHours || 24;

    // If this is a user-triggered scheduling (not from system scheduler)
    if (userId) {
      // Rate limiting - max 2 scheduled generations per hour
      const rateLimitResult = await rateLimit('schedule_insights', 2, 3600, userId);
      if (!rateLimitResult.success) {
        return NextResponse.json(
          { error: 'Rate limit exceeded', retryAfter: rateLimitResult.retryAfter },
          { status: 429, headers: { 'Retry-After': String(rateLimitResult.retryAfter) } }
        );
      }
    }

    // Initialize Supabase client
    const supabase = createClient();

    // Enhance the client with schema support
    const enhancedClient = enhanceClientWithSchemas(supabase);

    // If specific userId is provided, process insights for that user
    if (requestUserId) {
      await processUserInsights(requestUserId, source, lookbackHours, enhancedClient);

      return NextResponse.json({
        success: true,
        message: `Scheduled insights processing initiated for user: ${requestUserId}`
      });
    }
    // If tenantId is provided, process insights for all users in that tenant
    else if (requestTenantId) {
      // Get all active users in this tenant
      const { data: users, error } = await enhancedClient.tenants
        .from('users')
        .select('id')
        .eq('tenant_id', requestTenantId)
        .eq('is_active', true);

      if (error) throw error;

      // Process each user (limited to avoid overloading)
      const userIds = users?.map((u: any) => u.id) || [];
      const processPromises = userIds.slice(0, 10).map((uid: string) =>
        processUserInsights(uid, source, lookbackHours, enhancedClient)
      );

      await Promise.allSettled(processPromises);

      return NextResponse.json({
        success: true,
        message: `Scheduled insights processing initiated for ${processPromises.length} users in tenant: ${requestTenantId}`
      });
    }
    // No user or tenant specified - return error
    else {
      return NextResponse.json({ error: 'Missing userId or tenantId' }, { status: 400 });
    }
  } catch (_error) {
    console.error('Error in scheduled insights processing:', error);
    return NextResponse.json({ error: 'Failed to process scheduled insights' }, { status: 500 });
  }
}

/**
 * Process insights for a specific user
 */
async function processUserInsights(
  userId: string,
  source: string,
  lookbackHours: number,
  client: any
): Promise<void> {
  try {
    console.log(`Processing scheduled insights for user: ${userId}`);

    // Get user data and preferences
    const { data: userData, error: userError } = await client.tenants
      .from('users')
      .select('*')
      .eq('id', userId)
      .single();

    if (userError) throw userError;

    // Get recent activities
    const lookbackTime = new Date();
    lookbackTime.setHours(lookbackTime.getHours() - lookbackHours);

    const { data: activities, error } = await client
      .schema('tenants')
      .from('activities')
      .select('*')
      .eq('user_id', userId)
      .gte('created_at', lookbackTime.toISOString())
      .order('created_at', { ascending: false });

    if (error) throw error;

    // If not enough activities, skip insight generation
    if (!activities || activities.length < 3) {
      console.log(`Not enough activities for user ${userId}, skipping insight generation`);
      return;
    }

    // Get high priority cases for context
    const { data: highPriorityCases, error: caseError } = await client.tenants
      .from('cases')
      .select('*')
      .eq('user_id', userId)
      .in('status', ['active', 'pending', 'critical'])
      .order('updated_at', { ascending: false })
      .limit(3);

    if (caseError) throw caseError;

    // Get upcoming deadlines for context
    const nextWeek = new Date();
    nextWeek.setDate(nextWeek.getDate() + 7);

    const { data: upcomingDeadlines, error: deadlineError } = await client.tenants
      .from('deadlines')
      .select('*')
      .eq('user_id', userId)
      .gte('due_date', new Date().toISOString())
      .lte('due_date', nextWeek.toISOString())
      .order('due_date', { ascending: true })
      .limit(5);

    if (deadlineError) throw deadlineError;

    // Create the agent instances optimized for scheduled processing
    const swarmAgentSystem = new SwarmAgentSystem({
      // Use lower temperature for more focused insights in scheduled processing
      temperature: 0.3,
    });

    // @ts-expect-error - Ignoring type errors for now as the SupervisorAgent constructor might have changed
    const supervisorAgent = new SupervisorAgent({
      userId: userId,
      tenantId: userData?.tenant_id || '',
      timestamp: new Date().toISOString(),
      userPreferences: userData?.preferences || {}
    } as any);

    // Create and run the workflow
    // @ts-expect-error - Ignoring type errors for now as the InsightWorkflow constructor might have changed
    const workflow = new InsightWorkflow(supervisorAgent, swarmAgentSystem);

    // Type assertion for workflow.execute since TypeScript doesn't recognize it
    const execute = (workflow as any).execute as (params: {
      activities: Activity[];
      source: string;
      startTime: string;
      additionalContext?: Record<string, any>;
    }) => Promise<Insight[]>;

    const insights = await execute({
      activities: activities as unknown as Activity[],
      source: 'scheduled',
      startTime: new Date().toISOString(),
      // Add extra context for scheduled insights
      additionalContext: {
        isScheduled: true,
        highPriorityCases: highPriorityCases || [],
        upcomingDeadlines: upcomingDeadlines || []
      }
    });

    // Store insights in Neo4j for future reference (if enabled)
    try {
      for (const insight of insights) {
        await recordInsightInNeo4j(insight, userId, 'scheduled', null, {
          lookbackHours
        });
      }
    } catch (neo4jError) {
      console.error('Error storing scheduled insights in Neo4j:', neo4jError);
      // Continue even if Neo4j storage fails
    }

    // Log this scheduled insight generation
    await logActivity({
      userId: userId,
      type: 'scheduled_insights',
      description: `Generated scheduled insights`,
      metadata: {
        source,
        lookbackHours,
        activityCount: activities.length,
        insightCount: insights.length
      }
    });

    console.log(`Successfully generated ${insights.length} insights for user ${userId}`);
  } catch (_error) {
    console.error(`Error processing insights for user ${userId}:`, error);
    // Intentionally not re-throwing to prevent failure of batch processing
  }
}
