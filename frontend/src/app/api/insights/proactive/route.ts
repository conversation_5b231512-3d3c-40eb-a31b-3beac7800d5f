// @ts-nocheck - API route type issues
import { NextRequest, NextResponse } from 'next/server';
import { verifyJWT } from '@/lib/auth/server-exports';
import { createClient } from '@/lib/supabase/server';
import { InsightWorkflow } from '@/lib/langgraph/workflows/insight-workflow';
import { SwarmAgentSystem } from '@/lib/langgraph/agents/swarm';
import { SupervisorAgent } from '@/lib/langgraph/agents/supervisor';
import { EntityType, InsightSource } from '@/lib/langgraph/types/insight-graph';
import { logActivity } from '@/lib/activity-logger';
import { recordInsightInNeo4j } from '@/lib/neo4j/insights';
import { getUserLastActive, setUserLastActive } from '@/lib/redis/user-activity';
import { Activity, Insight } from '@/lib/types';
import { enhanceClientWithSchemas } from '@/lib/supabase/schema-client';

// Define types for the API
interface Case {
  id: string;
  title: string;
  status: string;
  priority: string;
  user_id: string;
  tenant_id: string;
  created_at: string;
  updated_at: string;
  [key: string]: any; // For additional properties
}

interface Deadline {
  id: string;
  title: string;
  description?: string;
  due_date: string;
  user_id: string;
  tenant_id: string;
  created_at: string;
  updated_at: string;
  [key: string]: any; // For additional properties
}

interface AuthUser {
  id: string;
  email?: string;
  role?: string;
  tenantId?: string;
  preferences?: Record<string, any>;
  metadata?: Record<string, any>;
}

// Activity lookback period (24 hours)
const LOOKBACK_HOURS = 24;

// Threshold for morning detection (5 AM to 11 AM)
const MORNING_START_HOUR = 5;
const MORNING_END_HOUR = 11;

// Minimum hours between proactive insight generations
const MIN_HOURS_BETWEEN_INSIGHTS = 4;

/**
 * Generate proactive insights when:
 * 1. The app is opened in the morning
 * 2. The app is opened after a certain period of inactivity
 *
 * GET /api/insights/proactive
 * Query params:
 *   context: "app_open" | "scheduled" - Optional context of the request
 *   timezone: string - Optional timezone offset in minutes
 */
export async function GET(_req: NextRequest): Promise<Response> {
  try {
    // Verify authentication
    const authResult = await verifyJWT(req);
    if (!authResult.success || !authResult.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(req.url);
    const context = searchParams.get('context') || 'app_open';
    const timezoneOffset = parseInt(searchParams.get('timezone') || '0');

    // Check if we should generate insights based on time and previous generation
    const shouldGenerateInsights = await checkShouldGenerateInsights(
      authResult.user.id,
      context,
      timezoneOffset
    );

    if (!shouldGenerateInsights.generate) {
      return NextResponse.json({
        message: 'Skipping proactive insights generation',
        reason: shouldGenerateInsights.reason,
        insights: [] // Return empty array for consistent frontend handling
      });
    }

    // Initialize Supabase client
    const supabase = createClient();

    // Enhance the client with schema support
    const enhancedClient = enhanceClientWithSchemas(supabase);

    // Get recent activities (last 24 hours)
    const lookbackTime = new Date();
    lookbackTime.setHours(lookbackTime.getHours() - LOOKBACK_HOURS);

    const { data: activities, error } = await supabase
      .schema('tenants')
      .from('activities')
      .select('*')
      .eq('user_id', authResult.user.id)
      .gte('created_at', lookbackTime.toISOString())
      .order('created_at', { ascending: false });

    if (error) throw error;

    // If not enough activities, skip insight generation
    if (!activities || activities.length < 3) {
      return NextResponse.json({
        message: 'Not enough recent activities for meaningful insights',
        insights: []
      });
    }

    // Get high-priority cases
    const { data: highPriorityCases, error: caseError } = await enhancedClient.tenants
      .from('cases')
      .select('*')
      .eq('user_id', authResult.user.id)
      .in('status', ['active', 'pending', 'critical'])
      .order('updated_at', { ascending: false })
      .limit(3);

    if (caseError) throw caseError;

    // Get upcoming deadlines
    const nextWeek = new Date();
    nextWeek.setDate(nextWeek.getDate() + 7);

    const { data: upcomingDeadlines, error: deadlineError } = await enhancedClient.tenants
      .from('deadlines')
      .select('*')
      .eq('user_id', authResult.user.id)
      .gte('due_date', new Date().toISOString())
      .lte('due_date', nextWeek.toISOString())
      .order('due_date', { ascending: true })
      .limit(5);

    if (deadlineError) throw deadlineError;

    // Generate insights using the LangGraph workflow
    const insights = await generateProactiveInsights(
      activities as unknown as Activity[],
      highPriorityCases as unknown as Case[] || [],
      upcomingDeadlines as unknown as Deadline[] || [],
      authResult.user,
      context,
      isMorningTime(timezoneOffset)
    );

    // Update the last insight generation time
    await setUserLastActive(authResult.user.id, 'insight_generation', new Date().toISOString());

    // Log this proactive insight generation
    await logActivity({
      userId: authResult.user.id,
      type: 'proactive_insights',
      description: `Generated proactive insights (${context})`,
      metadata: {
        context,
        activityCount: activities.length,
        insightCount: insights.length,
        isMorning: isMorningTime(timezoneOffset)
      }
    });

    return NextResponse.json({
      insights,
      context,
      isMorning: isMorningTime(timezoneOffset)
    });
  } catch (_error) {
    console.error('Error generating proactive insights:', error);
    return NextResponse.json({ error: 'Failed to generate proactive insights' }, { status: 500 });
  }
}

/**
 * Check if we should generate proactive insights based on time and previous generation
 */
async function checkShouldGenerateInsights(
  userId: string,
  context: string,
  timezoneOffset: number
): Promise<{ generate: boolean; reason?: string }> {
  try {
    // Get the last time insights were generated
    const lastInsightGeneration = await getUserLastActive(userId, 'insight_generation');

    // If this is a scheduled check, we want stricter rules
    if (context === 'scheduled') {
      if (lastInsightGeneration) {
        const lastGen = new Date(lastInsightGeneration);
        const hoursSinceLastGen = (Date.now() - lastGen.getTime()) / (1000 * 60 * 60);

        // If we generated insights recently, skip
        if (hoursSinceLastGen < MIN_HOURS_BETWEEN_INSIGHTS) {
          return {
            generate: false,
            reason: `Last generation was too recent (${hoursSinceLastGen.toFixed(1)} hours ago)`
          };
        }
      }
    }

    // Check if it's morning time (special time for daily summary)
    const isMorning = isMorningTime(timezoneOffset);

    // If it's morning, check if we already generated morning insights today
    if (isMorning && lastInsightGeneration) {
      const lastGen = new Date(lastInsightGeneration);
      const today = new Date();

      // If we already generated insights this morning, skip
      if (lastGen.getDate() === today.getDate() &&
          lastGen.getMonth() === today.getMonth() &&
          lastGen.getFullYear() === today.getFullYear() &&
          isTimeInMorningHours(lastGen, timezoneOffset)) {
        return {
          generate: false,
          reason: 'Already generated morning insights today'
        };
      }
    }

    // If this is an app_open context but not morning, check how long since last generation
    if (context === 'app_open' && !isMorning && lastInsightGeneration) {
      const lastGen = new Date(lastInsightGeneration);
      const hoursSinceLastGen = (Date.now() - lastGen.getTime()) / (1000 * 60 * 60);

      // If we generated insights in the last few hours, skip
      if (hoursSinceLastGen < MIN_HOURS_BETWEEN_INSIGHTS) {
        return {
          generate: false,
          reason: `Last generation was too recent (${hoursSinceLastGen.toFixed(1)} hours ago)`
        };
      }
    }

    // All checks passed, generate insights
    return { generate: true };
  } catch (_error) {
    console.error('Error checking insight generation timing:', error);
    // Default to generating insights if there's an error in the check
    return { generate: true, reason: 'Error in timing check' };
  }
}

/**
 * Check if the current time is in the morning hours (5 AM to 11 AM)
 */
function isMorningTime(timezoneOffset: number): boolean {
  const localDate = new Date();

  // Apply timezone offset if provided
  if (timezoneOffset) {
    // Convert timezone offset from minutes to milliseconds
    const offsetMs = timezoneOffset * 60 * 1000;
    // Get UTC time in ms
    const utcTime = localDate.getTime() + (localDate.getTimezoneOffset() * 60 * 1000);
    // Get local time in timezone
    const localTime = new Date(utcTime + offsetMs);

    const hour = localTime.getHours();
    return hour >= MORNING_START_HOUR && hour < MORNING_END_HOUR;
  } else {
    const hour = localDate.getHours();
    return hour >= MORNING_START_HOUR && hour < MORNING_END_HOUR;
  }
}

/**
 * Check if a specific time is within morning hours
 */
function isTimeInMorningHours(date: Date, timezoneOffset: number): boolean {
  // Apply timezone offset if provided
  if (timezoneOffset) {
    // Convert timezone offset from minutes to milliseconds
    const offsetMs = timezoneOffset * 60 * 1000;
    // Get UTC time in ms
    const utcTime = date.getTime() + (date.getTimezoneOffset() * 60 * 1000);
    // Get local time in timezone
    const localTime = new Date(utcTime + offsetMs);

    const hour = localTime.getHours();
    return hour >= MORNING_START_HOUR && hour < MORNING_END_HOUR;
  } else {
    const hour = date.getHours();
    return hour >= MORNING_START_HOUR && hour < MORNING_END_HOUR;
  }
}

/**
 * Generate proactive insights using the LangGraph workflow
 */
async function generateProactiveInsights(
  activities: Activity[],
  highPriorityCases: Case[],
  upcomingDeadlines: Deadline[],
  user: AuthUser,
  context: string,
  isMorning: boolean
): Promise<Insight[]> {
  try {
    // Create the agent instances with specific configuration for proactive insights
    const swarmAgentSystem = new SwarmAgentSystem({
      temperature: 0.3, // Lower temperature for more focused insights
    });

    // @ts-expect-error - Ignoring type errors for now as the SupervisorAgent constructor might have changed
    const supervisorAgent = new SupervisorAgent({
      userId: user.id,
      tenantId: user.tenantId || '',
      timestamp: new Date().toISOString(),
      userPreferences: user.preferences || {},
      feedbackHistory: [] // In the future, fetch feedback history from database
    } as any);

    // Create and run the workflow
    // @ts-expect-error - Ignoring type errors for now as the InsightWorkflow constructor might have changed
    const workflow = new InsightWorkflow(supervisorAgent, swarmAgentSystem);

    // Type assertion for workflow.execute since TypeScript doesn't recognize it
    const execute = (workflow as any).execute as (params: {
      activities: Activity[];
      source: InsightSource;
      startTime: string;
      additionalContext?: Record<string, any>;
    }) => Promise<Insight[]>;

    const insights = await execute({
      activities,
      source: 'proactive' as InsightSource,
      startTime: new Date().toISOString(),
      // Add extra context for proactive insights
      additionalContext: {
        isProactive: true,
        context,
        isMorning,
        highPriorityCases,
        upcomingDeadlines
      }
    });

    // Store insights in Neo4j for future reference (if enabled)
    try {
      for (const insight of insights) {
        await recordInsightInNeo4j(insight, user.id, 'proactive', null, {
          context,
          isMorning
        });
      }
    } catch (neo4jError) {
      console.error('Error storing proactive insights in Neo4j:', neo4jError);
      // Continue even if Neo4j storage fails
    }

    return insights;
  } catch (_error) {
    console.error('Error in proactive insight generation workflow:', error);
    throw error;
  }
}
