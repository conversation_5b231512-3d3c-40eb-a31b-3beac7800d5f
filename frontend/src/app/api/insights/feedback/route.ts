// @ts-nocheck - API route type issues
import { NextRequest, NextResponse } from 'next/server';
import { verifyJWT } from '@/lib/auth/server-exports';
import { recordInsightFeedback } from '@/lib/neo4j/insights';
import { logActivity } from '@/lib/activity-logger';
import { rateLimit } from '@/lib/middlewares/rate-limit';

/**
 * Submit feedback for an insight
 *
 * POST /api/insights/feedback
 * Body: {
 *   feedbackId: string, // The feedback ID from the insight
 *   rating: number, // Rating (1-5)
 *   comment?: string // Optional comment
 * }
 */
export async function POST(req: NextRequest) {
  try {
    // Verify authentication
    const authResult = await verifyJWT(req);
    if (!authResult.success) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Rate limiting - max 20 feedback submissions per minute
    if (!authResult.user) {
      return NextResponse.json({ error: 'User not found in authentication result' }, { status: 401 });
    }

    const rateLimitResult = await rateLimit('insight_feedback', 20, 60, authResult.user.id);
    if (!rateLimitResult.success) {
      return NextResponse.json(
        { error: 'Rate limit exceeded', retryAfter: rateLimitResult.retryAfter },
        { status: 429, headers: { 'Retry-After': String(rateLimitResult.retryAfter) } }
      );
    }

    // Parse request body
    const { feedbackId, rating, comment } = await req.json();

    // Validate request
    if (!feedbackId) {
      return NextResponse.json({ error: 'Missing required field: feedbackId' }, { status: 400 });
    }

    if (rating === undefined || rating === null) {
      return NextResponse.json({ error: 'Missing required field: rating' }, { status: 400 });
    }

    // Validate rating
    const numericRating = Number(rating);
    if (isNaN(numericRating) || numericRating < 1 || numericRating > 5) {
      return NextResponse.json({ error: 'Rating must be a number between 1 and 5' }, { status: 400 });
    }

    // Record feedback in Neo4j
    const success = await recordInsightFeedback(
      feedbackId,
      numericRating,
      comment || '',
      authResult.user.id
    );

    // Log this feedback activity
    await logActivity({
      userId: authResult.user.id,
      type: 'insight_feedback',
      description: `Provided feedback on insight`,
      metadata: {
        feedbackId,
        rating: numericRating,
        hasComment: !!comment
      }
    });

    return NextResponse.json({
      success,
      message: success ? 'Feedback recorded successfully' : 'Failed to record feedback'
    });
  } catch (_error) {
    console.error('Error recording insight feedback:', error);
    return NextResponse.json({ error: 'Failed to record feedback' }, { status: 500 });
  }
}
