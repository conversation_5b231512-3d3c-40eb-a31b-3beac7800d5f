import React from 'react';
import { createServerClient, type CookieOptions } from '@supabase/ssr';
import { cookies } from 'next/headers';
import { Database, Json } from '@/lib/supabase/database.types';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, Ta<PERSON><PERSON>ontent, Ta<PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import {
  BarChart,
  Building,
  CreditCard,
  Users,
  AlertTriangle,
  ArrowUpRight,
  ArrowDownRight,
  Activity,
  Phone,
  MessageSquare,
  BellRing // Added BellRing
} from 'lucide-react';
import Link from 'next/link';

// Define the specific type for a row in the security_events table (in the public schema)
type SecurityEventRow = Database['public']['Tables']['security_events']['Row'];

// Define the structure expected within the 'details' JSON column of security_events
interface SecurityEventDetails {
  description?: string;
  ip_address?: string;
  user_agent?: string;
  tenant_id?: string;
  error_message?: string;
  // Add other potential fields if known
}

export default async function AdminDashboard() {
  // Get the cookie store - use synchronous version for SSR
  const cookieStore = cookies();

  const supabase = createServerClient<Database>(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        get(name: string) {
          // Use type assertion to handle the cookies API
          return (cookieStore as any).get(name)?.value;
        },
        set(name: string, value: string, options: CookieOptions) {
          try {
            // Use type assertion to handle the cookies API
            (cookieStore as any).set({ name, value, ...options });
          } catch (_error) {
            // The `set` method was called from a Server Component.
            // This can be ignored if you have middleware refreshing
            // user sessions.
          }
        },
        remove(name: string, options: CookieOptions) {
          try {
            // Use type assertion to handle the cookies API
            (cookieStore as any).set({ name, value: '', ...options });
          } catch (_error) {
            // The `delete` method was called from a Server Component.
            // This can be ignored if you have middleware refreshing
            // user sessions.
          }
        },
      },
    }
  );

  const { count: tenantCount, error: tenantError } = await supabase
    .schema('tenants')
    .from('firms')
    .select('*', { count: 'exact', head: true });
  if (tenantError) console.error('Error fetching tenant count:', tenantError.message);

  const { count: userCount, error: userError } = await supabase
    .schema('tenants')
    .from('users')
    .select('*', { count: 'exact', head: true });
  if (userError) console.error('Error fetching user count:', userError.message);

  const { count: activeSubscriptionCount, error: subError } = await supabase
    .schema('tenants')
    .from('tenant_subscriptions')
    .select('*', { count: 'exact', head: true })
    .in('status', ['active', 'trialing']);
  if (subError) console.error('Error fetching subscription count:', subError.message);

  // Fetch recent security events (from public schema)
  const { data: recentAlerts, error: alertError } = await supabase
    .from('security_events') // Correct: Table is in public schema
    .select('*')
    .order('created_at', { ascending: false })
    .limit(5);

  if (alertError) console.error('Error fetching recent alerts:', alertError.message);

  const alertsToDisplay: SecurityEventRow[] = recentAlerts || [];

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold tracking-tight">Admin Dashboard</h1>
        <div className="flex items-center gap-2">
          <span className="text-sm text-muted-foreground">Last updated: {new Date().toLocaleString()}</span>
        </div>
      </div>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Tenants</CardTitle>
            <Building className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{tenantCount || 0}</div>
            <p className="text-xs text-muted-foreground">
              <span className="text-green-500 inline-flex items-center">
                <ArrowUpRight className="mr-1 h-3 w-3" />
                +2
              </span>{' '}
              since last month
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Users</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{userCount || 0}</div>
            <p className="text-xs text-muted-foreground">
              <span className="text-green-500 inline-flex items-center">
                <ArrowUpRight className="mr-1 h-3 w-3" />
                +15
              </span>{' '}
              since last month
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Subscriptions</CardTitle>
            <CreditCard className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{activeSubscriptionCount || 0}</div>
            <p className="text-xs text-muted-foreground">
              <span className="text-green-500 inline-flex items-center">
                <ArrowUpRight className="mr-1 h-3 w-3" />
                +3
              </span>{' '}
              since last month
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Security Alerts</CardTitle>
            <AlertTriangle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{alertsToDisplay.length || 0}</div>
            <p className="text-xs text-muted-foreground">
              <span className="text-red-500 inline-flex items-center">
                <ArrowUpRight className="mr-1 h-3 w-3" />
                +2
              </span>{' '}
              since yesterday
            </p>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="usage">Usage</TabsTrigger>
          <TabsTrigger value="security">Security</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-7">
            <Card className="col-span-4">
              <CardHeader>
                <CardTitle>Subscription Revenue</CardTitle>
                <CardDescription>
                  Monthly subscription revenue over time
                </CardDescription>
              </CardHeader>
              <CardContent className="pl-2">
                <div className="h-[200px] flex items-center justify-center text-muted-foreground">
                  <BarChart className="h-16 w-16" />
                  <span className="ml-2">Revenue chart will be displayed here</span>
                </div>
              </CardContent>
            </Card>

            <Card className="col-span-3">
              <CardHeader>
                <CardTitle>Recent Activity</CardTitle>
                <CardDescription>
                  Latest platform activity
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center">
                    <div className="mr-2 h-8 w-8 rounded-full bg-primary/10 flex items-center justify-center">
                      <Users className="h-4 w-4 text-primary" />
                    </div>
                    <div className="space-y-0.5">
                      <p className="text-sm font-medium">New user registered</p>
                      <p className="text-xs text-muted-foreground">2 minutes ago</p>
                    </div>
                  </div>

                  <div className="flex items-center">
                    <div className="mr-2 h-8 w-8 rounded-full bg-primary/10 flex items-center justify-center">
                      <CreditCard className="h-4 w-4 text-primary" />
                    </div>
                    <div className="space-y-0.5">
                      <p className="text-sm font-medium">Subscription upgraded</p>
                      <p className="text-xs text-muted-foreground">1 hour ago</p>
                    </div>
                  </div>

                  <div className="flex items-center">
                    <div className="mr-2 h-8 w-8 rounded-full bg-primary/10 flex items-center justify-center">
                      <Building className="h-4 w-4 text-primary" />
                    </div>
                    <div className="space-y-0.5">
                      <p className="text-sm font-medium">New tenant onboarded</p>
                      <p className="text-xs text-muted-foreground">3 hours ago</p>
                    </div>
                  </div>

                  <div className="flex items-center">
                    <div className="mr-2 h-8 w-8 rounded-full bg-primary/10 flex items-center justify-center">
                      <AlertTriangle className="h-4 w-4 text-primary" />
                    </div>
                    <div className="space-y-0.5">
                      <p className="text-sm font-medium">Security alert triggered</p>
                      <p className="text-xs text-muted-foreground">5 hours ago</p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            <Card>
              <CardHeader>
                <CardTitle>Top Tenants</CardTitle>
                <CardDescription>
                  By usage and revenue
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <p className="text-sm font-medium">Smith & Associates</p>
                      <p className="text-xs text-muted-foreground">Enterprise Plan</p>
                    </div>
                    <div className="text-sm font-medium">$499/mo</div>
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <p className="text-sm font-medium">Johnson Legal Group</p>
                      <p className="text-xs text-muted-foreground">Professional Plan</p>
                    </div>
                    <div className="text-sm font-medium">$199/mo</div>
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <p className="text-sm font-medium">Davis Law Firm</p>
                      <p className="text-xs text-muted-foreground">Professional Plan</p>
                    </div>
                    <div className="text-sm font-medium">$199/mo</div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Subscription Plans</CardTitle>
                <CardDescription>
                  Distribution of active plans
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-[200px] flex items-center justify-center text-muted-foreground">
                  <BarChart className="h-16 w-16" />
                  <span className="ml-2">Plan distribution chart</span>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Quick Actions</CardTitle>
                <CardDescription>
                  Common administrative tasks
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <Link
                    href="/admin/tenants/new"
                    className="flex items-center p-2 rounded-md hover:bg-muted transition-colors"
                  >
                    <Building className="h-4 w-4 mr-2" />
                    <span className="text-sm">Add New Tenant</span>
                  </Link>

                  <Link
                    href="/admin/subscriptions"
                    className="flex items-center p-2 rounded-md hover:bg-muted transition-colors"
                  >
                    <CreditCard className="h-4 w-4 mr-2" />
                    <span className="text-sm">Manage Subscriptions</span>
                  </Link>

                  <Link
                    href="/admin/security"
                    className="flex items-center p-2 rounded-md hover:bg-muted transition-colors"
                  >
                    <AlertTriangle className="h-4 w-4 mr-2" />
                    <span className="text-sm">View Security Alerts</span>
                  </Link>

                  <Link
                    href="/admin/usage"
                    className="flex items-center p-2 rounded-md hover:bg-muted transition-colors"
                  >
                    <Activity className="h-4 w-4 mr-2" />
                    <span className="text-sm">Monitor Usage</span>
                  </Link>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="usage" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">AI Tokens</CardTitle>
                <MessageSquare className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">2.4M</div>
                <p className="text-xs text-muted-foreground">
                  <span className="text-green-500 inline-flex items-center">
                    <ArrowUpRight className="mr-1 h-3 w-3" />
                    +12%
                  </span>{' '}
                  from last month
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Voice Agent (Inbound)</CardTitle>
                <Phone className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">1,245 min</div>
                <p className="text-xs text-muted-foreground">
                  <span className="text-green-500 inline-flex items-center">
                    <ArrowUpRight className="mr-1 h-3 w-3" />
                    +18%
                  </span>{' '}
                  from last month
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Voice Agent (Outbound)</CardTitle>
                <Phone className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">532 min</div>
                <p className="text-xs text-muted-foreground">
                  <span className="text-green-500 inline-flex items-center">
                    <ArrowUpRight className="mr-1 h-3 w-3" />
                    +7%
                  </span>{' '}
                  from last month
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Document Processing</CardTitle>
                <Activity className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">3,721</div>
                <p className="text-xs text-muted-foreground">
                  <span className="text-red-500 inline-flex items-center">
                    <ArrowDownRight className="mr-1 h-3 w-3" />
                    -3%
                  </span>{' '}
                  from last month
                </p>
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>Usage Trends</CardTitle>
              <CardDescription>
                Resource usage over the past 30 days
              </CardDescription>
            </CardHeader>
            <CardContent className="pl-2">
              <div className="h-[300px] flex items-center justify-center text-muted-foreground">
                <BarChart className="h-16 w-16" />
                <span className="ml-2">Usage trend chart will be displayed here</span>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="security" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Recent Security Events</CardTitle>
              <CardDescription>
                Latest security alerts and events
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {alertsToDisplay.length > 0 ? (
                alertsToDisplay.map((alert) => (
                  <div key={alert.id} className="flex items-center">
                    <div className="flex h-8 w-8 items-center justify-center rounded-full bg-muted">
                      {/* You might want a specific icon based on alert.severity or alert.event_type */}
                      <BellRing className="h-4 w-4" />
                    </div>
                    <div className="ml-4 flex-1">
                      <div className="space-y-1">
                        <p className="text-sm font-medium">{alert.event_type}</p>
                        <p className="text-xs text-muted-foreground">{(alert.details as SecurityEventDetails)?.description || 'No description available'}</p>
                        <div className="flex items-center text-xs text-muted-foreground">
                          <span>{alert.created_at ? new Date(alert.created_at).toLocaleString() : 'Unknown date'}</span>
                          <span className="mx-2">•</span>
                          <span>IP: {(alert.details as SecurityEventDetails)?.ip_address || 'Unknown'}</span>
                          {(alert.details as SecurityEventDetails)?.tenant_id && (
                            <>
                              <span className="mx-2">•</span>
                              <span>Tenant ID: {(alert.details as SecurityEventDetails).tenant_id}</span>
                            </>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                ))
              ) : (
                <div className="text-center py-4 text-muted-foreground">
                  No recent security events
                </div>
              )}
            </CardContent>
          </Card>

          <div className="grid gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>Login Attempts</CardTitle>
                <CardDescription>
                  Successful vs. failed login attempts
                </CardDescription>
              </CardHeader>
              <CardContent className="pl-2">
                <div className="h-[200px] flex items-center justify-center text-muted-foreground">
                  <BarChart className="h-16 w-16" />
                  <span className="ml-2">Login attempts chart</span>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Security Recommendations</CardTitle>
                <CardDescription>
                  Suggested actions to improve security
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-start space-x-2">
                    <div className="mt-0.5 h-5 w-5 rounded-full bg-amber-500/20 flex items-center justify-center">
                      <AlertTriangle className="h-3 w-3 text-amber-500" />
                    </div>
                    <div>
                      <p className="text-sm font-medium">Enable MFA for all admin users</p>
                      <p className="text-xs text-muted-foreground">
                        3 admin users don&apos;t have MFA enabled
                      </p>
                    </div>
                  </div>

                  <div className="flex items-start space-x-2">
                    <div className="mt-0.5 h-5 w-5 rounded-full bg-amber-500/20 flex items-center justify-center">
                      <AlertTriangle className="h-3 w-3 text-amber-500" />
                    </div>
                    <div>
                      <p className="text-sm font-medium">Review API key usage</p>
                      <p className="text-xs text-muted-foreground">
                        5 API keys haven't been rotated in over 90 days
                      </p>
                    </div>
                  </div>

                  <div className="flex items-start space-x-2">
                    <div className="mt-0.5 h-5 w-5 rounded-full bg-amber-500/20 flex items-center justify-center">
                      <AlertTriangle className="h-3 w-3 text-amber-500" />
                    </div>
                    <div>
                      <p className="text-sm font-medium">Update RLS policies</p>
                      <p className="text-xs text-muted-foreground">
                        Some tables have overly permissive policies
                      </p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
