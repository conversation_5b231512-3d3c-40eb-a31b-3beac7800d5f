import React from 'react';
import { redirect } from 'next/navigation';
import { createServerClient, type CookieOptions } from '@supabase/ssr';
import { cookies } from 'next/headers';
import { Database } from '@/lib/supabase/database.types';
import AdminSidebar from '@/components/admin/admin-sidebar';
import AdminHeader from '@/components/admin/admin-header';
import { UserRole } from '@/lib/types/auth';

const VALID_ADMIN_ROLES: ReadonlySet<UserRole> = new Set([UserRole.Partner]);
function isValidAdminRole(role: any): role is UserRole {
  return typeof role === 'string' && VALID_ADMIN_ROLES.has(role as UserRole);
}

export const metadata = {
  title: 'Admin Dashboard | PI Lawyer AI',
  description: 'Superadmin dashboard for managing the PI Lawyer AI platform',
};

export default async function AdminLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  // Get the cookie store - use synchronous version for SSR
  const cookieStore = cookies();

  const supabase = createServerClient<Database>(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        get(name: string) {
          // Use type assertion to handle the cookies API
          return (cookieStore as any).get(name)?.value;
        },
        set(name: string, value: string, options: CookieOptions) {
          try {
            // Use type assertion to handle the cookies API
            (cookieStore as any).set({ name, value, ...options });
          } catch (_error) {
            // The `set` method was called from a Server Component.
            // This can be ignored if you have middleware refreshing
            // user sessions.
          }
        },
        remove(name: string, options: CookieOptions) {
          try {
            // Use type assertion to handle the cookies API
            (cookieStore as any).set({ name, value: '', ...options });
          } catch (_error) {
            // The `delete` method was called from a Server Component.
            // This can be ignored if you have middleware refreshing
            // user sessions.
          }
        },
      },
    }
  );

  const { data: { user }, error: getUserError } = await supabase.auth.getUser();

  if (getUserError || !user) {
    console.error('AdminLayout: Error fetching user or no user found. Redirecting to login.', getUserError);
    redirect('/loginadmin?error=unauthenticated');
  }

  const potentialRole = user.app_metadata?.role || user.user_metadata?.role;
  const userRole = isValidAdminRole(potentialRole) ? potentialRole : undefined;

  if (!userRole) {
    console.warn(`AdminLayout: User ${user.email} does not have required 'partner' role. Redirecting.`);
    redirect('/dashboard?error=unauthorized');
  }

  // Check if it's the superadmin trying to access the partner admin area
  if (user.email === '<EMAIL>') {
      console.warn(`AdminLayout: Superadmin ${user.email} attempting to access partner admin area. Redirecting.`);
      redirect('/superadmin?error=incorrect_dashboard');
  }

  return (
    <div className="flex h-screen bg-gray-100">
      <AdminSidebar />
      <div className="flex flex-col flex-1 overflow-hidden">
        <AdminHeader user={user} />
        <main className="flex-1 overflow-y-auto p-4 md:p-6">
          {children}
        </main>
      </div>
    </div>
  );
}
