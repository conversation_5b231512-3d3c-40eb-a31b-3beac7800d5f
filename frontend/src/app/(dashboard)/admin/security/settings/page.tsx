'use client'

import { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { <PERSON><PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Switch } from '@/components/ui/switch'
import { Slider } from '@/components/ui/slider'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { toast } from 'sonner'
import { Shield, Bell, Key, Lock, Globe, Clock } from 'lucide-react'

export default function SecuritySettingsPage() {
  // General security settings
  const [loginAttempts, setLoginAttempts] = useState(5)
  const [lockoutDuration, setLockoutDuration] = useState(30)
  const [passwordExpiry, setPasswordExpiry] = useState(90)
  const [mfaRequired, setMfaRequired] = useState(false)
  const [ipRestrictions, setIpRestrictions] = useState('')

  // Alert settings
  const [emailAlerts, setEmailAlerts] = useState(true)
  const [smsAlerts, setSmsAlerts] = useState(false)
  const [slackAlerts, setSlackAlerts] = useState(false)
  const [alertThreshold, setAlertThreshold] = useState(70)
  const [alertRecipients, setAlertRecipients] = useState('')

  // Token settings
  const [tokenLifetime, setTokenLifetime] = useState(60)
  const [refreshTokenLifetime, setRefreshTokenLifetime] = useState(7)
  const [enforceDeviceBinding, setEnforceDeviceBinding] = useState(true)
  const [allowMultipleSessions, setAllowMultipleSessions] = useState(true)

  // Audit settings
  const [retentionPeriod, setRetentionPeriod] = useState(90)
  const [detailedLogging, setDetailedLogging] = useState(true)
  const [logIpAddresses, setLogIpAddresses] = useState(true)
  const [logUserAgents, setLogUserAgents] = useState(true)

  const handleSaveSettings = async (section: string) => {
    try {
      // In a real implementation, this would call the API to save settings
      toast.success(`${section} settings saved successfully`)
    } catch (_error) {
      console.error(`Error saving ${section} settings:`, error)
      toast.error(`Failed to save ${section} settings`)
    }
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold tracking-tight">Security Settings</h1>
      </div>

      <Tabs defaultValue="general">
        <TabsList className="grid grid-cols-4 w-full max-w-2xl">
          <TabsTrigger value="general" className="flex items-center">
            <Shield className="h-4 w-4 mr-2" />
            General
          </TabsTrigger>
          <TabsTrigger value="alerts" className="flex items-center">
            <Bell className="h-4 w-4 mr-2" />
            Alerts
          </TabsTrigger>
          <TabsTrigger value="tokens" className="flex items-center">
            <Key className="h-4 w-4 mr-2" />
            Tokens
          </TabsTrigger>
          <TabsTrigger value="audit" className="flex items-center">
            <Clock className="h-4 w-4 mr-2" />
            Audit
          </TabsTrigger>
        </TabsList>

        {/* General Security Settings */}
        <TabsContent value="general" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Authentication Settings</CardTitle>
              <CardDescription>
                Configure authentication security parameters
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="login-attempts">Max Failed Login Attempts</Label>
                  <div className="flex items-center space-x-2">
                    <Input
                      id="login-attempts"
                      type="number"
                      value={loginAttempts}
                      onChange={(e) => setLoginAttempts(parseInt(e.target.value))}
                      min={1}
                      max={10}
                    />
                    <span className="text-sm text-muted-foreground">attempts</span>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="lockout-duration">Account Lockout Duration</Label>
                  <div className="flex items-center space-x-2">
                    <Input
                      id="lockout-duration"
                      type="number"
                      value={lockoutDuration}
                      onChange={(e) => setLockoutDuration(parseInt(e.target.value))}
                      min={5}
                      max={1440}
                    />
                    <span className="text-sm text-muted-foreground">minutes</span>
                  </div>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="password-expiry">Password Expiry</Label>
                  <div className="flex items-center space-x-2">
                    <Input
                      id="password-expiry"
                      type="number"
                      value={passwordExpiry}
                      onChange={(e) => setPasswordExpiry(parseInt(e.target.value))}
                      min={0}
                      max={365}
                    />
                    <span className="text-sm text-muted-foreground">days (0 = never)</span>
                  </div>
                </div>

                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <Label htmlFor="mfa-required">Require MFA for All Users</Label>
                    <Switch
                      id="mfa-required"
                      checked={mfaRequired}
                      onCheckedChange={setMfaRequired}
                    />
                  </div>
                  <p className="text-sm text-muted-foreground">
                    When enabled, all users will be required to set up multi-factor authentication
                  </p>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="ip-restrictions">IP Address Restrictions</Label>
                <Input
                  id="ip-restrictions"
                  placeholder="e.g. ***********/24, 10.0.0.0/8 (comma separated)"
                  value={ipRestrictions}
                  onChange={(e) => setIpRestrictions(e.target.value)}
                />
                <p className="text-sm text-muted-foreground">
                  Restrict access to specific IP addresses or CIDR ranges (leave empty for no restrictions)
                </p>
              </div>

              <Button onClick={() => handleSaveSettings('General')}>
                Save Authentication Settings
              </Button>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Geographic Restrictions</CardTitle>
              <CardDescription>
                Configure location-based access restrictions
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <Label htmlFor="geo-restrictions">Enable Geographic Restrictions</Label>
                  <Switch
                    id="geo-restrictions"
                    checked={true}
                  />
                </div>
                <p className="text-sm text-muted-foreground">
                  When enabled, access will be restricted based on geographic location
                </p>
              </div>

              <div className="space-y-2">
                <Label>Allowed Countries</Label>
                <Select defaultValue="all">
                  <SelectTrigger>
                    <SelectValue placeholder="Select allowed countries" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Countries</SelectItem>
                    <SelectItem value="us">United States Only</SelectItem>
                    <SelectItem value="us-ca">US and Canada Only</SelectItem>
                    <SelectItem value="custom">Custom Selection</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <Button onClick={() => handleSaveSettings('Geographic')}>
                Save Geographic Settings
              </Button>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Alert Settings */}
        <TabsContent value="alerts" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Alert Notifications</CardTitle>
              <CardDescription>
                Configure how security alerts are delivered
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-3 gap-4">
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <Label htmlFor="email-alerts">Email Alerts</Label>
                    <Switch
                      id="email-alerts"
                      checked={emailAlerts}
                      onCheckedChange={setEmailAlerts}
                    />
                  </div>
                  <p className="text-sm text-muted-foreground">
                    Send security alerts via email
                  </p>
                </div>

                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <Label htmlFor="sms-alerts">SMS Alerts</Label>
                    <Switch
                      id="sms-alerts"
                      checked={smsAlerts}
                      onCheckedChange={setSmsAlerts}
                    />
                  </div>
                  <p className="text-sm text-muted-foreground">
                    Send security alerts via SMS
                  </p>
                </div>

                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <Label htmlFor="slack-alerts">Slack Alerts</Label>
                    <Switch
                      id="slack-alerts"
                      checked={slackAlerts}
                      onCheckedChange={setSlackAlerts}
                    />
                  </div>
                  <p className="text-sm text-muted-foreground">
                    Send security alerts to Slack
                  </p>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="alert-threshold">Alert Threshold</Label>
                <div className="pt-2">
                  <Slider
                    id="alert-threshold"
                    min={0}
                    max={100}
                    step={5}
                    value={[alertThreshold]}
                    onValueChange={(value: number[]) => setAlertThreshold(value[0])}
                  />
                  <div className="flex justify-between text-xs text-muted-foreground mt-1">
                    <span>Low (0)</span>
                    <span>Medium (50)</span>
                    <span>High (100)</span>
                  </div>
                </div>
                <p className="text-sm text-muted-foreground mt-2">
                  Only send alerts for events with a severity score above this threshold
                </p>
              </div>

              <div className="space-y-2">
                <Label htmlFor="alert-recipients">Alert Recipients</Label>
                <Input
                  id="alert-recipients"
                  placeholder="<EMAIL>, <EMAIL>"
                  value={alertRecipients}
                  onChange={(e) => setAlertRecipients(e.target.value)}
                />
                <p className="text-sm text-muted-foreground">
                  Comma-separated list of email addresses to receive alerts
                </p>
              </div>

              <Button onClick={() => handleSaveSettings('Alert')}>
                Save Alert Settings
              </Button>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Alert Types</CardTitle>
              <CardDescription>
                Configure which types of events trigger alerts
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <Label htmlFor="login-alerts">Failed Login Attempts</Label>
                    <Switch
                      id="login-alerts"
                      checked={true}
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <Label htmlFor="location-alerts">Unusual Locations</Label>
                    <Switch
                      id="location-alerts"
                      checked={true}
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <Label htmlFor="permission-alerts">Permission Changes</Label>
                    <Switch
                      id="permission-alerts"
                      checked={true}
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <Label htmlFor="token-alerts">Token Revocations</Label>
                    <Switch
                      id="token-alerts"
                      checked={true}
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <Label htmlFor="admin-alerts">Admin Actions</Label>
                    <Switch
                      id="admin-alerts"
                      checked={true}
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <Label htmlFor="data-alerts">Sensitive Data Access</Label>
                    <Switch
                      id="data-alerts"
                      checked={true}
                    />
                  </div>
                </div>
              </div>

              <Button onClick={() => handleSaveSettings('Alert Types')}>
                Save Alert Types
              </Button>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Token Settings */}
        <TabsContent value="tokens" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Token Lifecycle</CardTitle>
              <CardDescription>
                Configure token expiration and renewal settings
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="token-lifetime">Access Token Lifetime</Label>
                  <div className="flex items-center space-x-2">
                    <Input
                      id="token-lifetime"
                      type="number"
                      value={tokenLifetime}
                      onChange={(e) => setTokenLifetime(parseInt(e.target.value))}
                      min={5}
                      max={1440}
                    />
                    <span className="text-sm text-muted-foreground">minutes</span>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="refresh-token-lifetime">Refresh Token Lifetime</Label>
                  <div className="flex items-center space-x-2">
                    <Input
                      id="refresh-token-lifetime"
                      type="number"
                      value={refreshTokenLifetime}
                      onChange={(e) => setRefreshTokenLifetime(parseInt(e.target.value))}
                      min={1}
                      max={90}
                    />
                    <span className="text-sm text-muted-foreground">days</span>
                  </div>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <Label htmlFor="device-binding">Enforce Device Binding</Label>
                    <Switch
                      id="device-binding"
                      checked={enforceDeviceBinding}
                      onCheckedChange={setEnforceDeviceBinding}
                    />
                  </div>
                  <p className="text-sm text-muted-foreground">
                    Bind tokens to the device they were issued on
                  </p>
                </div>

                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <Label htmlFor="multiple-sessions">Allow Multiple Sessions</Label>
                    <Switch
                      id="multiple-sessions"
                      checked={allowMultipleSessions}
                      onCheckedChange={setAllowMultipleSessions}
                    />
                  </div>
                  <p className="text-sm text-muted-foreground">
                    Allow users to be logged in on multiple devices simultaneously
                  </p>
                </div>
              </div>

              <Button onClick={() => handleSaveSettings('Token')}>
                Save Token Settings
              </Button>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Token Revocation</CardTitle>
              <CardDescription>
                Configure automatic token revocation settings
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 gap-4">
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <Label htmlFor="revoke-on-password-change">Revoke on Password Change</Label>
                    <Switch
                      id="revoke-on-password-change"
                      checked={true}
                    />
                  </div>
                  <p className="text-sm text-muted-foreground">
                    Automatically revoke all tokens when a user changes their password
                  </p>
                </div>

                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <Label htmlFor="revoke-on-role-change">Revoke on Role Change</Label>
                    <Switch
                      id="revoke-on-role-change"
                      checked={true}
                    />
                  </div>
                  <p className="text-sm text-muted-foreground">
                    Automatically revoke all tokens when a user's role is changed
                  </p>
                </div>

                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <Label htmlFor="revoke-on-suspicious">Revoke on Suspicious Activity</Label>
                    <Switch
                      id="revoke-on-suspicious"
                      checked={true}
                    />
                  </div>
                  <p className="text-sm text-muted-foreground">
                    Automatically revoke tokens when suspicious activity is detected
                  </p>
                </div>
              </div>

              <Button onClick={() => handleSaveSettings('Token Revocation')}>
                Save Revocation Settings
              </Button>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Audit Settings */}
        <TabsContent value="audit" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Audit Log Settings</CardTitle>
              <CardDescription>
                Configure audit logging and retention settings
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="retention-period">Log Retention Period</Label>
                <div className="flex items-center space-x-2">
                  <Input
                    id="retention-period"
                    type="number"
                    value={retentionPeriod}
                    onChange={(e) => setRetentionPeriod(parseInt(e.target.value))}
                    min={30}
                    max={3650}
                  />
                  <span className="text-sm text-muted-foreground">days</span>
                </div>
                <p className="text-sm text-muted-foreground">
                  How long to retain security audit logs
                </p>
              </div>

              <div className="grid grid-cols-3 gap-4">
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <Label htmlFor="detailed-logging">Detailed Logging</Label>
                    <Switch
                      id="detailed-logging"
                      checked={detailedLogging}
                      onCheckedChange={setDetailedLogging}
                    />
                  </div>
                  <p className="text-sm text-muted-foreground">
                    Log detailed information about each event
                  </p>
                </div>

                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <Label htmlFor="log-ip">Log IP Addresses</Label>
                    <Switch
                      id="log-ip"
                      checked={logIpAddresses}
                      onCheckedChange={setLogIpAddresses}
                    />
                  </div>
                  <p className="text-sm text-muted-foreground">
                    Include IP addresses in audit logs
                  </p>
                </div>

                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <Label htmlFor="log-user-agent">Log User Agents</Label>
                    <Switch
                      id="log-user-agent"
                      checked={logUserAgents}
                      onCheckedChange={setLogUserAgents}
                    />
                  </div>
                  <p className="text-sm text-muted-foreground">
                    Include browser/device info in logs
                  </p>
                </div>
              </div>

              <div className="space-y-2">
                <Label>Log Export Format</Label>
                <Select defaultValue="json">
                  <SelectTrigger>
                    <SelectValue placeholder="Select export format" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="json">JSON</SelectItem>
                    <SelectItem value="csv">CSV</SelectItem>
                    <SelectItem value="syslog">Syslog</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <Button onClick={() => handleSaveSettings('Audit')}>
                Save Audit Settings
              </Button>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Export Audit Logs</CardTitle>
              <CardDescription>
                Export security audit logs for external analysis
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label>Date Range</Label>
                  <Select defaultValue="last-30">
                    <SelectTrigger>
                      <SelectValue placeholder="Select date range" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="last-7">Last 7 days</SelectItem>
                      <SelectItem value="last-30">Last 30 days</SelectItem>
                      <SelectItem value="last-90">Last 90 days</SelectItem>
                      <SelectItem value="custom">Custom range</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label>Event Types</Label>
                  <Select defaultValue="all">
                    <SelectTrigger>
                      <SelectValue placeholder="Select event types" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Events</SelectItem>
                      <SelectItem value="auth">Authentication Events</SelectItem>
                      <SelectItem value="data">Data Access Events</SelectItem>
                      <SelectItem value="admin">Admin Actions</SelectItem>
                      <SelectItem value="suspicious">Suspicious Activities</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="flex space-x-2">
                <Button>
                  Export to CSV
                </Button>
                <Button variant="outline">
                  Export to JSON
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
