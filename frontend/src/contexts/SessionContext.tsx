// src/contexts/SessionContext.tsx
'use client';
import React, { createContext, useContext, useEffect, useState, useCallback } from 'react';
import { supabase } from '@/lib/supabase/client';
import { isCypressTest, getMockSession } from '@/lib/testing/cypress-utils';
import { Session, User } from '@supabase/supabase-js';

// Define robust session context with refresh capabilities
interface SessionContextType {
  session: Session | null;
  user: User | null;
  isLoading: boolean;
  isCypressTest: boolean;
  refreshSession: () => Promise<void>;
}

// Context is undefined by default until provider mounts
const SessionContext = createContext<SessionContextType | undefined>(undefined);

export function SessionProvider({ children }: { children: React.ReactNode }) {
  // Use explicit null vs undefined for clear loading states
  const [session, setSession] = useState<Session | null>(null);
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [refreshCount, setRefreshCount] = useState(0);
  const isTestEnv = isCypressTest();

  // Manual session refresh function
  const refreshSession = useCallback(async () => {
    console.log(' Manually refreshing session');
    try {
      const { data, error } = await supabase.auth.getSession();
      if (error) {
        console.error(' Session refresh error:', error);
        return;
      }
      // Only update if session state actually changed
      const currentSessionId = session?.access_token;
      const newSessionId = data.session?.access_token;

      if (currentSessionId !== newSessionId) {
        console.log(' Session refreshed with new token');
        setSession(data.session);
        setUser(data.session?.user ?? null);
      } else {
        console.log(' Session unchanged after refresh');
      }
    } catch (_error) {
      console.error(' Session refresh exception:', error);
    }
  }, [session]);

  // Initialize session
  useEffect(() => {
    let mounted = true;
    let authListener: { data: { subscription: { unsubscribe: () => void } } } | null = null;

    // Initial session load
    const initializeSession = async () => {
      console.log(' Initializing session');
      setIsLoading(true);

      // Special handling for Cypress tests
      if (isTestEnv) {
        const mockSession = getMockSession();
        if (mockSession) {
          console.log(' Using mock session for Cypress');
          if (mounted) {
            setSession(mockSession);
            setUser(mockSession.user);
            setIsLoading(false);
          }
          return;
        }
      }

      try {
        // Get initial session state
        const { data, error } = await supabase.auth.getSession();

        if (error) {
          console.error(' Initial session error:', error);
          if (mounted) {
            setSession(null);
            setUser(null);
          }
        } else if (mounted) {
            console.log(' Initial session state:', data.session ? 'Authenticated' : 'Unauthenticated');
            setSession(data.session);
            setUser(data.session?.user ?? null);
        }

        // Setup limited listener for critical auth events only
        if (mounted && !error) {
          authListener = supabase.auth.onAuthStateChange((event, eventSession) => {
            if (!mounted) return;

            console.log(` Auth event: ${event}`);

            // Handle only essential events without triggering unnecessary refreshes
            switch (event) {
              case 'SIGNED_IN':
                console.log(' User signed in');
                setSession(eventSession);
                setUser(eventSession?.user ?? null);
                setIsLoading(false);
                break;

              case 'SIGNED_OUT':
                console.log(' User signed out');
                setSession(null);
                setUser(null);
                setIsLoading(false);
                break;

              // Ignore TOKEN_REFRESHED - we manage refreshes manually
              // Ignore USER_UPDATED - we'll handle it with dedicated calls when needed
            }
          });
        }
      } catch (_e) {
        console.error(' Session initialization error:', e);
        if (mounted) {
          setSession(null);
          setUser(null);
        }
      } finally {
         if (mounted) {
            setIsLoading(false);
         }
      }
    };

    initializeSession();

    // Cleanup function
    return () => {
      mounted = false;
      if (authListener) {
        authListener.data.subscription.unsubscribe();
      }
    };
  }, [isTestEnv]);

  // Set up periodic session validation without constant refreshing
  useEffect(() => {
    // Only run if we have an active session and aren't in a test environment
    if (!session || isLoading || isTestEnv) return;

    // Validate session every 5 minutes - infrequent to avoid token churn
    const intervalId = setInterval(() => {
      // Increment counter to trigger refresh dependency
      setRefreshCount(prev => prev + 1);
    }, 5 * 60 * 1000); // 5 minutes

    return () => clearInterval(intervalId);
  }, [session, isLoading, isTestEnv]);

  // Actual refresh logic triggered by counter changes
  useEffect(() => {
    // Ensure not loading and session exists before refresh
    if (refreshCount > 0 && session && !isLoading) {
      refreshSession();
    }
  }, [refreshCount, session, isLoading, refreshSession]);

  // Complete context with session, user, and manual refresh capability
  const contextValue: SessionContextType = {
    session,
    user,
    isLoading,
    isCypressTest: isTestEnv,
    refreshSession
  };

  return (
    <SessionContext.Provider value={contextValue}>
      {children}
    </SessionContext.Provider>
  );
}

export function useSession() {
  const context = useContext(SessionContext)
  if (!context) {
    throw new Error('useSession must be used inside SessionProvider')
  }
  return context
}
