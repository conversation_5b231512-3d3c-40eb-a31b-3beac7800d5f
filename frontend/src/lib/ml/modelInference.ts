/**
 * Model inference using ONNX Runtime Web
 * This module handles loading and running inference with ONNX models
 * for activity prioritization classification.
 */
import * as ort from 'onnxruntime-web';
import { createClient } from '@supabase/supabase-js';
import { Storage } from '@google-cloud/storage';
import { ActivityFeatures } from './features';

// Priority classification results
export enum PriorityLevel {
  LOW = 'Low',
  MEDIUM = 'Medium',
  HIGH = 'High'
}

// Model metadata and cache
interface ModelInfo {
  modelPath: string;
  lastUpdated: Date;
  session?: ort.InferenceSession;
}

// Model cache to avoid reloading models unnecessarily
const modelCache = new Map<string, ModelInfo>();

// Initialize Supabase client
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!;
const supabase = createClient(supabaseUrl, supabaseKey);

/**
 * Gets the latest model for a tenant
 * @param tenantId The tenant ID
 * @param modelName The model name (defaults to 'activity_prioritization')
 * @returns Path to the model in GCS
 */
export async function getLatestModelPath(
  tenantId: string,
  modelName = 'activity_prioritization'
): Promise<string | null> {
  try {
    // Get latest model record from Supabase
    const { data, error } = await supabase
      .from('ml_models')
      .select('model_gcs_path, updated_at')
      .eq('tenant_id', tenantId)
      .eq('model_name', modelName)
      .order('updated_at', { ascending: false })
      .limit(1)
      .single();

    if (error || !data) {
      console.error('Error retrieving model info:', error);
      return null;
    }

    return data.model_gcs_path;
  } catch (_error) {
    console.error('Failed to get latest model path:', error);
    return null;
  }
}

/**
 * Downloads a model from GCS
 * @param gcsPath GCS path to the model
 * @returns ArrayBuffer containing the model
 */
export async function downloadModelFromGCS(gcsPath: string): Promise<ArrayBuffer | null> {
  try {
    // Initialize GCS client
    const storage = new Storage({
      keyFilename: process.env.GCS_SERVICE_ACCOUNT_FILE,
    });
    const bucket = storage.bucket(process.env.GCS_BUCKET_NAME!);
    const file = bucket.file(gcsPath);

    // Download file
    const [fileContent] = await file.download();
    return fileContent.buffer as ArrayBuffer;
  } catch (_error) {
    console.error('Error downloading model from GCS:', error);
    return null;
  }
}

/**
 * Loads an ONNX model from GCS
 * @param tenantId Tenant ID
 * @param modelName Model name
 * @param forceReload Whether to force reload the model even if cached
 * @returns Inference session or null if loading failed
 */
export async function loadModel(
  tenantId: string,
  modelName = 'activity_prioritization',
  forceReload = false
): Promise<ort.InferenceSession | null> {
  const cacheKey = `${tenantId}_${modelName}`;

  // Check cache first unless force reload is requested
  if (!forceReload && modelCache.has(cacheKey)) {
    const cachedModel = modelCache.get(cacheKey)!;
    if (cachedModel.session) {
      return cachedModel.session;
    }
  }

  // Get latest model path
  const modelPath = await getLatestModelPath(tenantId, modelName);
  if (!modelPath) {
    console.error('No model found for tenant:', tenantId);
    return null;
  }

  try {
    // Download model from GCS
    const modelBuffer = await downloadModelFromGCS(modelPath);
    if (!modelBuffer) {
      throw new Error('Failed to download model');
    }

    // Create ONNX session
    const session = await ort.InferenceSession.create(modelBuffer as unknown as string);

    // Update cache
    modelCache.set(cacheKey, {
      modelPath,
      lastUpdated: new Date(),
      session
    });

    return session;
  } catch (_error) {
    console.error('Error loading ONNX model:', error);
    return null;
  }
}

/**
 * Run inference on activity features to predict priority
 * @param session ONNX inference session
 * @param features Extracted activity features
 * @returns Predicted priority level
 */
export async function predictPriority(
  session: ort.InferenceSession,
  features: ActivityFeatures
): Promise<PriorityLevel> {
  try {
    // Convert features to a flat array in the expected order
    // This order must match the training input order
    const featureArray = [
      features.daysSinceCreation,
      features.daysSinceLastUpdate,
      features.daysToDeadline,
      features.hasDeadline ? 1 : 0,
      features.hasStatutoryLimit ? 1 : 0,
      features.isUrgent ? 1 : 0,
      features.viewCount,
      features.interactionCount,
      features.completionPercentage,
      features.isDueToday ? 1 : 0,
      features.isDueTomorrow ? 1 : 0,
      features.isOverdue ? 1 : 0
    ];

    // Create input tensor
    // Shape [1, N] where N is the number of features
    const tensorData = new Float32Array(featureArray);
    const inputTensor = new ort.Tensor('float32', tensorData, [1, featureArray.length]);

    // Run inference
    const feeds = { input: inputTensor };
    const results = await session.run(feeds);

    // Get output (assuming output tensor name is 'output')
    // This will contain class probabilities for Low, Medium, High
    const outputTensor = results.output;
    const predictions = outputTensor.data as Float32Array;

    // Find the highest probability class
    let maxIndex = 0;
    let maxProb = predictions[0];

    for (let i = 1; i < predictions.length; i++) {
      if (predictions[i] > maxProb) {
        maxProb = predictions[i];
        maxIndex = i;
      }
    }

    // Map index to priority level (assuming the model outputs in order: Low, Medium, High)
    switch (maxIndex) {
      case 0: return PriorityLevel.LOW;
      case 1: return PriorityLevel.MEDIUM;
      case 2: return PriorityLevel.HIGH;
      default: return PriorityLevel.LOW; // Default fallback
    }
  } catch (_error) {
    console.error('Error during model inference:', error);
    // Return medium priority as a sensible default when inference fails
    return PriorityLevel.MEDIUM;
  }
}

/**
 * Main function to get priority prediction for an activity
 * Handles model loading and inference
 * @param tenantId Tenant ID
 * @param features Activity features
 * @returns Predicted priority level
 */
export async function getPriorityPrediction(
  tenantId: string,
  features: ActivityFeatures
): Promise<PriorityLevel> {
  try {
    // Load model
    const session = await loadModel(tenantId);
    if (!session) {
      console.warn('Using fallback priority since model failed to load');
      return getFallbackPriority(features);
    }

    // Predict priority
    return await predictPriority(session, features);
  } catch (_error) {
    console.error('Error predicting priority:', error);
    return getFallbackPriority(features);
  }
}

/**
 * Rule-based fallback when ML inference fails
 * @param features Activity features
 * @returns Priority based on basic rules
 */
function getFallbackPriority(features: ActivityFeatures): PriorityLevel {
  // Simple rule-based system as fallback
  if (
    features.isUrgent ||
    features.isOverdue ||
    features.isDueToday ||
    (features.hasDeadline && features.daysToDeadline <= 3) ||
    features.hasStatutoryLimit
  ) {
    return PriorityLevel.HIGH;
  }

  if (
    features.isDueTomorrow ||
    (features.hasDeadline && features.daysToDeadline <= 7) ||
    (features.daysSinceLastUpdate > 14 && features.completionPercentage < 50)
  ) {
    return PriorityLevel.MEDIUM;
  }

  return PriorityLevel.LOW;
}

/**
 * Simple function to get priority score (0-1) from priority level
 * Useful for sorting activities
 */
export function getPriorityScore(priority: PriorityLevel): number {
  switch (priority) {
    case PriorityLevel.HIGH: return 1.0;
    case PriorityLevel.MEDIUM: return 0.5;
    case PriorityLevel.LOW: return 0.1;
    default: return 0;
  }
}
