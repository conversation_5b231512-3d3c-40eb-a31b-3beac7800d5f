/**
 * Neo4j driver setup and management
 * Provides a singleton driver instance for Neo4j connections
 */

import neo4j, { Driver } from 'neo4j-driver';

// Environment variables
const NEO4J_URI = process.env.NEO4J_URI || 'bolt://localhost:7687';
const NEO4J_USER = process.env.NEO4J_USER || 'neo4j';
const NEO4J_PASSWORD = process.env.NEO4J_PASSWORD || 'neo4j';
const ENABLE_NEO4J = process.env.ENABLE_NEO4J === 'true';

// Singleton driver instance
let driver: Driver | null = null;

/**
 * Get or create a Neo4j driver instance
 * @returns Neo4j driver or null if disabled/unavailable
 */
export function getDriver(): Driver | null {
  // Return null if Neo4j is disabled
  if (!ENABLE_NEO4J) {
    return null;
  }

  // Return existing driver if available
  if (driver) {
    return driver;
  }

  // Create a new driver
  try {
    driver = neo4j.driver(
      NEO4J_URI,
      neo4j.auth.basic(NEO4J_USER, NEO4J_PASSWORD),
      {
        maxConnectionPoolSize: 50,
        connectionAcquisitionTimeout: 5000,
        disableLosslessIntegers: true
      }
    );

    console.log('Neo4j driver initialized successfully');

    // Add cleanup on process exit (for server environments)
    if (typeof process !== 'undefined') {
      process.on('exit', () => {
        if (driver) {
          driver.close();
        }
      });
    }

    return driver;
  } catch (_error) {
    console.error('Failed to create Neo4j driver:', error);
    return null;
  }
}

/**
 * Close the Neo4j driver connection
 * Should be called when the application is shutting down
 */
export async function closeDriver(): Promise<void> {
  if (driver) {
    try {
      await driver.close();
      driver = null;
      console.log('Neo4j driver closed successfully');
    } catch (_error) {
      console.error('Error closing Neo4j driver:', error);
    }
  }
}

/**
 * Verify Neo4j connection
 * @returns Boolean indicating if the connection is successful
 */
export async function verifyConnection(): Promise<boolean> {
  const driver = getDriver();
  if (!driver) {
    return false;
  }

  const session = driver.session();

  try {
    // Simple query to verify connection
    const result = await session.run('RETURN 1 as n');
    return result.records[0].get('n') === 1;
  } catch (_error) {
    console.error('Neo4j connection verification failed:', error);
    return false;
  } finally {
    await session.close();
  }
}
