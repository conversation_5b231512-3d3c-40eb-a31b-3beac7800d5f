/**
 * Neo4j Client Helper
 *
 * Provides utility functions for interacting with Neo4j database via MCP server
 */

import { AuthUser } from '../types';
import { executeCypherQuery, executeCypherWrite } from './mcp-client';

/**
 * Log a user activity to Neo4j
 *
 * @param userId The ID of the user performing the activity
 * @param tenantId The tenant ID for the user
 * @param activityType Type of activity (e.g., 'DOCUMENT_UPLOAD', 'EMAIL_SENT', 'CASE_VIEWED')
 * @param llmSummary LLM-generated summary of the activity
 * @param importance LLM-assigned importance ('high', 'medium', 'low')
 * @param tags LLM-assigned tags
 * @param caseId The ID of the case related to the activity (optional)
 * @param documentId The ID of the document related to the activity (optional)
 * @param contactId The ID of the contact related to the activity (optional)
 * @param metadata Additional metadata about the activity
 * @returns Promise resolving to the ID of the created activity node
 */
export async function logUserActivity({
  userId,
  tenantId,
  activityType,
  llmSummary,
  importance,
  tags,
  caseId,
  documentId,
  contactId,
  metadata = {}
}: {
  userId: string;
  tenantId: string;
  activityType: string;
  llmSummary: string;
  importance: 'high' | 'medium' | 'low';
  tags: string[];
  caseId?: string;
  documentId?: string;
  contactId?: string;
  metadata?: Record<string, any>;
}): Promise<string> {
  try {
    const activityTimestamp = new Date().toISOString();

    const cypher = `
      MERGE (u:User {id: $userId})
      MERGE (at:ActivityType {name: $activityTypeName})
      CREATE (a:Activity {
          id: randomUUID(),
          type: $activityType,
          timestamp: datetime($timestamp),
          summary: $llmSummary,
          importance: $importance,
          tags: $tags,
          tenant_id: $tenantId,
          document_id: $documentId,
          case_id: $caseId,
          contact_id: $contactId,
          metadata: $metadata
      })
      CREATE (u)-[:PERFORMED]->(a)
      CREATE (a)-[:OF_TYPE]->(at)
      ${caseId ? 'WITH a MATCH (c:Case {id: $caseId}) CREATE (a)-[:ON_CASE]->(c)' : ''}
      ${documentId ? 'WITH a MATCH (d:Document {id: $documentId}) CREATE (a)-[:REFERENCES]->(d)' : ''}
      ${contactId ? 'WITH a MATCH (ct:Contact {id: $contactId}) CREATE (a)-[:TO]->(ct)' : ''}
      RETURN a.id as activityId
    `;

    const params = {
      userId,
      activityTypeName: activityType,
      activityType,
      timestamp: activityTimestamp,
      llmSummary,
      importance,
      tags,
      tenantId,
      caseId: caseId || null,
      documentId: documentId || null,
      contactId: contactId || null,
      metadata: metadata || {}
    };

    // Make the actual call to Neo4j via the MCP client
    const result = await executeCypherWrite(cypher, params);

    if (result.error) {
      throw new Error(`Neo4j error: ${result.error}`);
    }

    // Extract the activity ID from the result
    const activityId = result.data?.results?.[0]?.activityId || 'unknown-activity-id';
    return activityId;
  } catch (_error) {
    console.error('Failed to log user activity to Neo4j:', error);
    // Don't throw the error - we don't want activity logging to break the main workflow
    return 'failed-activity-logging';
  }
}

/**
 * Get recent user activities from Neo4j
 *
 * @param userId The ID of the user
 * @param daysBack Number of days to look back (default: 7)
 * @param limit Maximum number of activities to return (default: 5)
 * @returns Promise resolving to array of recent activities
 */
export async function getRecentUserActivities({
  userId,
  daysBack = 7,
  limit = 5
}: {
  userId: string;
  daysBack?: number;
  limit?: number;
}): Promise<any[]> {
  try {
    const cypher = `
      MATCH (u:User {id: $userId})-[:PERFORMED]->(a:Activity)
      WHERE a.timestamp >= datetime() - duration('P${daysBack}D')
      // Optionally match related entities like Case, Document for context
      OPTIONAL MATCH (a)-[:ON_CASE]->(c:Case)
      OPTIONAL MATCH (a)-[:REFERENCES]->(d:Document)
      RETURN a.timestamp AS time,
             a.summary AS summary,
             a.type AS actionType,
             a.importance AS importance,
             a.tags AS tags,
             c.title AS caseTitle,
             d.name AS documentName,
             a.id AS activityId,
             a.metadata AS metadata
      ORDER BY a.timestamp DESC
      LIMIT $limit
    `;

    const params = {
      userId,
      limit
    };

    // Make the actual call to Neo4j via the MCP client
    const result = await executeCypherQuery(cypher, params);

    if (result.error) {
      console.error('Neo4j query error:', result.error);
      return [];
    }

    // Return the activity data from the result
    return result.data?.results || [];
  } catch (_error) {
    console.error('Failed to get recent user activities from Neo4j:', error);
    return [];
  }
}

/**
 * Log user feedback on insights to Neo4j
 *
 * @param userId The ID of the user providing feedback
 * @param feedbackId The unique ID for this feedback instance
 * @param insightId The ID of the insight being rated
 * @param action The action taken (clicked, dismissed, rated)
 * @param rating Optional rating (1-5)
 * @param comment Optional user comment
 * @param timestamp When the feedback was provided
 * @returns Promise resolving to the ID of the created feedback node
 */
export async function logUserFeedback({
  userId,
  feedbackId,
  insightId,
  action,
  rating,
  comment,
  timestamp
}: {
  userId: string;
  feedbackId: string;
  insightId: string;
  action: 'clicked' | 'dismissed' | 'rated' | string;
  rating?: number;
  comment?: string;
  timestamp: string;
}): Promise<string> {
  try {
    const cypher = `
      MERGE (u:User {id: $userId})
      CREATE (f:InsightFeedback {
          id: $feedbackId,
          insight_id: $insightId,
          action: $action,
          rating: $rating,
          comment: $comment,
          timestamp: datetime($timestamp)
      })
      CREATE (u)-[:PROVIDED]->(f)
      RETURN f.id as feedbackId
    `;

    const params = {
      userId,
      feedbackId,
      insightId,
      action,
      rating: rating || null,
      comment: comment || null,
      timestamp
    };

    // Make the actual call to Neo4j via the MCP client
    const result = await executeCypherWrite(cypher, params);

    if (result.error) {
      throw new Error(`Neo4j error: ${result.error}`);
    }

    // Extract the feedback ID from the result
    return result.data?.results?.[0]?.feedbackId || feedbackId;
  } catch (_error) {
    console.error('Failed to log user feedback to Neo4j:', error);
    // Don't throw the error - we don't want feedback logging to break the main workflow
    return feedbackId;
  }
}

/**
 * Log an activity to Neo4j for relationship analysis
 * 
 * @param entityType Type of entity (e.g., 'case', 'document', 'user')
 * @param entityId ID of the entity
 * @param actionType Action performed (e.g., 'created', 'viewed', 'downloaded')
 * @param relatedEntityType Type of related entity (optional)
 * @param relatedEntityId ID of related entity (optional)
 * @param metadata Additional metadata about the activity
 * @returns Promise resolving to success status
 */
export async function logActivityToNeo4j({
  entityType,
  entityId,
  actionType,
  userId,
  tenantId,
  relatedEntityType,
  relatedEntityId,
  metadata = {}
}: {
  entityType: string;
  entityId: string;
  actionType: string;
  userId: string;
  tenantId: string;
  relatedEntityType?: string;
  relatedEntityId?: string;
  metadata?: Record<string, any>;
}): Promise<boolean> {
  try {
    const activityTimestamp = new Date().toISOString();
    
    // Base query that creates the primary entity and action relationship
    let cypher = `
      MERGE (u:User {id: $userId})
      MERGE (e:${entityType.charAt(0).toUpperCase() + entityType.slice(1)} {id: $entityId})
      CREATE (a:Activity {
          id: randomUUID(),
          action: $actionType,
          timestamp: datetime($timestamp),
          tenant_id: $tenantId,
          metadata: $metadata
      })
      CREATE (u)-[:PERFORMED]->(a)-[:TO]->(e)
    `;
    
    // Add related entity if provided
    if (relatedEntityType && relatedEntityId) {
      cypher += `
        WITH a, e
        MERGE (r:${relatedEntityType.charAt(0).toUpperCase() + relatedEntityType.slice(1)} {id: $relatedEntityId})
        CREATE (e)-[:RELATED_TO {via: $actionType}]->(r)
      `;
    }
    
    cypher += `
      RETURN a.id as activityId
    `;

    const params = {
      userId,
      entityId,
      actionType,
      timestamp: activityTimestamp,
      tenantId,
      relatedEntityId: relatedEntityId || null,
      metadata: metadata || {}
    };

    // Make the actual call to Neo4j via the MCP client
    const result = await executeCypherWrite(cypher, params);

    if (result.error) {
      console.error(`Neo4j error in logActivityToNeo4j: ${result.error}`);
      return false;
    }

    return true;
  } catch (_error) {
    console.error('Failed to log activity to Neo4j:', error);
    // Don't throw the error - logging shouldn't break main workflow
    return false;
  }
}
