/**
 * Staff intake-related actions for CopilotKit
 * Implements AG-UI protocol format exclusively for v2.x
 */
import { useCopilotAction } from "@copilotkit/react-core";
import { validateActionInput, createActionSuccess, ensureRequiredFields } from '../utils/ag-actions';
import { z } from "zod";

// Schema for staff intake data
export const StaffIntakeSchema = z.object({
  clientName: z.string(),
  practiceArea: z.string(),
  caseType: z.string(),
  details: z.string(),
});

export type StaffIntakeData = z.infer<typeof StaffIntakeSchema>;

// Parameter interface for staff intake action
interface StaffIntakeParams {
  clientName: string;
  practiceArea: string;
  caseType: string;
  details: string;
}

/**
 * Hook to register the staff intake form action with CopilotKit
 * Accepts a callback function to handle the form data
 */
export function useStaffIntakeAction(
  handleIntake: (data: StaffIntakeData) => Promise<void> | void
) {
  useCopilotAction({
    name: 'submitIntake',
    description: 'Submit a new client intake form',
    parameters: [{
      name: 'clientName',
      type: 'string',
      description: 'Full name of the client'
    }, {
      name: 'practiceArea',
      type: 'string',
      description: 'Practice area (personal_injury, criminal_defense, family_law)'
    }, {
      name: 'caseType',
      type: 'string',
      description: 'Specific type of case within the practice area'
    }, {
      name: 'details',
      type: 'string',
      description: 'Brief description of the case'
    }],
    handler: async (args: any) => {
      try {
        // Ensure required fields are present
        ensureRequiredFields(args, ['clientName', 'practiceArea', 'caseType', 'details']);
        
        // Validate the intake data against schema
        const validatedData = validateActionInput(args, StaffIntakeSchema) as StaffIntakeData;

        // Call the handler function with validated data
        await handleIntake(validatedData);
        
        // Return success message
        return createActionSuccess(`Intake form for ${args.clientName} has been populated`);
      } catch (_error) {
        // Handle errors during validation or intake submission
        console.error('Error in submitIntake action:', error);
        throw new Error(error instanceof Error ? error.message : 'Unknown error in intake submission');
      }
    }
  });
}
