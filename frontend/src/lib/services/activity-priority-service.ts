/**
 * Activity Priority Service
 *
 * This service handles activity prioritization using the trained ML model.
 * It provides both client-side inference using ONNX Runtime Web and
 * fallback mechanisms when the model cannot be loaded.
 */
import * as ort from 'onnxruntime-web';
import { ActivityFeatures, extractActivityFeatures, Activity } from '../ml/features';
import { PriorityLevel } from '../ml/modelInference';

// Rule-based fallback when ML inference fails
function getFallbackPriority(features: ActivityFeatures): PriorityLevel {
  // Simple rule-based system as fallback
  if (
    features.isUrgent ||
    features.isOverdue ||
    features.isDueToday ||
    (features.hasDeadline && features.daysToDeadline <= 3) ||
    features.hasStatutoryLimit
  ) {
    return PriorityLevel.HIGH;
  }

  if (
    features.isDueTomorrow ||
    (features.hasDeadline && features.daysToDeadline <= 7) ||
    (features.daysSinceLastUpdate > 14 && features.completionPercentage < 50)
  ) {
    return PriorityLevel.MEDIUM;
  }

  return PriorityLevel.LOW;
}

// Model paths
const LOCAL_MODEL_PATH = '/models/activity_classifier.onnx';

// Service class for activity prioritization
export class ActivityPriorityService {
  private session: ort.InferenceSession | null = null;
  private modelLoaded = false;
  private loadPromise: Promise<void> | null = null;

  /**
   * Initialize the model
   * This loads the ONNX model for browser-based inference
   */
  async initialize(): Promise<void> {
    if (this.loadPromise) {
      return this.loadPromise;
    }

    if (this.modelLoaded) {
      return Promise.resolve();
    }

    this.loadPromise = new Promise<void>(async (resolve) => {
      try {
        console.log('Loading activity priority model...');

        // Set WebAssembly execution provider
        const options = {
          executionProviders: ['wasm']
        };

        // Load the model
        this.session = await ort.InferenceSession.create(
          LOCAL_MODEL_PATH,
          options
        );

        this.modelLoaded = true;
        console.log('Activity priority model loaded successfully');
        resolve();
      } catch (_error) {
        console.error('Failed to load activity priority model:', error);
        this.modelLoaded = false;
        this.session = null;
        resolve(); // Resolve anyway to prevent hanging promises
      } finally {
        this.loadPromise = null;
      }
    });

    return this.loadPromise;
  }

  /**
   * Predict priority for an activity
   * @param activity The activity to predict priority for
   * @returns Predicted priority level
   */
  async predictPriority(activity: Activity): Promise<PriorityLevel> {
    // Ensure model is loaded
    if (!this.modelLoaded || !this.session) {
      try {
        await this.initialize();
      } catch (_error) {
        console.error('Error initializing model:', error);
      }
    }

    try {
      // Extract features
      const features = await extractActivityFeatures(activity);

      // If model still not loaded, use fallback
      if (!this.session) {
        console.warn('Using fallback priority calculation');
        return this.getFallbackPriority(features);
      }

      // Convert features to a flat array in the expected order
      const featureArray = this.featuresToArray(features);

      // Create input tensor
      const inputTensor = new ort.Tensor(
        'float32',
        new Float32Array(featureArray),
        [1, featureArray.length]
      );

      // Run inference
      const feeds = { input: inputTensor };
      const results = await this.session.run(feeds);

      // Get prediction
      const outputData = results.label.data as Float32Array;
      const predictedClass = Math.round(outputData[0]);

      // Map to priority level
      switch (predictedClass) {
        case 0: return PriorityLevel.LOW;
        case 1: return PriorityLevel.MEDIUM;
        case 2: return PriorityLevel.HIGH;
        default: return PriorityLevel.MEDIUM;
      }
    } catch (_error) {
      console.error('Error predicting activity priority:', error);

      // Extract features for fallback
      try {
        const features = await extractActivityFeatures(activity);
        return this.getFallbackPriority(features);
      } catch (featureError) {
        console.error('Error extracting features:', featureError);
        // Default to Medium priority on error
        return PriorityLevel.MEDIUM;
      }
    }
  }

  /**
   * Convert features object to array in the correct order for model input
   */
  private featuresToArray(features: ActivityFeatures): number[] {
    return [
      features.daysSinceCreation,
      features.daysSinceLastUpdate,
      features.daysToDeadline,
      features.hasDeadline ? 1 : 0,
      features.hasStatutoryLimit ? 1 : 0,
      features.isUrgent ? 1 : 0,
      features.viewCount,
      features.interactionCount,
      features.completionPercentage,
      features.isDueToday ? 1 : 0,
      features.isDueTomorrow ? 1 : 0,
      features.isOverdue ? 1 : 0
    ];
  }

  /**
   * Rule-based fallback when ML inference fails
   */
  private getFallbackPriority(features: ActivityFeatures): PriorityLevel {
    return getFallbackPriority(features);
  }

  /**
   * Get a numeric score for a priority level (for sorting)
   */
  getPriorityScore(priority: PriorityLevel): number {
    switch (priority) {
      case PriorityLevel.HIGH: return 1.0;
      case PriorityLevel.MEDIUM: return 0.5;
      case PriorityLevel.LOW: return 0.1;
      default: return 0;
    }
  }

  /**
   * Get the CSS class for a priority level
   */
  getPriorityClass(priority: PriorityLevel): string {
    switch (priority) {
      case PriorityLevel.HIGH: return 'priority-high';
      case PriorityLevel.MEDIUM: return 'priority-medium';
      case PriorityLevel.LOW: return 'priority-low';
      default: return 'priority-medium';
    }
  }
}

// Create singleton instance
export const activityPriorityService = new ActivityPriorityService();

// Export types
export { PriorityLevel };
