import { SupabaseClient } from '@supabase/supabase-js';
import { Database, Json } from '../supabase/database.types';
import { toJsonSafe, fromJsonSafe } from '../utils/type-converters';
import { setMetadata } from '../utils/subscription-utils';
import {
  SubscriptionPlan,
  SubscriptionAddon,
  TenantSubscription,
  TenantAddon
} from '../supabase/subscription.types';
import { createSubscriptionClient } from './subscription-client';

export interface SubscriptionPlanDTO {
  id: string;
  name: string;
  code: string;
  description: string | null;
  isActive: boolean | null;
  isPublic: boolean | null;
  basePriceMonthly: number;
  basePriceYearly: number;
  features: Json | null;
  createdAt: string | null;
  updatedAt: string | null;
}

export interface SubscriptionAddonDTO {
  id: string;
  name: string;
  code: string;
  description: string | null;
  category: string;
  isActive: boolean | null;
  priceMonthly: number;
  priceYearly: number;
  features: Json | null;
  createdAt: string | null;
  updatedAt: string | null;
}

export interface TenantSubscriptionDTO {
  id: string;
  tenantId: string;
  planId: string;
  status: 'active' | 'past_due' | 'canceled' | 'trialing' | 'inactive';
  billingCycle: 'monthly' | 'yearly';
  trialStart: string | null;
  trialEnd: string | null;
  currentPeriodStart: string;
  currentPeriodEnd: string;
  canceledAt: string | null;
  paymentProvider: string | null;
  paymentProviderSubscriptionId: string | null;
  metadata: Json | null;
  createdAt: string | null;
  updatedAt: string | null;
  plan?: {
    name: string;
    code: string;
    basePriceMonthly: number;
    basePriceYearly: number;
    features: Json | null;
  };
  addons?: TenantAddonDTO[];
}

export interface TenantAddonDTO {
  id: string;
  tenantId: string;
  subscriptionId: string;
  addonId: string;
  status: 'active' | 'canceled';
  quantity: number;
  currentPeriodStart: string;
  currentPeriodEnd: string;
  canceledAt: string | null;
  metadata: Json | null;
  createdAt: string | null;
  updatedAt: string | null;
  addon?: {
    name: string;
    code: string;
    category: string;
    priceMonthly: number;
    priceYearly: number;
    features: Json | null;
  };
}

export interface CreateSubscriptionParams {
  tenantId: string;
  planId: string;
  billingCycle?: 'monthly' | 'yearly';
  trialDays?: number;
  metadata?: Record<string, unknown>;
  paymentProvider?: string;
  paymentProviderSubscriptionId?: string;
}

export interface UpdateSubscriptionParams {
  planId?: string;
  status?: 'active' | 'past_due' | 'canceled' | 'trialing' | 'inactive';
  billingCycle?: 'monthly' | 'yearly';
  trialEnd?: string | null;
  currentPeriodStart?: string;
  currentPeriodEnd?: string;
  canceledAt?: string | null;
  paymentProvider?: string | null;
  paymentProviderSubscriptionId?: string | null;
  metadata?: Record<string, unknown>;
}

export interface AddAddonParams {
  tenantId: string;
  subscriptionId: string;
  addonId: string;
  quantity?: number;
  metadata?: Record<string, unknown>;
}

export interface UpdateAddonParams {
  status?: 'active' | 'canceled';
  quantity?: number;
  canceledAt?: string | null;
  metadata?: Record<string, unknown>;
}

/**
 * Service for managing subscriptions
 */
export class SubscriptionService {
  private subscriptionClient;

  constructor(private supabase: SupabaseClient<Database>) {
    this.subscriptionClient = createSubscriptionClient(supabase);
  }

  /**
   * Get all subscription plans
   * @param includeInactive Whether to include inactive plans
   * @returns List of subscription plans
   */
  async getSubscriptionPlans(includeInactive: boolean = false): Promise<SubscriptionPlanDTO[]> {
    return this.getPlans(includeInactive);
  }

  /**
   * Get all subscription addons
   * @param includeInactive Whether to include inactive addons
   * @returns List of subscription addons
   */
  async getSubscriptionAddons(includeInactive: boolean = false): Promise<SubscriptionAddonDTO[]> {
    return this.getAddons(includeInactive);
  }

  /**
   * Get all subscription addons (alias for backward compatibility)
   * @param includeInactive Whether to include inactive addons
   * @returns List of subscription addons
   */
  async getAddons(includeInactive: boolean = false): Promise<SubscriptionAddonDTO[]> {
    const addons = await this.subscriptionClient.getSubscriptionAddons(includeInactive);

    // Process each addon with type assertion to handle the complex typing
    return addons.map(addon => {
      // Cast to the right type to avoid TypeScript errors
      const typedAddon = addon as unknown as SubscriptionAddon;

      return {
        id: typedAddon.id,
        name: typedAddon.name,
        code: typedAddon.code,
        description: typedAddon.description,
        category: typedAddon.category,
        isActive: typedAddon.is_active, // Reverted: Allow null to pass through
        priceMonthly: typedAddon.price_monthly,
        priceYearly: typedAddon.price_yearly,
        features: typedAddon.features,
        createdAt: typedAddon.created_at,
        updatedAt: typedAddon.updated_at,
      };
    });
  }

  /**
   * Get all subscription plans (alias for backward compatibility)
   * @param includeInactive Whether to include inactive plans
   * @returns List of subscription plans
   */
  async getPlans(includeInactive: boolean = false): Promise<SubscriptionPlanDTO[]> {
    const plans = await this.subscriptionClient.getSubscriptionPlans(includeInactive);

    // Process each plan with type assertion to handle the complex typing
    return plans.map(plan => {
      // Cast to the right type to avoid TypeScript errors
      const typedPlan = plan as unknown as SubscriptionPlan;

      return {
        id: typedPlan.id,
        name: typedPlan.name,
        code: typedPlan.code,
        description: typedPlan.description,
        isActive: typedPlan.is_active ?? false,
        isPublic: typedPlan.is_public ?? false,
        basePriceMonthly: typedPlan.base_price_monthly,
        basePriceYearly: typedPlan.base_price_yearly,
        features: typedPlan.features,
        createdAt: typedPlan.created_at,
        updatedAt: typedPlan.updated_at,
      };
    });
  }

  /**
   * Get a subscription plan by ID
   * @param planId The plan ID
   * @returns The subscription plan
   */
  async getSubscriptionPlan(planId: string): Promise<SubscriptionPlanDTO | null> {
    const plan = await this.subscriptionClient.getSubscriptionPlan(planId);

    if (!plan) return null;

    // Cast to the right type to avoid TypeScript errors
    const typedPlan = plan as unknown as SubscriptionPlan;

    return {
      id: typedPlan.id,
      name: typedPlan.name,
      code: typedPlan.code,
      description: typedPlan.description,
      isActive: typedPlan.is_active ?? false,
      isPublic: typedPlan.is_public ?? false,
      basePriceMonthly: typedPlan.base_price_monthly,
      basePriceYearly: typedPlan.base_price_yearly,
      features: typedPlan.features,
      createdAt: typedPlan.created_at,
      updatedAt: typedPlan.updated_at,
    };
  }

  // This method was a duplicate of getAddons() and has been removed

  /**
   * Get a subscription addon by ID
   * @param addonId The addon ID
   * @returns The subscription addon
   */
  async getSubscriptionAddon(addonId: string): Promise<SubscriptionAddonDTO | null> {
    const addon = await this.subscriptionClient.getSubscriptionAddon(addonId);

    if (!addon) {
      return null;
    }

    // Cast to the right type to avoid TypeScript errors
    const typedAddon = addon as unknown as SubscriptionAddon;

    return {
      id: typedAddon.id,
      name: typedAddon.name,
      code: typedAddon.code,
      description: typedAddon.description,
      category: typedAddon.category,
      isActive: typedAddon.is_active, // Reverted: Allow null to pass through
      priceMonthly: typedAddon.price_monthly,
      priceYearly: typedAddon.price_yearly,
      features: typedAddon.features,
      createdAt: typedAddon.created_at,
      updatedAt: typedAddon.updated_at,
    };
  }

  /**
   * Get a tenant's subscription
   * @param tenantId The tenant ID
   * @returns The tenant's subscription
   */
  async getTenantSubscription(tenantId: string): Promise<TenantSubscriptionDTO | null> {
    // Use the subscription client to get the tenant's subscription
    const subscription = await this.subscriptionClient.getTenantSubscription(tenantId);

    if (!subscription) {
      return null;
    }

    // Get addons for this subscription
    const addons = await this.subscriptionClient.getTenantAddons(tenantId, subscription.id);

    // Cast to the right types to avoid TypeScript errors
    const typedSubscription = subscription as unknown as TenantSubscription & {
      subscription_plans: SubscriptionPlan
    };

    // Map addons to the DTO format with type assertions
    const mappedAddons = addons.map(addon => {
      const typedAddon = addon as unknown as TenantAddon & {
        subscription_addons: SubscriptionAddon
      };

      return {
        id: typedAddon.id,
        tenantId: typedAddon.tenant_id,
        subscriptionId: typedAddon.subscription_id,
        addonId: typedAddon.addon_id,
        status: typedAddon.status as TenantAddonDTO['status'],
        quantity: typedAddon.quantity,
        currentPeriodStart: typedAddon.current_period_start,
        currentPeriodEnd: typedAddon.current_period_end,
        canceledAt: typedAddon.canceled_at,
        metadata: typedAddon.metadata as Json | null,
        createdAt: typedAddon.created_at,
        updatedAt: typedAddon.updated_at,
        addon: {
          name: typedAddon.subscription_addons.name,
          code: typedAddon.subscription_addons.code,
          category: typedAddon.subscription_addons.category,
          priceMonthly: typedAddon.subscription_addons.price_monthly,
          priceYearly: typedAddon.subscription_addons.price_yearly,
          features: typedAddon.subscription_addons.features,
        },
      };
    });

    // Map subscription to the DTO format
    return {
      id: typedSubscription.id,
      tenantId: typedSubscription.tenant_id,
      planId: typedSubscription.plan_id,
      status: typedSubscription.status as TenantSubscriptionDTO['status'],
      billingCycle: typedSubscription.billing_cycle as TenantSubscriptionDTO['billingCycle'],
      trialStart: typedSubscription.trial_start,
      trialEnd: typedSubscription.trial_end,
      currentPeriodStart: typedSubscription.current_period_start,
      currentPeriodEnd: typedSubscription.current_period_end,
      canceledAt: typedSubscription.canceled_at,
      paymentProvider: typedSubscription.payment_provider,
      paymentProviderSubscriptionId: typedSubscription.payment_provider_subscription_id,
      metadata: typedSubscription.metadata as Json | null,
      createdAt: typedSubscription.created_at,
      updatedAt: typedSubscription.updated_at,
      plan: {
        name: typedSubscription.subscription_plans.name,
        code: typedSubscription.subscription_plans.code,
        basePriceMonthly: typedSubscription.subscription_plans.base_price_monthly,
        basePriceYearly: typedSubscription.subscription_plans.base_price_yearly,
        features: typedSubscription.subscription_plans.features,
      },
      addons: mappedAddons,
    };
  }

  /**
   * Check if a tenant has an active subscription
   * @param tenantId The tenant ID
   * @returns Whether the tenant has an active subscription
   */
  async hasTenantActiveSubscription(tenantId: string): Promise<boolean> {
    return this.subscriptionClient.hasTenantActiveSubscription(tenantId);
  }

  /**
   * Create a new subscription for a tenant
   * @param params The subscription parameters
   * @returns The created subscription
   */
  /**
   * Change the subscription plan for a tenant
   * @param tenantId The tenant ID
   * @param planId The new plan ID
   * @param billingCycle Optional billing cycle change
   * @returns The updated subscription
   */
  async changePlan(
    tenantId: string,
    planId: string,
    billingCycle?: 'monthly' | 'yearly'
  ): Promise<TenantSubscriptionDTO> {
    // First get the current subscription
    const currentSubscription = await this.getTenantSubscription(tenantId);

    if (!currentSubscription) {
      throw new Error('Tenant has no subscription to update');
    }

    // Update the subscription with the new plan
    return this.updateSubscription(currentSubscription.id, {
      planId,
      billingCycle: billingCycle || currentSubscription.billingCycle,
    });
  }

  /**
   * Create a trial subscription for a tenant
   * @param tenantId The tenant ID
   * @param planId The plan ID
   * @param trialDays Number of trial days
   * @returns The created subscription
   */
  async createTrialSubscription(
    tenantId: string,
    planId: string,
    trialDays: number = 14
  ): Promise<TenantSubscriptionDTO> {
    return this.createSubscription({
      tenantId,
      planId,
      trialDays,
      billingCycle: 'monthly',
    });
  }

  async createSubscription(params: CreateSubscriptionParams): Promise<TenantSubscriptionDTO> {
    const now = new Date();
    const trialDays = params.trialDays || 0;

    let trialStart = null;
    let trialEnd = null;
    let status = 'active';

    if (trialDays > 0) {
      trialStart = now.toISOString();
      const trialEndDate = new Date(now);
      trialEndDate.setDate(trialEndDate.getDate() + trialDays);
      trialEnd = trialEndDate.toISOString();
      status = 'trialing';
    }

    // Calculate billing period
    const billingCycle = params.billingCycle || 'monthly';
    const currentPeriodStart = now.toISOString();
    const currentPeriodEnd = new Date(now);

    if (billingCycle === 'monthly') {
      currentPeriodEnd.setMonth(currentPeriodEnd.getMonth() + 1);
    } else {
      currentPeriodEnd.setFullYear(currentPeriodEnd.getFullYear() + 1);
    }

    // Use the subscription client to create the subscription
    const subscription = await this.subscriptionClient.createSubscription({
      id: undefined, // TypeScript will remove this during object spread for the Omit type
      tenant_id: params.tenantId,
      plan_id: params.planId,
      status: status as TenantSubscription['status'],
      billing_cycle: billingCycle as TenantSubscription['billing_cycle'],
      trial_start: trialStart,
      trial_end: trialEnd,
      current_period_start: currentPeriodStart,
      current_period_end: currentPeriodEnd.toISOString(),
      canceled_at: null,
      payment_provider: params.paymentProvider || null,
      payment_provider_subscription_id: params.paymentProviderSubscriptionId || null,
      metadata: params.metadata || {},
      created_at: now.toISOString(),
      updated_at: now.toISOString(),
    } as Omit<TenantSubscription, 'id'>);

    // Cast to the right type to avoid TypeScript errors
    const typedSubscription = subscription as unknown as TenantSubscription;

    // Map to DTO format
    return {
      id: typedSubscription.id,
      tenantId: typedSubscription.tenant_id,
      planId: typedSubscription.plan_id,
      status: typedSubscription.status as TenantSubscriptionDTO['status'],
      billingCycle: typedSubscription.billing_cycle as TenantSubscriptionDTO['billingCycle'],
      trialStart: typedSubscription.trial_start,
      trialEnd: typedSubscription.trial_end,
      currentPeriodStart: typedSubscription.current_period_start,
      currentPeriodEnd: typedSubscription.current_period_end,
      canceledAt: typedSubscription.canceled_at,
      paymentProvider: typedSubscription.payment_provider,
      paymentProviderSubscriptionId: typedSubscription.payment_provider_subscription_id,
      metadata: typedSubscription.metadata as Json | null,
      createdAt: typedSubscription.created_at,
      updatedAt: typedSubscription.updated_at,
    };
  }

  /**
   * Update a subscription
   * @param subscriptionId The subscription ID
   * @param params The update parameters
   * @returns The updated subscription
   */
  async updateSubscription(subscriptionId: string, params: UpdateSubscriptionParams): Promise<TenantSubscriptionDTO> {
    const now = new Date();

    // Prepare the update data for the subscription client
    const updateData: Partial<TenantSubscription> = {
      updated_at: now.toISOString(),
    };

    if (params.planId !== undefined) updateData.plan_id = params.planId;
    if (params.status !== undefined) updateData.status = params.status as TenantSubscription['status'];
    if (params.billingCycle !== undefined) updateData.billing_cycle = params.billingCycle as TenantSubscription['billing_cycle'];
    if (params.trialEnd !== undefined) updateData.trial_end = params.trialEnd;
    if (params.currentPeriodStart !== undefined) updateData.current_period_start = params.currentPeriodStart;
    if (params.currentPeriodEnd !== undefined) updateData.current_period_end = params.currentPeriodEnd;
    if (params.canceledAt !== undefined) updateData.canceled_at = params.canceledAt;
    if (params.paymentProvider !== undefined) updateData.payment_provider = params.paymentProvider;
    if (params.paymentProviderSubscriptionId !== undefined) updateData.payment_provider_subscription_id = params.paymentProviderSubscriptionId;
    if (params.metadata !== undefined) {
      // Ensure params.metadata is a Record object, not null or undefined
      updateData.metadata = params.metadata || {} as Record<string, unknown>;
    }

    // Use the subscription client to update the subscription
    const subscription = await this.subscriptionClient.updateSubscription(subscriptionId, updateData);

    // Cast to the right type to avoid TypeScript errors
    const typedSubscription = subscription as unknown as TenantSubscription;

    // Map to DTO format
    return {
      id: typedSubscription.id,
      tenantId: typedSubscription.tenant_id,
      planId: typedSubscription.plan_id,
      status: typedSubscription.status as TenantSubscriptionDTO['status'],
      billingCycle: typedSubscription.billing_cycle as TenantSubscriptionDTO['billingCycle'],
      trialStart: typedSubscription.trial_start,
      trialEnd: typedSubscription.trial_end,
      currentPeriodStart: typedSubscription.current_period_start,
      currentPeriodEnd: typedSubscription.current_period_end,
      canceledAt: typedSubscription.canceled_at,
      paymentProvider: typedSubscription.payment_provider,
      paymentProviderSubscriptionId: typedSubscription.payment_provider_subscription_id,
      metadata: typedSubscription.metadata as Json | null,
      createdAt: typedSubscription.created_at,
      updatedAt: typedSubscription.updated_at,
    };
  }

  /**
   * Cancel a subscription
   * @param subscriptionId The subscription ID
   * @param cancelImmediately Whether to cancel immediately or at the end of the billing period
   * @returns The updated subscription
   */
  async cancelSubscription(subscriptionId: string, cancelImmediately: boolean = false): Promise<TenantSubscriptionDTO> {
    // Use the subscription client to cancel the subscription
    const subscription = await this.subscriptionClient.cancelSubscription(subscriptionId, cancelImmediately);

    // Cast to the right type to avoid TypeScript errors
    const typedSubscription = subscription as unknown as TenantSubscription;

    // Map to DTO format
    return {
      id: typedSubscription.id,
      tenantId: typedSubscription.tenant_id,
      planId: typedSubscription.plan_id,
      status: typedSubscription.status as TenantSubscriptionDTO['status'],
      billingCycle: typedSubscription.billing_cycle as TenantSubscriptionDTO['billingCycle'],
      trialStart: typedSubscription.trial_start,
      trialEnd: typedSubscription.trial_end,
      currentPeriodStart: typedSubscription.current_period_start,
      currentPeriodEnd: typedSubscription.current_period_end,
      canceledAt: typedSubscription.canceled_at,
      paymentProvider: typedSubscription.payment_provider,
      paymentProviderSubscriptionId: typedSubscription.payment_provider_subscription_id,
      metadata: typedSubscription.metadata as Json | null,
      createdAt: typedSubscription.created_at,
      updatedAt: typedSubscription.updated_at,
    };
  }

  /**
   * Add an addon to a subscription
   * @param params The addon parameters
   * @returns The created addon
   */
  async addAddon(params: AddAddonParams): Promise<TenantAddonDTO> {
    const now = new Date();

    // Get the subscription to determine the billing period
    const subscription = await this.subscriptionClient.getSubscriptionById(params.subscriptionId);

    if (!subscription) {
      throw new Error(`Subscription with ID ${params.subscriptionId} not found`);
    }

    // Cast to the right type to avoid TypeScript errors
    const typedSubscription = subscription as unknown as TenantSubscription;

    // Use the subscription client to add the addon
    const addonData = await this.subscriptionClient.addAddon({
      tenant_id: params.tenantId,
      subscription_id: params.subscriptionId,
      addon_id: params.addonId,
      status: 'active',
      quantity: params.quantity || 1,
      current_period_start: typedSubscription.current_period_start,
      current_period_end: typedSubscription.current_period_end,
      canceled_at: null,
      // Ensure metadata is correctly typed
      metadata: params.metadata || {} as Record<string, unknown>,
      created_at: now.toISOString(),
      updated_at: now.toISOString(),
    });

    // Cast to the right type to avoid TypeScript errors
    const typedAddon = addonData as unknown as TenantAddon;

    // Map to DTO format
    return {
      id: typedAddon.id,
      tenantId: typedAddon.tenant_id,
      subscriptionId: typedAddon.subscription_id,
      addonId: typedAddon.addon_id,
      status: typedAddon.status as TenantAddonDTO['status'],
      quantity: typedAddon.quantity,
      currentPeriodStart: typedAddon.current_period_start,
      currentPeriodEnd: typedAddon.current_period_end,
      canceledAt: typedAddon.canceled_at,
      metadata: typedAddon.metadata as Json | null,
      createdAt: typedAddon.created_at,
      updatedAt: typedAddon.updated_at,
    };
  }

  /**
   * Update an addon
   * @param addonId The addon ID
   * @param params The update parameters
   * @returns The updated addon
   */
  async updateAddon(addonId: string, params: UpdateAddonParams): Promise<TenantAddonDTO> {
    const now = new Date();

    // Prepare the update data for the subscription client
    const updateData: Partial<TenantAddon> = {
      updated_at: now.toISOString(),
    };

    if (params.status !== undefined) updateData.status = params.status as TenantAddon['status'];
    if (params.quantity !== undefined) updateData.quantity = params.quantity;
    if (params.canceledAt !== undefined) updateData.canceled_at = params.canceledAt;
    if (params.metadata !== undefined) {
      // Ensure metadata is a non-null Record object for database storage
      updateData.metadata = params.metadata || {} as Record<string, unknown>;
    }

    // Use the subscription client to update the addon
    const addonData = await this.subscriptionClient.updateAddon(addonId, updateData);

    // Cast to the right type to avoid TypeScript errors
    const typedAddon = addonData as unknown as TenantAddon;

    // Map to DTO format
    return {
      id: typedAddon.id,
      tenantId: typedAddon.tenant_id,
      subscriptionId: typedAddon.subscription_id,
      addonId: typedAddon.addon_id,
      status: typedAddon.status as TenantAddonDTO['status'],
      quantity: typedAddon.quantity,
      currentPeriodStart: typedAddon.current_period_start,
      currentPeriodEnd: typedAddon.current_period_end,
      canceledAt: typedAddon.canceled_at,
      metadata: typedAddon.metadata as Json | null,
      createdAt: typedAddon.created_at,
      updatedAt: typedAddon.updated_at,
    };
  }

  /**
   * Cancel an addon
   * @param addonId The addon ID
   * @param cancelImmediately Whether to cancel immediately or at the end of the billing period
   * @returns The updated addon
   */
  async cancelAddon(addonId: string, cancelImmediately: boolean = false): Promise<TenantAddonDTO> {
    const now = new Date();

    // Prepare the update data for the subscription client
    const updateData: Partial<TenantAddon> = {
      canceled_at: now.toISOString(),
      updated_at: now.toISOString(),
    };

    if (cancelImmediately) {
      updateData.status = 'canceled';
    }

    // Use the subscription client to update the addon
    const addonData = await this.subscriptionClient.updateAddon(addonId, updateData);

    // Cast to the right type to avoid TypeScript errors
    const typedAddon = addonData as unknown as TenantAddon;

    // Map to DTO format
    return {
      id: typedAddon.id,
      tenantId: typedAddon.tenant_id,
      subscriptionId: typedAddon.subscription_id,
      addonId: typedAddon.addon_id,
      status: typedAddon.status as TenantAddonDTO['status'],
      quantity: typedAddon.quantity,
      currentPeriodStart: typedAddon.current_period_start,
      currentPeriodEnd: typedAddon.current_period_end,
      canceledAt: typedAddon.canceled_at,
      metadata: typedAddon.metadata as Json | null,
      createdAt: typedAddon.created_at,
      updatedAt: typedAddon.updated_at,
    };
  }

  /**
   * Check if a tenant has access to a specific feature
   * @param tenantId The tenant ID
   * @param featureKey The feature key to check
   * @returns Whether the tenant has access to the feature
   */
  async hasTenantFeatureAccess(tenantId: string, featureKey: string): Promise<boolean> {
    // Get the tenant's subscription
    const subscription = await this.getTenantSubscription(tenantId);

    if (!subscription) {
      return false;
    }

    // Check if the subscription is active or in trial
    if (!['active', 'trialing'].includes(subscription.status)) {
      return false;
    }

    // Check if the feature is included in the plan
    const planFeatures = subscription.plan?.features;
    if (planFeatures && typeof planFeatures === 'object' && planFeatures !== null && featureKey in (planFeatures as Record<string, unknown>) && (planFeatures as Record<string, unknown>)[featureKey]) {
      return true;
    }

    // Check if the feature is included in any of the addons
    if (subscription.addons && subscription.addons.length > 0) {
      for (const addon of subscription.addons) {
        const addonFeatures = addon.addon?.features;
        if (addon.status === 'active' && addonFeatures && typeof addonFeatures === 'object' && addonFeatures !== null && featureKey in (addonFeatures as Record<string, unknown>) && (addonFeatures as Record<string, unknown>)[featureKey]) {
          return true;
        }
      }
    }

    return false;
  }

  /**
   * Check if a tenant has access to a specific feature based on their subscription.
   * This method calls a Supabase RPC function.
   * @param tenantId The tenant ID
   * @param featureKey The key of the feature to check (e.g., 'hasIntakeAgent')
   * @returns True if the tenant has access, false otherwise.
   */
  async checkFeatureAccess(tenantId: string, featureKey: string): Promise<boolean> {
    try {
      // Use the subscription client to check feature access
      return await this.subscriptionClient.checkFeatureAccess(tenantId, featureKey);
    } catch (_error) {
      console.error('Error checking feature access:', error);
      throw error; // Re-throw the error to be handled by the caller
    }
  }

  /**
   * Extend a trial subscription
   * @param subscriptionId The subscription ID
   * @param additionalDays The number of additional days to add to the trial
   * @returns The updated subscription
   */
  async extendTrial(subscriptionId: string, additionalDays: number): Promise<TenantSubscriptionDTO> {
    // Use the subscription client to extend the trial
    const subscription = await this.subscriptionClient.extendTrial(subscriptionId, additionalDays);

    // Cast to the right type to avoid TypeScript errors
    const typedSubscription = subscription as unknown as TenantSubscription;

    // Map to DTO format
    return {
      id: typedSubscription.id,
      tenantId: typedSubscription.tenant_id,
      planId: typedSubscription.plan_id,
      status: typedSubscription.status as TenantSubscriptionDTO['status'],
      billingCycle: typedSubscription.billing_cycle as TenantSubscriptionDTO['billingCycle'],
      trialStart: typedSubscription.trial_start,
      trialEnd: typedSubscription.trial_end,
      currentPeriodStart: typedSubscription.current_period_start,
      currentPeriodEnd: typedSubscription.current_period_end,
      canceledAt: typedSubscription.canceled_at,
      paymentProvider: typedSubscription.payment_provider,
      paymentProviderSubscriptionId: typedSubscription.payment_provider_subscription_id,
      metadata: typedSubscription.metadata as Json | null,
      createdAt: typedSubscription.created_at,
      updatedAt: typedSubscription.updated_at,
    };
  }

  /**
   * Get all tenant subscriptions (admin function)
   * @param limit The maximum number of subscriptions to return
   * @param offset The offset for pagination
   * @returns List of tenant subscriptions
   */
  async getAllTenantSubscriptions(limit: number = 10, offset: number = 0): Promise<TenantSubscriptionDTO[]> {
    // Use the subscription client to get all tenant subscriptions
    const subscriptions = await this.subscriptionClient.getAllTenantSubscriptions(limit, offset);

    // Map to DTO format
    return subscriptions.map(sub => {
      // Cast to the right type to avoid TypeScript errors
      const typedSub = sub as unknown as TenantSubscription & {
        subscription_plans: SubscriptionPlan
      };

      return {
        id: typedSub.id,
        tenantId: typedSub.tenant_id,
        planId: typedSub.plan_id,
        status: typedSub.status as TenantSubscriptionDTO['status'],
        billingCycle: typedSub.billing_cycle as TenantSubscriptionDTO['billingCycle'],
        trialStart: typedSub.trial_start,
        trialEnd: typedSub.trial_end,
        currentPeriodStart: typedSub.current_period_start,
        currentPeriodEnd: typedSub.current_period_end,
        canceledAt: typedSub.canceled_at,
        paymentProvider: typedSub.payment_provider,
        paymentProviderSubscriptionId: typedSub.payment_provider_subscription_id,
        metadata: typedSub.metadata as Json | null,
        createdAt: typedSub.created_at,
        updatedAt: typedSub.updated_at,
        plan: {
          name: typedSub.subscription_plans.name,
          code: typedSub.subscription_plans.code,
          basePriceMonthly: typedSub.subscription_plans.base_price_monthly,
          basePriceYearly: typedSub.subscription_plans.base_price_yearly,
          features: typedSub.subscription_plans.features,
        },
      };
    });
  }
}
