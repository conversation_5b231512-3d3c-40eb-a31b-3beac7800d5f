import { SupabaseClient } from '@supabase/supabase-js';
import { Database } from '../supabase/database.types';
import { ResourceUsageTable, TenantQuotasTable } from '../supabase/tenant.types';
import { createTenantClient } from './database-client';

export interface ResourceUsage {
  id: string;
  tenantId: string;
  usageType: string;
  usageCount: number;
  resourceSizeBytes?: number | null;
  periodStart: string;
  periodEnd: string;
  createdAt: string;
}

export interface TrackResourceUsageParams {
  tenantId: string;
  usageType: string;
  usageCount: number;
  resourceSizeBytes?: number;
  periodStart?: Date;
  periodEnd?: Date;
}

export interface IncrementResourceUsageParams {
  tenantId: string;
  usageType: string;
  incrementBy: number;
  resourceSizeBytes?: number;
}

export interface GetTenantUsageParams {
  tenantId: string;
  usageType: string;
  startDate: Date;
  endDate: Date;
}

export interface CheckQuotaLimitParams {
  tenantId: string;
  usageType: string;
  incrementBy: number;
  resourceSizeBytes?: number;
}

export interface QuotaCheckResult {
  withinQuota: boolean;
  currentUsage: number;
  quotaLimit: number;
  percentUsed: number;
}

export class UsageTrackingService {
  private tenantDb: ReturnType<typeof createTenantClient>;

  constructor(private supabase: SupabaseClient<Database>) {
    this.tenantDb = createTenantClient(supabase);
  }

  /**
   * Track resource usage
   * @param data The resource usage data
   * @returns The created resource usage record
   */
  async trackResourceUsage(data: TrackResourceUsageParams): Promise<ResourceUsage> {
    // Set default period if not provided
    const now = new Date();
    const periodStart = data.periodStart || new Date(now.getFullYear(), now.getMonth(), 1);
    const periodEnd = data.periodEnd || new Date(now.getFullYear(), now.getMonth() + 1, 0, 23, 59, 59, 999);

    const usage = await this.tenantDb.trackResourceUsage({
      tenant_id: data.tenantId,
      usage_type: data.usageType,
      usage_count: data.usageCount,
      resource_size_bytes: data.resourceSizeBytes,
      period_start: periodStart.toISOString(),
      period_end: periodEnd.toISOString(),
      created_at: now.toISOString(),
    });

    return {
      id: usage.id,
      tenantId: usage.tenant_id,
      usageType: usage.usage_type,
      usageCount: usage.usage_count,
      resourceSizeBytes: usage.resource_size_bytes,
      periodStart: usage.period_start,
      periodEnd: usage.period_end,
      createdAt: usage.created_at,
    };
  }

  /**
   * Increment resource usage for the current period
   * @param data The increment data
   * @returns The updated resource usage record
   */
  async incrementResourceUsage(data: IncrementResourceUsageParams): Promise<ResourceUsage> {
    // Get the current period
    const now = new Date();
    const periodStart = new Date(now.getFullYear(), now.getMonth(), 1);
    const periodEnd = new Date(now.getFullYear(), now.getMonth() + 1, 0, 23, 59, 59, 999);

    // Check if there is an existing usage record for the current period
    try {
      // Use the tenantDb client to get resource usage for the current period
      const existingUsage = await this.getCurrentPeriodUsage(data.tenantId, data.usageType);

      if (existingUsage) {
        // Update the existing usage record using the tenant client
        const updatedUsage = await this.tenantDb.updateResourceUsage(existingUsage.id, {
          usage_count: existingUsage.usageCount + data.incrementBy,
          resource_size_bytes: data.resourceSizeBytes
            ? (existingUsage.resourceSizeBytes || 0) + data.resourceSizeBytes
            : existingUsage.resourceSizeBytes,
        });

        return {
          id: updatedUsage.id,
          tenantId: updatedUsage.tenant_id,
          usageType: updatedUsage.usage_type,
          usageCount: updatedUsage.usage_count,
          resourceSizeBytes: updatedUsage.resource_size_bytes,
          periodStart: updatedUsage.period_start,
          periodEnd: updatedUsage.period_end,
          createdAt: updatedUsage.created_at,
        };
      } else {
        // Create a new usage record
        return this.trackResourceUsage({
          tenantId: data.tenantId,
          usageType: data.usageType,
          usageCount: data.incrementBy,
          resourceSizeBytes: data.resourceSizeBytes,
          periodStart,
          periodEnd,
        });
      }
    } catch (_error) {
      // If the error is not "no rows returned", rethrow it
      throw error;
    }
  }

  /**
   * Get tenant usage for a specific period and type
   * @param params The query parameters
   * @returns The resource usage records
   */
  async getTenantUsage(params: GetTenantUsageParams): Promise<ResourceUsage[]> {
    // Use the tenant client method for getting resource usage for a specific period
    const usageData = await this.tenantDb.getResourceUsageForPeriod(
      params.tenantId,
      params.usageType,
      params.startDate,
      params.endDate
    );

    return usageData.map(usage => ({
      id: usage.id,
      tenantId: usage.tenant_id,
      usageType: usage.usage_type,
      usageCount: usage.usage_count,
      resourceSizeBytes: usage.resource_size_bytes,
      periodStart: usage.period_start,
      periodEnd: usage.period_end,
      createdAt: usage.created_at,
    }));
  }

  /**
   * Get current period usage for a specific type
   * @param tenantId The tenant ID
   * @param usageType The usage type
   * @returns The resource usage record or null if none exists
   */
  async getCurrentPeriodUsage(tenantId: string, usageType: string): Promise<ResourceUsage | null> {
    // Use the tenant client method for getting current period usage
    const usage = await this.tenantDb.getCurrentPeriodUsage(tenantId, usageType);

    if (!usage) return null;

    return {
      id: usage.id,
      tenantId: usage.tenant_id,
      usageType: usage.usage_type,
      usageCount: usage.usage_count,
      resourceSizeBytes: usage.resource_size_bytes,
      periodStart: usage.period_start,
      periodEnd: usage.period_end,
      createdAt: usage.created_at,
    };
  }

  /**
   * Check if a tenant has exceeded their quota limit
   * @param params The check parameters
   * @returns The quota check result
   */
  async checkQuotaLimit(params: CheckQuotaLimitParams): Promise<QuotaCheckResult> {
    // Get tenant quota using the tenant client
    const quota = await this.tenantDb.getQuota(params.tenantId);

    // Get current usage
    const currentUsage = await this.getCurrentPeriodUsage(params.tenantId, params.usageType);

    // Determine quota limit based on usage type
    let quotaLimit = 0;
    switch (params.usageType) {
      case 'document_upload':
        quotaLimit = quota.max_monthly_uploads;
        break;
      case 'document_processing':
        quotaLimit = quota.max_concurrent_processing;
        break;
      case 'storage_usage':
        quotaLimit = quota.max_document_size_mb * 1024 * 1024; // Convert MB to bytes
        break;
      default:
        quotaLimit = 0;
    }

    // Calculate current usage and check if it exceeds the quota
    const usageCount = currentUsage ? currentUsage.usageCount : 0;
    const newUsage = usageCount + params.incrementBy;
    const percentUsed = quotaLimit > 0 ? Math.round((usageCount * 100) / quotaLimit) : 0;

    return {
      withinQuota: newUsage <= quotaLimit || quotaLimit === 0,
      currentUsage: usageCount,
      quotaLimit,
      percentUsed,
    };
  }
}
