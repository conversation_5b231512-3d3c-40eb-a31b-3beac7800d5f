import { describe, it, expect, beforeAll, afterAll, jest } from '@jest/globals';
import { createClient } from '@supabase/supabase-js';
import { SubscriptionService } from '../subscription-service';
import { UsageTrackingService } from '../usage-tracking-service';
import { NotificationService } from '../notification-service';
import { Database } from '../../supabase/database.types';

// This is an integration test that tests the interaction between services
// It requires a real Supabase connection, but we'll use the test environment

describe('Subscription Integration Tests', () => {
  // Create a Supabase client for testing
  const supabase = createClient<Database>(
    process.env.NEXT_PUBLIC_SUPABASE_URL || '',
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || ''
  );

  // Create service instances
  const subscriptionService = new SubscriptionService(supabase);
  const usageTrackingService = new UsageTrackingService(supabase);
  const notificationService = new NotificationService(supabase);

  // Test data
  const testTenantId = 'test-tenant-id';
  let testSubscriptionId: string;

  // Mock console.error to prevent error output during tests
  const originalConsoleError = console.error;
  beforeAll(() => {
    console.error = jest.fn();
  });

  afterAll(() => {
    console.error = originalConsoleError;
  });

  // Skip tests if environment variables are not set
  const skipIfNoEnv = () => {
    if (!process.env.NEXT_PUBLIC_SUPABASE_URL || !process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY) {
      return true;
    }
    return false;
  };

  describe('Subscription Lifecycle', () => {
    it('should create a subscription', async () => {
      if (skipIfNoEnv()) {
        console.log('Skipping test: No Supabase environment variables');
        return;
      }

      try {
        // Get available plans
        const plans = await subscriptionService.getSubscriptionPlans();
        expect(plans).toBeDefined();
        expect(Array.isArray(plans)).toBe(true);

        if (plans.length === 0) {
          console.log('Skipping test: No subscription plans available');
          return;
        }

        // Create a subscription
        const subscription = await subscriptionService.createSubscription({
          tenantId: testTenantId,
          planId: plans[0].id,
          trialDays: 14,
        });

        expect(subscription).toBeDefined();
        expect(subscription.tenantId).toBe(testTenantId);
        expect(subscription.planId).toBe(plans[0].id);
        expect(subscription.status).toBe('trialing');

        // Save the subscription ID for later tests
        testSubscriptionId = subscription.id;
      } catch (_error) {
        console.log('Test error:', error);
        throw error;
      }
    });

    it('should get tenant subscription', async () => {
      if (skipIfNoEnv() || !testSubscriptionId) {
        console.log('Skipping test: No Supabase environment variables or no test subscription');
        return;
      }

      try {
        const subscription = await subscriptionService.getTenantSubscription(testTenantId);

        expect(subscription).toBeDefined();
        expect(subscription?.id).toBe(testSubscriptionId);
        expect(subscription?.tenantId).toBe(testTenantId);
      } catch (_error) {
        console.log('Test error:', error);
        throw error;
      }
    });

    it('should check feature access', async () => {
      if (skipIfNoEnv() || !testSubscriptionId) {
        console.log('Skipping test: No Supabase environment variables or no test subscription');
        return;
      }

      try {
        // This test might fail if the feature doesn't exist in the plan
        // It's just a demonstration of how to check feature access
        const hasAccess = await subscriptionService.checkFeatureAccess(testTenantId, 'basic_access');

        // We expect this to be either true or false, depending on the plan
        expect(typeof hasAccess).toBe('boolean');
      } catch (_error) {
        console.log('Test error:', error);
        throw error;
      }
    });

    it('should cancel subscription', async () => {
      if (skipIfNoEnv() || !testSubscriptionId) {
        console.log('Skipping test: No Supabase environment variables or no test subscription');
        return;
      }

      try {
        const result = await subscriptionService.cancelSubscription(testSubscriptionId);

        expect(result).toBeDefined();
        expect(result.id).toBe(testSubscriptionId);
        expect(result.status).toBe('canceled');
        expect(result.canceledAt).toBeDefined();
      } catch (_error) {
        console.log('Test error:', error);
        throw error;
      }
    });
  });

  describe('Usage Tracking Integration', () => {
    it('should track resource usage', async () => {
      if (skipIfNoEnv()) {
        console.log('Skipping test: No Supabase environment variables');
        return;
      }

      try {
        const usage = await usageTrackingService.trackResourceUsage({
          tenantId: testTenantId,
          usageType: 'document_upload',
          usageCount: 1,
          resourceSizeBytes: 1024,
        });

        expect(usage).toBeDefined();
        expect(usage.tenantId).toBe(testTenantId);
        expect(usage.usageType).toBe('document_upload');
        expect(usage.usageCount).toBe(1);
        expect(usage.resourceSizeBytes).toBe(1024);
      } catch (_error) {
        console.log('Test error:', error);
        throw error;
      }
    });

    it('should increment resource usage', async () => {
      if (skipIfNoEnv()) {
        console.log('Skipping test: No Supabase environment variables');
        return;
      }

      try {
        const usage = await usageTrackingService.incrementResourceUsage({
          tenantId: testTenantId,
          usageType: 'document_upload',
          incrementBy: 1,
          resourceSizeBytes: 1024,
        });

        expect(usage).toBeDefined();
        expect(usage.tenantId).toBe(testTenantId);
        expect(usage.usageType).toBe('document_upload');
        expect(usage.usageCount).toBeGreaterThanOrEqual(1);
      } catch (_error) {
        console.log('Test error:', error);
        throw error;
      }
    });

    it('should get current period usage', async () => {
      if (skipIfNoEnv()) {
        console.log('Skipping test: No Supabase environment variables');
        return;
      }

      try {
        const usage = await usageTrackingService.getCurrentPeriodUsage(
          testTenantId,
          'document_upload'
        );

        expect(usage).toBeDefined();
        if (usage) {
          expect(usage.tenantId).toBe(testTenantId);
          expect(usage.usageType).toBe('document_upload');
          expect(usage.usageCount).toBeGreaterThanOrEqual(1);
        }
      } catch (_error) {
        console.log('Test error:', error);
        throw error;
      }
    });
  });

  describe('Notification Integration', () => {
    it('should create a notification', async () => {
      if (skipIfNoEnv()) {
        console.log('Skipping test: No Supabase environment variables');
        return;
      }

      try {
        const notification = await notificationService.createNotification(
          testTenantId,
          'test-user-id',
          'info',
          'Test notification',
          'low'
        );

        expect(notification).toBeDefined();
        expect(notification.tenantId).toBe(testTenantId);
        expect(notification.userId).toBe('test-user-id');
        expect(notification.type).toBe('info');
        expect(notification.message).toBe('Test notification');
        expect(notification.severity).toBe('low');
        expect(notification.read).toBe(false);
      } catch (_error) {
        console.log('Test error:', error);
        throw error;
      }
    });

    it('should get user notifications', async () => {
      if (skipIfNoEnv()) {
        console.log('Skipping test: No Supabase environment variables');
        return;
      }

      try {
        const notifications = await notificationService.getUserNotifications(
          'test-user-id',
          true
        );

        expect(notifications).toBeDefined();
        expect(Array.isArray(notifications)).toBe(true);

        if (notifications.length > 0) {
          const notification = notifications[0];
          expect(notification.userId).toBe('test-user-id');
        }
      } catch (_error) {
        console.log('Test error:', error);
        throw error;
      }
    });

    it('should mark notification as read', async () => {
      if (skipIfNoEnv()) {
        console.log('Skipping test: No Supabase environment variables');
        return;
      }

      try {
        // Get the latest notification
        const notifications = await notificationService.getUserNotifications(
          'test-user-id',
          false
        );

        if (notifications.length === 0) {
          console.log('Skipping test: No unread notifications');
          return;
        }

        const notificationId = notifications[0].id;

        const result = await notificationService.markAsRead(
          notificationId,
          'test-user-id'
        );

        expect(result).toBeDefined();
        expect(result.id).toBe(notificationId);
        expect(result.read).toBe(true);
      } catch (_error) {
        console.log('Test error:', error);
        throw error;
      }
    });
  });
});
