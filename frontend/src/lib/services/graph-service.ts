import neo4j, { Driver, Session, QueryResult } from 'neo4j-driver';
import { Case } from '@/lib/schemas/case'; // Assuming Case type/interface exists

const NEO4J_URI = process.env.NEO4J_URI;
const NEO4J_USER = process.env.NEO4J_USER;
const NEO4J_PASSWORD = process.env.NEO4J_PASSWORD;

let driver: Driver | null = null;

function getDriver(): Driver {
  if (!driver) {
    if (!NEO4J_URI || !NEO4J_USER || !NEO4J_PASSWORD) {
      throw new Error('Neo4j environment variables (URI, USER, PASSWORD) not set.');
    }
    try {
      driver = neo4j.driver(NEO4J_URI, neo4j.auth.basic(NEO4J_USER, NEO4J_PASSWORD));
      // Optional: Verify connection on startup - useful for debugging
      driver.verifyConnectivity()
        .then(() => console.log('Neo4j driver connected successfully.'))
        .catch((error) => console.error('Neo4j driver connection error:', error));
    } catch (_error) {
      console.error('Failed to create Neo4j driver:', error);
      throw new Error('Could not establish connection with Neo4j database.');
    }
  }
  return driver;
}

// Graceful shutdown
// Consider calling this on application exit if possible
export async function closeDriver(): Promise<void> {
  if (driver) {
    await driver.close();
    driver = null;
    console.log('Neo4j driver closed.');
  }
}

export interface GraphNode {
  id: string; // Unique identifier for the node (e.g., case ID, person ID)
  label: string; // Node type (e.g., 'Case', 'Person', 'Task')
  properties: Record<string, any>; // All other properties of the node
}

export interface GraphLink {
  source: string; // ID of the source node
  target: string; // ID of the target node
  type: string; // Type of the relationship (e.g., 'REPRESENTS', 'CLIENT_IN')
  properties?: Record<string, any>; // Optional relationship properties
}

export interface GraphData {
  nodes: GraphNode[];
  links: GraphLink[];
}

export class GraphService {
  private getSession(database?: string): Session {
    return getDriver().session({ database }); // Specify database if needed, defaults often work
  }

  /**
   * Retrieves cases associated with a specific staff member within a tenant.
   * @param staffId - The UUID of the staff member.
   * @param tenantId - The UUID of the tenant.
   * @returns A promise resolving to an array of Case objects.
   */
  async getStaffCaseLoad(staffId: string, tenantId: string): Promise<Case[]> {
    const session = this.getSession();
    try {
      // Note: Adjust relationship type (:REPRESENTS) and node properties (person_id, case_id, tenant_id)
      // based on your actual graph schema populated by the sync service.
      const query = `
        MATCH (s:Person {person_id: $staffId, tenant_id: $tenantId})-[:REPRESENTS]->(c:Case {tenant_id: $tenantId})
        RETURN c
      `;
      const result: QueryResult = await session.run(query, { staffId, tenantId });

      // Process results - assumes 'c' node properties match the 'Case' type
      // The structure depends heavily on how neo4j-driver returns node data.
      // You might need to access properties like result.records[0].get('c').properties
      const cases: Case[] = result.records.map(record => {
          const caseNode = record.get('c');
          // Map Neo4j node properties to your Case type
          // Ensure property names match (e.g., caseNode.properties.case_id vs caseNode.properties.id)
          return {
              id: caseNode.properties.case_id, // Adjust property name as needed
              tenant_id: caseNode.properties.tenant_id,
              title: caseNode.properties.title,
              status: caseNode.properties.status,
              description: caseNode.properties.description,
              client_id: caseNode.properties.client_id, // Example, if stored
              // Add other relevant fields from your Case type/schema
              created_at: caseNode.properties.created_at,
              updated_at: caseNode.properties.updated_at,
              metadata: caseNode.properties.metadata ? JSON.parse(caseNode.properties.metadata) : {},
              sensitive: caseNode.properties.sensitive,
              rejection_reason: caseNode.properties.rejection_reason,
              created_by: caseNode.properties.created_by
          } as Case; // Type assertion might be needed
      });

      return cases;
    } catch (_error) {
      console.error(`Error fetching staff caseload for staff ${staffId} in tenant ${tenantId}:`, error);
      throw new Error('Failed to retrieve staff caseload from graph database.');
    } finally {
      await session.close();
    }
  }

  /**
   * Retrieves the network graph for a specific case within a tenant.
   * Fetches the case node and its immediate neighbors (Persons, Tasks, etc.)
   * and the relationships connecting them.
   * @param caseId - The UUID of the case.
   * @param tenantId - The UUID of the tenant.
   * @returns A promise resolving to graph data { nodes, links }.
   */
  async getCaseNetwork(caseId: string, tenantId: string): Promise<GraphData> {
    const session = this.getSession();
    const graphData: GraphData = { nodes: [], links: [] };
    const nodeIds = new Set<string>(); // Keep track of added nodes to avoid duplicates

    try {
      // Query to get the case and its direct neighbors (adjust depth [*1..1] if needed)
      // Filter neighbors by tenant_id where applicable
      const query = `
        MATCH (c:Case {case_id: $caseId, tenant_id: $tenantId})
        OPTIONAL MATCH path = (c)-[r*1..1]-(neighbor)
        WHERE neighbor.tenant_id = $tenantId OR NOT EXISTS(neighbor.tenant_id) // Filter neighbors by tenant or allow nodes without tenant_id (like generic types?)
        WITH c, collect(path) as paths
        UNWIND paths as p
        WITH nodes(p) as pathNodes, relationships(p) as pathRels
        UNWIND pathNodes as node
        WITH collect(DISTINCT node) as nodes, collect(DISTINCT pathRels[0]) as rels // Collect distinct nodes and the first relationship in the path
        RETURN nodes, rels
      `;

      const result: QueryResult = await session.run(query, { caseId, tenantId });

      if (result.records.length === 0) {
          // Handle case where the specific case isn't found or has no neighbors
          // Optionally, fetch just the case node itself if it exists but has no connections
          const caseNodeResult = await session.run('MATCH (c:Case {case_id: $caseId, tenant_id: $tenantId}) RETURN c', { caseId, tenantId });
          if (caseNodeResult.records.length > 0) {
              const caseNode = caseNodeResult.records[0].get('c');
              const nodeId = caseNode.properties.case_id ?? caseNode.identity.toString(); // Use case_id if available, else internal ID
              if (!nodeIds.has(nodeId)) {
                  graphData.nodes.push({
                      id: nodeId,
                      label: caseNode.labels[0] || 'Unknown', // Get first label
                      properties: caseNode.properties
                  });
                  nodeIds.add(nodeId);
              }
          }
          return graphData; // Return empty or single-node graph
      }

      // Process nodes and relationships from the first record (UNWIND aggregates results)
      const record = result.records[0];
      const nodes = record.get('nodes');
      const rels = record.get('rels');

      // Add nodes to graphData
      nodes.forEach((node: any) => {
          // Prefer a business key (like case_id, person_id) if available, otherwise use Neo4j's internal ID
          const nodeId = node.properties.case_id ?? node.properties.person_id ?? node.properties.task_id ?? node.identity.toString();
          if (!nodeIds.has(nodeId)) {
            graphData.nodes.push({
              id: nodeId,
              label: node.labels[0] || 'Unknown', // Use the first label as the node type
              properties: node.properties,
            });
            nodeIds.add(nodeId);
          }
      });

      // Add relationships (links) to graphData
      rels.forEach((rel: any) => {
          // Find corresponding source and target node IDs from our processed nodes
          // Note: Neo4j driver might return internal IDs in different ways. elementId is common in newer versions.
          // Fallback to start/end if elementId is not present.
          const sourceInternalId = rel.start.toString();
          const targetInternalId = rel.end.toString();

          // Find the nodes in our collected list using the internal IDs
          const sourceNode = nodes.find((n: any) => n.identity.toString() === sourceInternalId);
          const targetNode = nodes.find((n: any) => n.identity.toString() === targetInternalId);

          // Determine the final ID (business key preferred) for source and target
          const finalSourceId = sourceNode?.properties.case_id ?? sourceNode?.properties.person_id ?? sourceNode?.properties.task_id ?? sourceInternalId;
          const finalTargetId = targetNode?.properties.case_id ?? targetNode?.properties.person_id ?? targetNode?.properties.task_id ?? targetInternalId;

          // Only add link if both source and target nodes were included in our node set
          if (nodeIds.has(finalSourceId) && nodeIds.has(finalTargetId)) {
              graphData.links.push({
                source: finalSourceId,
                target: finalTargetId,
                type: rel.type,
                properties: rel.properties,
              });
          }
      });

      return graphData;
    } catch (_error) {
      console.error(`Error fetching network for case ${caseId} in tenant ${tenantId}:`, error);
      throw new Error('Failed to retrieve case network from graph database.');
    } finally {
      await session.close();
    }
  }

  /**
   * Retrieves the network graph for a specific person.
   */
  async getPersonNetwork(personId: string, tenantId: string): Promise<GraphData> {
      const session = this.getSession();
      const graphData: GraphData = { nodes: [], links: [] };
      const nodeIds = new Set<string>(); // Keep track of added node IDs
      const internalIdMap = new Map<string, string>(); // Map internal Neo4j IDs to our consistent IDs

      try {
          // Query to get the person and their 1-hop neighbors and relationships within the tenant
          const query = `
            MATCH (p:Person {person_id: $personId, tenant_id: $tenantId})
            OPTIONAL MATCH path = (p)-[r*1..1]-(neighbor)
            // Ensure neighbors are within the same tenant or have no tenant (e.g., global concepts)
            WHERE neighbor.tenant_id = $tenantId OR NOT EXISTS(neighbor.tenant_id)
            WITH p, collect(path) AS paths
            // Extract all unique nodes and relationships from the paths
            CALL apoc.convert.toSet(nodes(paths)) AS n
            CALL apoc.convert.toSet(relationships(paths)) AS r
            // Add the central person node even if they have no relationships
            RETURN p, n, r
          `;

          const result: QueryResult = await session.run(query, { personId, tenantId });

          if (result.records.length === 0) {
             // Person not found, try fetching just the person node if needed
             const personNodeResult = await session.run(
                `MATCH (p:Person {person_id: $personId, tenant_id: $tenantId}) RETURN p`,
                { personId, tenantId }
             );
             if (personNodeResult.records.length > 0) {
                 const personNode = personNodeResult.records[0].get('p');
                 const nodeId = personNode.properties.person_id ?? personNode.identity.toString();
                 if (!nodeIds.has(nodeId)) {
                     graphData.nodes.push({
                         id: nodeId,
                         label: personNode.labels[0] || 'Person',
                         properties: personNode.properties
                     });
                     nodeIds.add(nodeId);
                 }
             }
             return graphData; // Return empty or single-node graph
         }

         // Process nodes and relationships
         const record = result.records[0];
         const primaryPersonNode = record.get('p');
         const neighborNodes = record.get('n') || []; // Default to empty array if null/undefined
         const relationships = record.get('r') || []; // Default to empty array if null/undefined

         // Add the primary person node first
         if (primaryPersonNode) {
             const primaryNodeId = this.getNodeId(primaryPersonNode);
             if (!nodeIds.has(primaryNodeId)) {
                graphData.nodes.push({
                    id: primaryNodeId,
                    label: primaryPersonNode.labels[0] || 'Unknown',
                    properties: primaryPersonNode.properties,
                });
                nodeIds.add(primaryNodeId);
                // Map internal Neo4j ID to our consistent ID
                internalIdMap.set(primaryPersonNode.identity.toString(), primaryNodeId);
            }
         }

         // Process neighbor nodes
         neighborNodes.forEach((node: any) => {
             const nodeId = this.getNodeId(node);
             if (!nodeIds.has(nodeId)) {
                 graphData.nodes.push({
                     id: nodeId,
                     label: node.labels[0] || 'Unknown',
                     properties: node.properties,
                 });
                 nodeIds.add(nodeId);
                 // Map internal Neo4j ID to our consistent ID
                 internalIdMap.set(node.identity.toString(), nodeId);
             }
         });

         // Process relationships
         relationships.forEach((rel: any) => {
             // Use the map to get consistent source/target IDs
             const sourceId = internalIdMap.get(rel.start.toString());
             const targetId = internalIdMap.get(rel.end.toString());

             // Only add the link if both source and target nodes were processed
             if (sourceId && targetId) {
                 graphData.links.push({
                     source: sourceId,
                     target: targetId,
                     type: rel.type,
                     properties: rel.properties,
                 });
             }
         });

          return graphData;
      } catch (_error) {
          console.error(`Error fetching network for person ${personId} in tenant ${tenantId}:`, error);
          throw new Error('Failed to retrieve person network from graph database.');
      } finally {
          await session.close();
      }
  }

   /**
    * Runs a conflict check based on provided input.
    * // TODO: Define the structure of checkInput and the actual conflict logic.
    * // TODO: Define the structure of the returned conflict objects.
    * @param checkInput - Object containing details for the conflict check (e.g., names, company, case ID).
    * @param tenantId - The UUID of the tenant.
    * @returns A promise resolving to an array of potential conflict objects.
    */
   async runConflictCheck(checkInput: any, tenantId: string): Promise<any[]> {
     const session = this.getSession();
     const conflicts: any[] = []; // Array to hold identified conflicts

     // --- Example Conflict Check Logic (Placeholder) ---
     // This needs to be replaced with actual Cypher queries based on your firm's rules.
     // Example: Check if a provided person name exists as a Client in another active case for the firm.

     const { personName, companyName, associatedCaseId } = checkInput;

     try {
       console.log(`Running conflict check for tenant ${tenantId} with input:`, checkInput);

       // --- Placeholder Query 1: Check against existing client names ---
       if (personName) {
         const clientConflictQuery = `
           MATCH (p:Person {name: $personName, tenant_id: $tenantId})-[:CLIENT_IN]->(c:Case {tenant_id: $tenantId})
           WHERE c.status = 'Active' AND c.case_id <> $associatedCaseId // Exclude the current case if provided
           RETURN p, c, 'Existing Client Conflict' AS conflictType
           LIMIT 5 // Limit results for performance
         `;
         const clientResult = await session.run(clientConflictQuery, { personName, tenantId, associatedCaseId: associatedCaseId || null });
         clientResult.records.forEach(record => {
           conflicts.push({
             type: record.get('conflictType'),
             conflictingEntity: record.get('p').properties,
             relatedCase: record.get('c').properties,
             description: `Provided name '${personName}' matches an existing active client.`
           });
         });
       }

       // --- Placeholder Query 2: Check against opposing party names ---
       // Add similar queries for other conflict types (opposing parties, related companies, etc.)
       // if (personName) { ... }
       // if (companyName) { ... }

       console.log(`Conflict check for tenant ${tenantId} found ${conflicts.length} potential conflicts.`);
       return conflicts;

     } catch (_error) {
       console.error(`Error running conflict check for tenant ${tenantId}:`, error);
       throw new Error('Failed to run conflict check in graph database.');
     } finally {
       await session.close();
     }
   }

   // Helper to get a consistent node ID
   private getNodeId(node: any): string {
       return node.properties.person_id ?? node.properties.case_id ?? node.properties.task_id ?? node.identity.toString();
   }
}

// Export a singleton instance
export const graphService = new GraphService();
