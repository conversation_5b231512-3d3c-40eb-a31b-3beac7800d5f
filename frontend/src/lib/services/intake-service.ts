// src/lib/services/intake-service.ts
import type { SupabaseClient } from '@supabase/supabase-js';
import { Database } from '@/lib/supabase/database.types';

/**
 * Interface for intake data
 */
export interface IntakeData {
  client_id: string;
  practice_area: string;
  case_type: string;
  case_description: string;
  date_of_incident?: string;
  other_party_names?: string;
  previous_attorney_consultation: boolean;
  previous_consultation_details?: string;
  priority: string;
  occupation?: string;
  employer?: string;
  work_status?: string;
  status: string;
  [key: string]: any;
}

/**
 * Service result interface
 */
export interface ServiceResult<T = any> {
  success: boolean;
  data?: T;
  error?: any;
}

/**
 * Intake Service
 * Handles operations related to client intakes
 */
export class IntakeService {
  private supabase: SupabaseClient;
  private tenantId: string;

  constructor(supabase: SupabaseClient, tenantId: string) {
    this.supabase = supabase;
    this.tenantId = tenantId;
  }

  /**
   * Create a new intake
   */
  async create(data: IntakeData): Promise<ServiceResult> {
    try {
      // Add tenant ID to the data
      const intakeData = {
        ...data,
        tenant_id: this.tenantId,
        created_at: new Date().toISOString(),
      };

      // Insert into the database
      const { data: intake, error } = await this.supabase
        .from('intakes')
        .insert(intakeData)
        .select('*')
        .single();

      if (error) {
        console.error('Error creating intake:', error);
        return { success: false, error };
      }

      return { success: true, data: intake };
    } catch (_error) {
      console.error('Unexpected error creating intake:', error);
      return { success: false, error };
    }
  }

  /**
   * Get an intake by ID
   */
  async getById(id: string): Promise<ServiceResult> {
    try {
      const { data: intake, error } = await this.supabase
        .from('intakes')
        .select('*')
        .eq('id', id)
        .eq('tenant_id', this.tenantId)
        .single();

      if (error) {
        console.error('Error fetching intake:', error);
        return { success: false, error };
      }

      return { success: true, data: intake };
    } catch (_error) {
      console.error('Unexpected error fetching intake:', error);
      return { success: false, error };
    }
  }

  /**
   * Update an intake
   */
  async update(id: string, data: Partial<IntakeData>): Promise<ServiceResult> {
    try {
      // Remove any fields that shouldn't be updated
      const { created_at, tenant_id, ...updateData } = data as any;

      // Update the intake
      const { data: intake, error } = await this.supabase
        .from('intakes')
        .update({
          ...updateData,
          updated_at: new Date().toISOString(),
        })
        .eq('id', id)
        .eq('tenant_id', this.tenantId)
        .select('*')
        .single();

      if (error) {
        console.error('Error updating intake:', error);
        return { success: false, error };
      }

      return { success: true, data: intake };
    } catch (_error) {
      console.error('Unexpected error updating intake:', error);
      return { success: false, error };
    }
  }

  /**
   * List intakes with optional filters
   */
  async list(options: {
    status?: string;
    priority?: string;
    limit?: number;
    offset?: number;
  } = {}): Promise<ServiceResult> {
    try {
      const { status, priority, limit = 50, offset = 0 } = options;

      // Start building the query
      let query = this.supabase
        .from('intakes')
        .select('*, clients(*)')
        .eq('tenant_id', this.tenantId);

      // Apply filters if provided
      if (status) {
        query = query.eq('status', status);
      }

      if (priority) {
        query = query.eq('priority', priority);
      }

      // Apply pagination
      query = query.range(offset, offset + limit - 1).order('created_at', { ascending: false });

      // Execute the query
      const { data: intakes, error } = await query;

      if (error) {
        console.error('Error listing intakes:', error);
        return { success: false, error };
      }

      return { success: true, data: intakes };
    } catch (_error) {
      console.error('Unexpected error listing intakes:', error);
      return { success: false, error };
    }
  }

  /**
   * Delete an intake
   */
  async delete(id: string): Promise<ServiceResult> {
    try {
      const { error } = await this.supabase
        .from('intakes')
        .delete()
        .eq('id', id)
        .eq('tenant_id', this.tenantId);

      if (error) {
        console.error('Error deleting intake:', error);
        return { success: false, error };
      }

      return { success: true };
    } catch (_error) {
      console.error('Unexpected error deleting intake:', error);
      return { success: false, error };
    }
  }
}

/**
 * Create an intake service instance
 */
export function createIntakeService(supabase: SupabaseClient, tenantId: string) {
  return new IntakeService(supabase, tenantId);
}
