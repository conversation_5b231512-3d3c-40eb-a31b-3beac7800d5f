import { SupabaseClient } from '@supabase/supabase-js';
import { RateLimitService } from './rate-limit-service';

export type DocumentQueueItem = {
  documentId: string;
  userId: string;
  fileName: string;
  fileType: string;
  priority?: 'high' | 'normal' | 'low';
  metadata?: Record<string, any>;
};

type JobStatus = {
  jobId: string;
  status: 'queued' | 'processing' | 'completed' | 'failed';
  progress: number;
  message: string;
  createdAt: string;
  updatedAt: string;
  result?: Record<string, any>;
  error?: string;
};

/**
 * Service for interacting with the document processing queue
 */
export class QueueService {
  private supabase: SupabaseClient;
  private tenantId: string;

  constructor(supabase: SupabaseClient, tenantId: string) {
    this.supabase = supabase;
    this.tenantId = tenantId;
  }

  /**
   * Enqueue a document for processing
   * @param document The document to enqueue
   * @returns Promise that resolves to the job ID
   */
  async enqueueDocument(document: DocumentQueueItem): Promise<string> {
    const rateLimitService = new RateLimitService(this.supabase);

    try {
      // Track processing start
      await rateLimitService.trackProcessingStart(this.tenantId);

      // Call the API endpoint to enqueue the document
      const { data, error } = await this.supabase
        .from('document_processing_jobs')
        .insert({
          document_id: document.documentId,
          user_id: document.userId,
          tenant_id: this.tenantId,
          file_name: document.fileName,
          file_type: document.fileType,
          priority: document.priority || 'normal',
          metadata: document.metadata || {},
        status: 'queued',
        progress: 0,
        message: 'Document queued for processing',
      })
      .select('id')
      .single();

    if (error) {
      // If there's an error, make sure we track processing completion
      await rateLimitService.trackProcessingComplete(this.tenantId);
      console.error('Error enqueueing document:', error);
      throw new Error(`Failed to enqueue document: ${error.message}`);
    }

    // Return the job ID
    return data.id;
  } catch (_error) {
    // If there's an exception, make sure we track processing completion
    await rateLimitService.trackProcessingComplete(this.tenantId);
    throw error;
  }
  }

  /**
   * Get the status of a document processing job
   * @param jobId The ID of the job to check
   * @returns Promise that resolves to the job status
   */
  async getJobStatus(jobId: string): Promise<JobStatus> {
    const { data, error } = await this.supabase
      .from('document_processing_jobs')
      .select('*')
      .eq('id', jobId)
      .eq('tenant_id', this.tenantId)
      .single();

    if (error) {
      console.error('Error getting job status:', error);
      throw new Error(`Failed to get job status: ${error.message}`);
    }

    return {
      jobId: data.id,
      status: data.status,
      progress: data.progress,
      message: data.message,
      createdAt: data.created_at,
      updatedAt: data.updated_at,
      result: data.result,
      error: data.error,
    };
  }

  /**
   * Get all processing jobs for a document
   * @param documentId The ID of the document
   * @returns Promise that resolves to an array of job statuses
   */
  async getDocumentJobs(documentId: string): Promise<JobStatus[]> {
    const { data, error } = await this.supabase
      .from('document_processing_jobs')
      .select('*')
      .eq('document_id', documentId)
      .eq('tenant_id', this.tenantId)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error getting document jobs:', error);
      throw new Error(`Failed to get document jobs: ${error.message}`);
    }

    return data.map((job) => ({
      jobId: job.id,
      status: job.status,
      progress: job.progress,
      message: job.message,
      createdAt: job.created_at,
      updatedAt: job.updated_at,
      result: job.result,
      error: job.error,
    }));
  }

  /**
   * Re-process a document that was previously processed
   * @param documentId The ID of the document to reprocess
   * @param priority The priority of the job
   * @returns Promise that resolves to the new job ID
   */
  async reprocessDocument(
    documentId: string,
    priority: 'high' | 'normal' | 'low' = 'high',
    options?: {
      analysisType?: 'text' | 'tasks' | 'medical';
      forceClassification?: boolean;
    }
  ): Promise<string> {
    const rateLimitService = new RateLimitService(this.supabase);

    try {
      // Track processing start
      await rateLimitService.trackProcessingStart(this.tenantId);
    // First, get the document information
    const { data: document, error: documentError } = await this.supabase
      .from('case_documents')
      .select('*')
      .eq('id', documentId)
      .eq('tenant_id', this.tenantId)
      .single();

    if (documentError) {
      console.error('Error getting document info:', documentError);
      throw new Error(`Failed to get document info: ${documentError.message}`);
    }

    // Create a new processing job
    const { data, error } = await this.supabase
      .from('document_processing_jobs')
      .insert({
        document_id: documentId,
        user_id: document.created_by,
        tenant_id: this.tenantId,
        file_name: document.title,
        file_type: document.metadata?.file_type || 'application/pdf',
        priority,
        metadata: {
          ...document.metadata,
          ...options,
          reprocessed: true,
          original_document_id: documentId,
        },
        status: 'queued',
        progress: 0,
        message: 'Document queued for reprocessing',
      })
      .select('id')
      .single();

    if (error) {
      // If there's an error, make sure we track processing completion
      await rateLimitService.trackProcessingComplete(this.tenantId);
      console.error('Error reprocessing document:', error);
      throw new Error(`Failed to reprocess document: ${error.message}`);
    }

    // Return the job ID
    return data.id;
  } catch (_error) {
    // If there's an exception, make sure we track processing completion
    await rateLimitService.trackProcessingComplete(this.tenantId);
    throw error;
  }
  }
}
