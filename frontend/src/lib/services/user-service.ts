// src/lib/services/user-service.ts
import type { SupabaseClient } from '@supabase/supabase-js';

/**
 * User Service
 * Responsible for all user-related data operations
 */
export class UserService {
  private supabase: SupabaseClient;
  private tenantId: string;

  constructor(supabase: SupabaseClient, tenantId: string) {
    this.supabase = supabase;
    this.tenantId = tenantId;
  }

  /**
   * Get all users for the current tenant
   */
  async getAll() {
    try {
      const { data, error } = await this.supabase
        .schema('tenants')
        .from('users')
        .select('id, email, first_name, last_name, role, auth_user_id')
        .eq('tenant_id', this.tenantId);

      if (error) {
        console.error('Error fetching users:', error);
        throw error;
      }

      return data.map(user => ({
        ...user,
        full_name: `${user.first_name || ''} ${user.last_name || ''}`.trim() || 'Unnamed User'
      }));
    } catch (_error) {
      console.error('Exception in UserService.getAll:', error);
      throw error;
    }
  }

  /**
   * Get a user by their ID (users.id)
   */
  async getById(userId: string) {
    try {
      const { data, error } = await this.supabase
        .schema('tenants')
        .from('users')
        .select('id, email, first_name, last_name, role, auth_user_id')
        .eq('id', userId)
        .eq('tenant_id', this.tenantId)
        .single();

      if (error) {
        console.error(`Error fetching user by ID ${userId}:`, error);
        return null;
      }

      return {
        ...data,
        full_name: `${data.first_name || ''} ${data.last_name || ''}`.trim() || 'Unnamed User'
      };
    } catch (_error) {
      console.error('Exception in UserService.getById:', error);
      return null;
    }
  }

  /**
   * Get a user by their auth_user_id
   * This is useful for converting auth IDs to user IDs
   */
  async getByAuthId(authUserId: string) {
    try {
      const { data, error } = await this.supabase
        .schema('tenants')
        .from('users')
        .select('id, email, first_name, last_name, role, auth_user_id')
        .eq('auth_user_id', authUserId)
        .eq('tenant_id', this.tenantId)
        .single();

      if (error) {
        console.error(`Error fetching user by auth ID ${authUserId}:`, error);
        return null;
      }

      return {
        ...data,
        full_name: `${data.first_name || ''} ${data.last_name || ''}`.trim() || 'Unnamed User'
      };
    } catch (_error) {
      console.error('Exception in UserService.getByAuthId:', error);
      return null;
    }
  }

  /**
   * Get multiple users by their IDs
   */
  async getByIds(userIds: string[]) {
    if (!userIds || userIds.length === 0) return [];

    try {
      const { data, error } = await this.supabase
        .schema('tenants')
        .from('users')
        .select('id, email, first_name, last_name, role, auth_user_id')
        .eq('tenant_id', this.tenantId)
        .in('id', userIds);

      if (error) {
        console.error(`Error fetching users by IDs:`, error);
        return [];
      }

      return data.map(user => ({
        ...user,
        full_name: `${user.first_name || ''} ${user.last_name || ''}`.trim() || 'Unnamed User'
      }));
    } catch (_error) {
      console.error('Exception in UserService.getByIds:', error);
      return [];
    }
  }
}

/**
 * Factory function to create a UserService instance
 */
export function createUserService(supabase: SupabaseClient, tenantId: string) {
  return new UserService(supabase, tenantId);
}
