'use client';

import { SubscriptionService, TenantSubscriptionDTO } from '@/lib/services/subscription-service';
import { SupabaseClient } from '@supabase/supabase-js';
import { Database } from '@/lib/supabase/database.types';

export interface CurrentPlan {
  planCode: string;
  planName: string;
  features: string[];
  status: 'active' | 'trialing' | 'canceled' | 'inactive' | 'past_due';
  expired?: boolean;
  trialEnd?: Date | null;
  currentPeriodEnd?: Date | null;
}

/**
 * Determines the current plan for a tenant based on subscription data
 * @param tenantId - The tenant ID
 * @param supabase - Supabase client instance
 * @returns Current plan information with features
 */
export async function getCurrentPlan(
  tenantId: string,
  supabase: SupabaseClient<Database>
): Promise<CurrentPlan> {
  const subscriptionService = new SubscriptionService(supabase);

  try {
    // Get tenant subscription with plan details
    const subscription = await subscriptionService.getTenantSubscription(tenantId);
    
    if (!subscription || !subscription.plan) {
      // No subscription found - return default/free plan
      return {
        planCode: 'free',
        planName: 'Free Plan',
        features: ['document_upload'],
        status: 'inactive',
        expired: true,
      };
    }

    // Get features from the backend API
    let features: string[] = [];
    try {
      const response = await fetch(`/api/subscription/${tenantId}/features`);
      if (response.ok) {
        const data = await response.json();
        features = data.features || [];
      }
    } catch (_error) {
      console.warn('Failed to fetch features, using plan features as fallback:', error);
      // Fallback to plan features if API call fails
      features = extractFeaturesFromPlan(subscription);
    }

    // Determine if subscription is expired
    const now = new Date();
    const currentPeriodEnd = subscription.currentPeriodEnd ? new Date(subscription.currentPeriodEnd) : null;
    const trialEnd = subscription.trialEnd ? new Date(subscription.trialEnd) : null;
    
    let expired = false;
    if (subscription.status === 'canceled' || subscription.status === 'inactive') {
      expired = true;
    } else if (subscription.status === 'trialing' && trialEnd && now > trialEnd) {
      expired = true;
    } else if (currentPeriodEnd && now > currentPeriodEnd) {
      expired = true;
    }

    return {
      planCode: subscription.plan.code,
      planName: subscription.plan.name,
      features,
      status: subscription.status,
      expired,
      trialEnd,
      currentPeriodEnd,
    };
  } catch (_error) {
    console.error('Error getting current plan:', error);
    
    // Return fallback plan on error
    return {
      planCode: 'unknown',
      planName: 'Unknown Plan',
      features: [],
      status: 'inactive',
      expired: true,
    };
  }
}

/**
 * Extract features from plan data as fallback
 * @param subscription - Subscription with plan data
 * @returns Array of feature codes
 */
function extractFeaturesFromPlan(subscription: TenantSubscriptionDTO): string[] {
  const features: string[] = [];
  
  if (!subscription.plan?.features) {
    return features;
  }

  const planFeatures = subscription.plan.features as any;
  
  if (typeof planFeatures === 'object' && planFeatures !== null) {
    // Handle feature flags format: { "feature_voice_intake": true, "feature_calendar": false }
    for (const [key, value] of Object.entries(planFeatures)) {
      if (key.startsWith('feature_') && value === true) {
        const featureCode = key.replace('feature_', '');
        features.push(featureCode);
      }
    }
    
    // Handle features array format: { "features": ["voice_intake", "calendar"] }
    if (Array.isArray(planFeatures.features)) {
      features.push(...planFeatures.features);
    }
  }

  // Fallback based on plan code if no features found
  if (features.length === 0) {
    const planCode = subscription.plan.code.toLowerCase();
    
    if (planCode.includes('core') || planCode.includes('basic')) {
      features.push('document_upload', 'basic_search');
    } else if (planCode.includes('avr') || planCode.includes('professional')) {
      features.push('document_upload', 'basic_search', 'ai_research', 'case_management');
    } else if (planCode.includes('bundle') || planCode.includes('enterprise')) {
      features.push(
        'document_upload', 
        'basic_search', 
        'ai_research', 
        'case_management', 
        'voice_intake',
        'calendar_booking',
        'advanced_analytics'
      );
    }
  }

  return features;
}

/**
 * Get plan badge color based on plan code
 * @param planCode - The plan code
 * @returns Tailwind CSS classes for badge styling
 */
export function getPlanBadgeColor(planCode: string): string {
  const code = planCode.toLowerCase();
  
  if (code === 'core' || code.includes('core')) {
    return 'bg-slate-100 text-slate-700 border-slate-200';
  } else if (code === 'avr' || code.includes('avr')) {
    return 'bg-orange-100 text-orange-700 border-orange-200';
  } else if (code === 'bundle' || code.includes('bundle')) {
    return 'bg-emerald-100 text-emerald-700 border-emerald-200';
  } else {
    // Default for enterprise, custom, or unknown plans
    return 'bg-violet-100 text-violet-700 border-violet-200';
  }
}

/**
 * Get display name for plan code
 * @param planCode - The plan code
 * @returns Human-readable plan name
 */
export function getPlanDisplayName(planCode: string): string {
  const code = planCode.toLowerCase();
  
  if (code === 'core') return 'Core';
  if (code === 'avr') return 'AVR';
  if (code === 'bundle') return 'Bundle';
  if (code === 'enterprise') return 'Enterprise';
  if (code === 'free') return 'Free';
  if (code === 'trial') return 'Trial';
  
  // Capitalize first letter for unknown plans
  return planCode.charAt(0).toUpperCase() + planCode.slice(1);
}
