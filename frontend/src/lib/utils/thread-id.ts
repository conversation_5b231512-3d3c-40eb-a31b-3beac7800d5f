/**
 * Utilities for generating deterministic thread IDs for AG-UI.
 * This ensures proper tenant isolation in multi-tenant environments.
 */

import { createHash } from 'crypto';

/**
 * Generate a deterministic thread ID based on organization ID and user ID.
 * This ensures tenant isolation by ensuring the same user/org combination
 * always gets the same thread ID.
 *
 * @param orgId The organization/tenant ID
 * @param userId The user ID
 * @returns A deterministic thread ID hash
 */
export function generateThreadId(orgId: string, userId: string): string {
  // Ensure we have valid inputs
  if (!orgId || !userId) {
    console.warn('Missing orgId or userId for thread ID generation');
    // Fallback to a random ID with a prefix to indicate it's a fallback
    return `fallback-${Math.random().toString(36).substring(2, 15)}`;
  }

  // Combine orgId and userId to create a deterministic but unique value
  const combinedValue = `${orgId}:${userId}`;

  try {
    // Create SHA-256 hash of the combined value
    return createHash('sha256').update(combinedValue).digest('hex');
  } catch (_error) {
    console.error('Error generating thread ID:', error);
    // Fallback to a deterministic but simpler approach
    return `${orgId}-${userId}`.replace(/[^a-zA-Z0-9]/g, '-');
  }
}

/**
 * Generate an agent-specific thread ID to further isolate conversations
 * even within the same user/org context.
 *
 * @param orgId The organization/tenant ID
 * @param userId The user ID
 * @param agentName The agent name (e.g., "intake_agent", "document_agent")
 * @returns A deterministic thread ID hash specific to the agent
 */
export function generateAgentThreadId(orgId: string, userId: string, agentName: string): string {
  // Ensure we have valid inputs
  if (!orgId || !userId) {
    console.warn('Missing orgId or userId for agent thread ID generation');
    // Fallback to a random ID with a prefix to indicate it's a fallback
    return `fallback-agent-${agentName || 'unknown'}-${Math.random().toString(36).substring(2, 15)}`;
  }

  // Default agent name if not provided
  const agent = agentName || 'default';

  // Combine orgId, userId, and agentName for agent-specific threads
  const combinedValue = `${orgId}:${userId}:${agent}`;

  try {
    // Create SHA-256 hash of the combined value
    return createHash('sha256').update(combinedValue).digest('hex');
  } catch (_error) {
    console.error('Error generating agent thread ID:', error);
    // Fallback to a deterministic but simpler approach
    return `${orgId}-${userId}-${agent}`.replace(/[^a-zA-Z0-9]/g, '-');
  }
}

/**
 * For use during dev/testing when actual user/org IDs might not be available
 *
 * @param context A context string to identify the thread (e.g., "client-intake", "staff-document")
 * @param agentName Optional agent name for more specific thread isolation
 * @returns A deterministic thread ID for development
 */
export function generateDevThreadId(context: string, agentName?: string): string {
  try {
    // Ensure we have a valid context
    if (!context) {
      console.warn('Missing context for dev thread ID generation');
      context = 'unknown';
    }

    // For development, use a simpler approach
    const timestamp = new Date().toISOString().split('T')[0]; // YYYY-MM-DD
    const agentPart = agentName ? `-${agentName}` : '';

    return `dev-${context}${agentPart}-${timestamp}`;
  } catch (_error) {
    console.error('Error generating dev thread ID:', error);
    // Fallback to a random ID with a prefix
    return `dev-fallback-${Math.random().toString(36).substring(2, 15)}`;
  }
}

/**
 * Generate a session-specific thread ID that persists only for the current browser session
 *
 * @param prefix An optional prefix for the thread ID
 * @returns A session-specific thread ID
 */
export function generateSessionThreadId(prefix?: string): string {
  try {
    // Check if we already have a session thread ID in sessionStorage
    if (typeof window !== 'undefined') {
      const storageKey = `session-thread-id${prefix ? `-${prefix}` : ''}`;
      const existingId = sessionStorage.getItem(storageKey);

      if (existingId) {
        return existingId;
      }

      // Generate a new ID
      const newId = `session-${prefix || 'default'}-${Math.random().toString(36).substring(2, 15)}`;

      // Store it for future use in this session
      sessionStorage.setItem(storageKey, newId);

      return newId;
    }

    // Fallback for server-side rendering
    return `session-${prefix || 'default'}-${Math.random().toString(36).substring(2, 15)}`;
  } catch (_error) {
    console.error('Error generating session thread ID:', error);
    // Fallback to a random ID with a prefix
    return `session-fallback-${Math.random().toString(36).substring(2, 15)}`;
  }
}
