import { clsx, type ClassValue } from "clsx";
import { twMerge } from "tailwind-merge";

export function cn(...inputs: ClassValue[]): string {
  return twMerge(clsx(inputs));
}

/**
 * Check if the current environment is production
 * @returns boolean indicating if we're in production
 */
export function isProduction(): boolean {
  return process.env.NODE_ENV === "production";
}

/**
 * Format a date with a timezone consideration
 * @param date Date to format
 * @param userTimezone Optional user timezone
 * @returns Formatted date string
 */
export function formatDate(date: Date | string, userTimezone?: string): string {
  try {
    const dateObj = typeof date === "string" ? new Date(date) : date;
    return dateObj.toLocaleString("en-US", {
      timeZone: userTimezone,
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  } catch (_e) {
    console.error("Date formatting error:", e);
    return typeof date === "string" ? date : date.toString();
  }
}

/**
 * Check if a string is a valid UUID.
 * @param uuid String to check
 * @returns True if the string is a valid UUID, false otherwise
 */
export function isValidUUID(uuid: string): boolean {
  const regex =
    /^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[1-5][0-9a-fA-F]{3}-[89abAB][0-9a-fA-F]{3}-[0-9a-fA-F]{12}$/;
  return regex.test(uuid);
}

/**
 * Check if a string is a valid URL.
 * @param url The string to check.
 * @returns True if the string is a valid URL, false otherwise.
 */
export function isValidUrl(url: string): boolean {
  try {
    new URL(url);
    return true;
  } catch {
    return false;
  }
}

/**
 * Generate a random string of the specified length.
 * @param length The length of the string to generate.
 * @returns A random string of the specified length.
 */
export function generateRandomString(length: number): string {
  let result = "";
  const characters =
    "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
  for (let i = 0; i < length; i++) {
    result += characters.charAt(Math.floor(Math.random() * characters.length));
  }
  return result;
}

/**
 * Get a string representation of the error message.
 * @param error The error to get the message from.
 * @returns A string representation of the error message.
 */
export function getErrorMessage(error: unknown): string {
  if (error instanceof Error) {
    return error.message;
  }
  return String(error);
}

/**
 * Normalize a string by converting it to lowercase and trimming whitespace.
 * @param str The string to normalize.
 * @returns The normalized string.
 */
export function normalizeString(str: string): string {
  return str.toLowerCase().trim();
}

/**
 * Parse a JSON string safely, returning null if parsing fails.
 * @param jsonString The JSON string to parse.
 * @returns The parsed JSON object or null if parsing fails.
 */
export function parseJsonSafe<T>(jsonString: string): T | null {
  try {
    return JSON.parse(jsonString) as T;
  } catch {
    console.error("Error parsing JSON:");
    return null;
  }
}

/**
 * Sleep for the specified duration.
 * @param ms The number of milliseconds to sleep.
 * @returns A promise that resolves after the specified duration.
 */
export function sleep(ms: number): Promise<void> {
  return new Promise((resolve) => setTimeout(resolve, ms));
}

/**
 * Debounce a function by the specified wait time.
 * @param func The function to debounce.
 * @param wait The number of milliseconds to wait before calling the function.
 * @returns A debounced version of the function.
 */
export function debounce<T extends (...args: unknown[]) => void>(
  func: T,
  wait: number,
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout | null = null;

  return function executedFunction(...args: Parameters<T>): void {
    if (timeout) {
      clearTimeout(timeout);
    }
    timeout = setTimeout(() => {
      func(...args);
    }, wait);
  };
}
