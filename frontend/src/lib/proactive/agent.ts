/**
 * Proactive Agent System
 *
 * Generates personalized, context-aware messages for users based on their recent activity
 * stored in Neo4j. Designed to make AiLex feel more persistent and helpful.
 */

import { getRecentUserActivities } from '../neo4j/client';
import { ActivityRecord, ProactiveMessageConfig } from '../types';

/**
 * Generate a proactive welcome message for a user based on their recent activity
 *
 * @param userId The user's ID
 * @param tenantId The tenant ID
 * @param lastLoginDate Optional date of the user's last login
 * @returns Promise resolving to a personalized message
 */
export async function generateProactiveMessage({
  userId,
  tenantId,
  lastLoginDate,
  daysBack = 7,
  limit = 5
}: {
  userId: string;
  tenantId: string;
  lastLoginDate?: string;
  daysBack?: number;
  limit?: number;
}): Promise<{
  message: string;
  suggestions?: string[];
  hasRecentActivity: boolean;
}> {
  try {
    // Get the user's recent activities from Neo4j
    const recentActivities = await getRecentUserActivities({
      userId,
      daysBack,
      limit
    });

    // If there are no recent activities, return a default message
    if (!recentActivities.length) {
      return {
        message: "Welcome back! Would you like to start a new intake or browse your open cases?",
        suggestions: ["New Intake", "View Open Cases", "Check Deadlines"],
        hasRecentActivity: false
      };
    }

    // Use OpenAI to generate a personalized message based on recent activities
    const prompt = constructPromptFromActivities(recentActivities, lastLoginDate);
    const generatedMessage = await callOpenAI(prompt);

    return {
      message: generatedMessage.message,
      suggestions: generatedMessage.suggestions,
      hasRecentActivity: true
    };
  } catch (_error) {
    console.error('Failed to generate proactive message:', error);

    // Return a fallback message
    return {
      message: "Welcome back to AiLex. How can I assist you today?",
      suggestions: ["New Intake", "View Cases"],
      hasRecentActivity: false
    };
  }
}

/**
 * Construct a prompt for OpenAI based on recent user activities
 *
 * @param activities Recent user activities
 * @param lastLoginDate Optional date of user's last login
 * @returns Constructed prompt string
 */
function constructPromptFromActivities(
  activities: ActivityRecord[],
  lastLoginDate?: string
): string {
  const today = new Date().toLocaleDateString('en-US', {
    month: 'long',
    day: 'numeric'
  });

  const lastLogin = lastLoginDate
    ? new Date(lastLoginDate).toLocaleDateString('en-US', {
        month: 'long',
        day: 'numeric'
      })
    : 'recently';

  // Format the activities into a list
  const activityList = activities
    .map(activity => {
      const date = new Date(activity.time).toLocaleDateString('en-US', {
        month: 'long',
        day: 'numeric'
      });
      return `- ${date}: ${activity.summary}${activity.caseTitle ? ` in *${activity.caseTitle}*` : ''}`;
    })
    .join('\n');

  return `
You are AiLex, a helpful and personalized legal assistant. The user has just opened the app on ${today}.
${lastLoginDate ? `Their last login was on ${lastLogin}.` : ''}

Here are their recent activities:
${activityList}

Generate a friendly, personalized welcome message that:
1. Acknowledges their return
2. References their recent activity in a natural way
3. Offers 2-3 helpful suggestions for what they might want to do next
4. Keeps the tone professional but warm

Format your response as JSON with "message" and "suggestions" fields:
{
  "message": "Your personalized message here",
  "suggestions": ["Suggestion 1", "Suggestion 2", "Suggestion 3"]
}
`;
}

/**
 * Call OpenAI to generate a personalized message
 *
 * @param prompt The prompt to send to OpenAI
 * @returns The generated message and suggestions
 */
async function callOpenAI(prompt: string): Promise<{
  message: string;
  suggestions: string[];
}> {
  try {
    const response = await fetch('/api/ai/generate', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        prompt,
        model: 'gpt-4o'
      })
    });

    if (!response.ok) {
      throw new Error(`OpenAI API error: ${response.status}`);
    }

    const data = await response.json();

    try {
      // Parse the response as JSON
      const parsedResponse = JSON.parse(data.text);
      return {
        message: parsedResponse.message || "Welcome back to AiLex. How can I assist you today?",
        suggestions: parsedResponse.suggestions || ["View Cases", "New Intake"]
      };
    } catch (parseError) {
      console.error('Failed to parse OpenAI response as JSON:', parseError);
      // If parsing fails, treat the entire response as the message
      return {
        message: data.text || "Welcome back to AiLex. How can I assist you today?",
        suggestions: ["View Cases", "New Intake"]
      };
    }
  } catch (_error) {
    console.error('Error calling OpenAI:', error);
    return {
      message: "Welcome back to AiLex. How can I assist you today?",
      suggestions: ["View Cases", "New Intake"]
    };
  }
}
