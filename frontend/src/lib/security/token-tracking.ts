/**
 * Token tracking service for security monitoring
 * Tracks JWT token usage patterns and detects potential misuse
 */

import { createClient } from '@supabase/supabase-js';
import { sendAlert } from './alerting';

export interface TokenUsage {
  tokenId: string;
  userId: string;
  ipAddresses: string[];
  userAgents: string[];
  lastUsed: string;
  issuedAt: string;
  expiresAt: string;
  isRevoked: boolean;
}

/**
 * Track token usage
 * @param tokenId Token ID (jti claim)
 * @param userId User ID
 * @param ipAddress IP address
 * @param userAgent User agent
 */
export async function trackTokenUsage(
  tokenId: string,
  userId: string,
  ipAddress: string,
  userAgent: string
): Promise<void> {
  try {
    // Create a Supabase client with service role
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_KEY!
    );

    // Check if the token is already being tracked
    const { data: existingToken, error: fetchError } = await supabase
      .schema('security')
      .from('token_tracking')
      .select('*')
      .eq('token_id', tokenId)
      .single();

    if (fetchError && fetchError.code !== 'PGRST116') { // PGRST116 is "No rows found"
      console.error('Error checking token tracking:', fetchError);
      return;
    }

    const now = new Date().toISOString();

    if (existingToken) {
      // Token exists, update its usage
      const ipAddresses = existingToken.ip_addresses || [];
      const userAgents = existingToken.user_agents || [];

      // Add new IP address if not already tracked
      if (!ipAddresses.includes(ipAddress)) {
        ipAddresses.push(ipAddress);
      }

      // Add new user agent if not already tracked
      if (!userAgents.includes(userAgent)) {
        userAgents.push(userAgent);
      }

      // Update the token tracking record
      const { error: updateError } = await supabase
        .schema('security')
        .from('token_tracking')
        .update({
          ip_addresses: ipAddresses,
          user_agents: userAgents,
          last_used: now
        })
        .eq('token_id', tokenId);

      if (updateError) {
        console.error('Error updating token tracking:', updateError);
        return;
      }

      // Check for suspicious token usage
      if (ipAddresses.length > 3) {
        // Token used from too many IP addresses, could be shared or stolen
        await sendAlert(
          userId,
          'Security Alert: Suspicious Token Usage',
          `Your account token is being used from multiple locations (${ipAddresses.length} different IP addresses). If this wasn't you, please change your password immediately.`,
          'high',
          tokenId
        );

        // Log the suspicious activity
        await supabase
          .schema('security')
          .from('events')
          .insert({
            event_type: 'suspicious.token_misuse',
            event_category: 'suspicious',
            user_id: userId,
            ip_address: ipAddress,
            user_agent: userAgent,
            details: {
              tokenId,
              ipAddresses,
              reason: 'Token used from multiple IP addresses'
            },
            created_at: now
          });
      }
    } else {
      // New token, create a tracking record
      const { error: insertError } = await supabase
        .schema('security')
        .from('token_tracking')
        .insert({
          token_id: tokenId,
          user_id: userId,
          ip_addresses: [ipAddress],
          user_agents: [userAgent],
          issued_at: now,
          last_used: now,
          is_revoked: false
        });

      if (insertError) {
        console.error('Error creating token tracking:', insertError);
        return;
      }
    }
  } catch (_err) {
    console.error('Error tracking token usage:', err);
  }
}

/**
 * Revoke a token
 * @param tokenId Token ID (jti claim)
 * @param reason Reason for revocation
 */
export async function revokeToken(tokenId: string, reason: string): Promise<void> {
  try {
    // Create a Supabase client with service role
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_KEY!
    );

    // Get the token tracking record
    const { data: token, error: fetchError } = await supabase
      .schema('security')
      .from('token_tracking')
      .select('*')
      .eq('token_id', tokenId)
      .single();

    if (fetchError) {
      console.error('Error getting token tracking:', fetchError);
      return;
    }

    // Mark the token as revoked
    const { error: updateError } = await supabase
      .schema('security')
      .from('token_tracking')
      .update({
        is_revoked: true,
        revoked_at: new Date().toISOString(),
        revocation_reason: reason
      })
      .eq('token_id', tokenId);

    if (updateError) {
      console.error('Error revoking token:', updateError);
      return;
    }

    // Log the token revocation
    await supabase
      .schema('security')
      .from('events')
      .insert({
        event_type: 'auth.token_revoked',
        event_category: 'authentication',
        user_id: token.user_id,
        details: {
          tokenId,
          reason
        },
        created_at: new Date().toISOString()
      });

    // Send an alert to the user
    await sendAlert(
      token.user_id,
      'Security Alert: Session Terminated',
      `One of your active sessions was terminated for security reasons: ${reason}. If this wasn't you, please contact support.`,
      'medium',
      tokenId
    );
  } catch (_err) {
    console.error('Error revoking token:', err);
  }
}

/**
 * Check if a token is revoked
 * @param tokenId Token ID (jti claim)
 * @returns True if the token is revoked, false otherwise
 */
export async function isTokenRevoked(tokenId: string): Promise<boolean> {
  try {
    // Create a Supabase client with service role
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_KEY!
    );

    // Check if the token is revoked
    const { data, error } = await supabase
      .schema('security')
      .from('token_tracking')
      .select('is_revoked')
      .eq('token_id', tokenId)
      .single();

    if (error) {
      console.error('Error checking token revocation:', error);
      return false;
    }

    return data.is_revoked;
  } catch (_err) {
    console.error('Error checking token revocation:', err);
    return false;
  }
}

/**
 * Get all active tokens for a user
 * @param userId User ID
 * @returns List of active tokens
 */
export async function getUserActiveTokens(userId: string): Promise<TokenUsage[]> {
  try {
    // Create a Supabase client with service role
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_KEY!
    );

    // Get the user's active tokens
    const { data, error } = await supabase
      .schema('security')
      .from('token_tracking')
      .select('*')
      .eq('user_id', userId)
      .eq('is_revoked', false);

    if (error) {
      console.error('Error getting user active tokens:', error);
      return [];
    }

    return data.map(token => ({
      tokenId: token.token_id,
      userId: token.user_id,
      ipAddresses: token.ip_addresses || [],
      userAgents: token.user_agents || [],
      lastUsed: token.last_used,
      issuedAt: token.issued_at,
      expiresAt: token.expires_at || '',
      isRevoked: token.is_revoked
    }));
  } catch (_err) {
    console.error('Error getting user active tokens:', err);
    return [];
  }
}

/**
 * Revoke all tokens for a user
 * @param userId User ID
 * @param reason Reason for revocation
 */
export async function revokeAllUserTokens(userId: string, reason: string): Promise<void> {
  try {
    // Create a Supabase client with service role
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_KEY!
    );

    // Get all active tokens for the user
    const { data: tokens, error: fetchError } = await supabase
      .schema('security')
      .from('token_tracking')
      .select('token_id')
      .eq('user_id', userId)
      .eq('is_revoked', false);

    if (fetchError) {
      console.error('Error getting user tokens:', fetchError);
      return;
    }

    // Mark all tokens as revoked
    const now = new Date().toISOString();
    const { error: updateError } = await supabase
      .schema('security')
      .from('token_tracking')
      .update({
        is_revoked: true,
        revoked_at: now,
        revocation_reason: reason
      })
      .eq('user_id', userId)
      .eq('is_revoked', false);

    if (updateError) {
      console.error('Error revoking user tokens:', updateError);
      return;
    }

    // Log the token revocation
    await supabase
      .schema('security')
      .from('events')
      .insert({
        event_type: 'auth.all_tokens_revoked',
        event_category: 'authentication',
        user_id: userId,
        details: {
          tokenCount: tokens.length,
          reason
        },
        created_at: now
      });

    // Send an alert to the user
    await sendAlert(
      userId,
      'Security Alert: All Sessions Terminated',
      `All your active sessions were terminated for security reasons: ${reason}. If this wasn't you, please contact support.`,
      'high',
      'all-tokens'
    );
  } catch (_err) {
    console.error('Error revoking all user tokens:', err);
  }
}
