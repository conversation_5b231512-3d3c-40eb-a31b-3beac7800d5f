import { SupabaseClient } from '@supabase/supabase-js';
import { Database } from '@/lib/supabase/database.types';
import { createSecurityClient } from './security-client';

/**
 * Security event categories
 */
export enum SecurityEventCategory {
  AUTHENTICATION = 'authentication',
  AUTHORIZATION = 'authorization',
  DATA_ACCESS = 'data_access',
  SUSPICIOUS = 'suspicious',
  SYSTEM = 'system',
  OTHER = 'other'
}

/**
 * Security event details
 */
export interface SecurityEventDetails {
  reason?: string;
  userId?: string;
  tenantId?: string | null;
  ipAddress?: string | null;
  userAgent?: string | null;
  resource?: string;
  status?: number;
  error?: string;
  requiredRoles?: string[];
  userRoles?: string[];
  errorStack?: string;
  [key: string]: any; // Allow additional arbitrary details
}

/**
 * Log a security event to the security.events table
 *
 * @param supabase The Supabase client instance to use.
 * @param eventType Type of security event (e.g., 'auth.login', 'data.sensitive_access')
 * @param details Additional details about the event
 * @returns A promise that resolves to void
 */
export async function logSecurityEvent(
  supabase: SupabaseClient<Database>,
  eventType: string,
  details: SecurityEventDetails
): Promise<void> {
  // Remove sensitive details before logging if necessary, e.g., full tokens
  const logPayload = {
    event_type: eventType,
    details: details, // Store the whole details object as JSONB
    user_id: details.userId,
    tenant_id: details.tenantId,
    ip_address: details.ipAddress,
    user_agent: details.userAgent,
    timestamp: new Date().toISOString(), // Use server time
  };

  // Use the provided Supabase client
  try {
    const { error } = await supabase
      .schema('security')
      .from('events') // Access the table from the security schema
      .insert(logPayload);

    if (error) {
      console.error('Error logging security event:', error);
    }
  } catch (_error) {
    console.error('Error logging security event:', error);
  }
}

/**
 * Log an authentication event
 *
 * @param supabase The Supabase client instance to use.
 * @param action The authentication action (login, logout, etc.)
 * @param details Additional details about the event
 * @returns A promise that resolves to void
 */
export async function logAuthEvent(
  supabase: SupabaseClient<Database>,
  action: 'login' | 'logout' | 'signup' | 'password_reset' | 'token_refresh' | 'mfa_challenge',
  details: SecurityEventDetails
): Promise<void> {
  return logSecurityEvent(supabase, `auth.${action}`, details);
}

/**
 * Log a suspicious activity event
 *
 * @param supabase The Supabase client instance to use.
 * @param activity The type of suspicious activity
 * @param details Additional details about the event
 * @returns A promise that resolves to void
 */
export async function logSuspiciousActivity(
  supabase: SupabaseClient<Database>,
  activity: 'multiple_failures' | 'unusual_location' | 'unusual_time' | 'brute_force',
  details: SecurityEventDetails
): Promise<void> {
  return logSecurityEvent(supabase, `suspicious.${activity}`, details);
}

/**
 * Log a data access event
 *
 * @param supabase The Supabase client instance to use.
 * @param access The type of data access
 * @param details Additional details about the event
 * @returns A promise that resolves to void
 */
export async function logDataAccessEvent(
  supabase: SupabaseClient<Database>,
  access: 'sensitive_access' | 'export' | 'bulk_operation',
  details: SecurityEventDetails
): Promise<void> {
  return logSecurityEvent(supabase, `data.${access}`, details);
}

/**
 * Create a digital signature for an action to ensure non-repudiation
 *
 * @param supabase The Supabase client instance to use.
 * @param action The action being performed
 * @param data The data associated with the action
 * @returns A promise that resolves to the signature or null if there was an error
 */
export async function createActionSignature(
  supabase: SupabaseClient<Database>,
  action: string,
  data: Record<string, any>
): Promise<string | null> {
  try {
    // Use security client for RPC calls
    const securityClient = createSecurityClient(supabase);
    return await securityClient.createActionSignature(action, data);
  } catch (_err) {
    console.error('Error creating action signature:', err)
    return null
  }
}

/**
 * Send a security alert to an external system (e.g., Slack, email)
 *
 * @param supabase The Supabase client instance to use.
 * @param eventType Type of security event
 * @param details Additional details about the event
 * @returns A promise that resolves to true if the alert was sent successfully
 */
export async function sendSecurityAlert(
  supabase: SupabaseClient<Database>,
  eventType: string,
  details: SecurityEventDetails
): Promise<boolean> {
  try {
    // Log the event to our database first
    await logSecurityEvent(supabase, `alert.${eventType}`, details)

    // This would typically call an API route that handles the external notification
    const response = await fetch('/api/security/alert', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        eventType,
        details
      })
    })

    return response.ok
  } catch (_error) {
    console.error('Failed to send security alert:', error)
    return false
  }
}

/**
 * Middleware function to log page access attempts
 * Can be used with Next.js middleware
 */
export function logPageAccess(
  supabase: SupabaseClient<Database>,
  userId: string | undefined,
  path: string,
  isAuthorized: boolean
) {
  return logSecurityEvent(
    supabase,
    isAuthorized ? 'page_access' : 'unauthorized_page_access',
    {
      userId,
      path,
      timestamp: new Date().toISOString()
    }
  )
}
