/**
 * Geolocation service for security event tracking
 * Uses ipapi.co for IP-to-location mapping
 */

export interface GeolocationData {
  ip: string;
  city: string | null;
  region: string | null;
  country: string | null;
  country_name: string | null;
  latitude: number | null;
  longitude: number | null;
  timezone: string | null;
  org: string | null; // ISP or organization
}

/**
 * Get geolocation data for an IP address
 * @param ip IP address to lookup
 * @returns Geolocation data or null if lookup failed
 */
export async function getGeolocationData(ip: string): Promise<GeolocationData | null> {
  // Skip lookup for localhost or private IPs
  if (ip === 'localhost' || ip === '127.0.0.1' || ip === '::1' || ip === 'unknown') {
    return {
      ip,
      city: 'Local',
      region: 'Local',
      country: 'Local',
      country_name: 'Local Environment',
      latitude: 0,
      longitude: 0,
      timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
      org: 'Local'
    };
  }

  try {
    // Use ipapi.co free API (limited to 1000 requests per day)
    const response = await fetch(`https://ipapi.co/${ip}/json/`);

    if (!response.ok) {
      console.error(`Geolocation lookup failed: ${response.status} ${response.statusText}`);
      return null;
    }

    const data = await response.json();

    // Check for error response
    if (data.error) {
      console.error(`Geolocation API error: ${data.reason}`);
      return null;
    }

    return {
      ip: data.ip,
      city: data.city || null,
      region: data.region || null,
      country: data.country || null,
      country_name: data.country_name || null,
      latitude: data.latitude || null,
      longitude: data.longitude || null,
      timezone: data.timezone || null,
      org: data.org || null
    };
  } catch (_error) {
    console.error('Error fetching geolocation data:', error);
    return null;
  }
}
