/**
 * Token Tracker
 *
 * Integrates with the existing security infrastructure to provide enhanced token security:
 * - Tracks token usage across devices
 * - Detects potential token theft
 * - Provides token revocation capabilities
 */

import { supabase } from '@/lib/supabase/client';
import { useSecurity } from './context';
import { createSecurityClient } from './security-client';

// Create security client instance
const securityClient = createSecurityClient(supabase);

// Interface for parsed JWT token
interface ParsedToken {
  jti?: string;        // JWT ID
  sub: string;         // Subject (user ID)
  exp: number;         // Expiration time
  iat: number;         // Issued at time
  aud: string;         // Audience
  role?: string;       // User role
  email?: string;      // User email
  tenant_id?: string;  // Tenant ID
}

/**
 * Parse a JWT token
 * @param token JWT token string
 * @returns Parsed token payload or null if invalid
 */
export function parseJwtToken(token: string): ParsedToken | null {
  try {
    // Split the token and get the payload (second part)
    const parts = token.split('.');
    if (parts.length !== 3) return null;

    // Decode the base64 payload
    const payload = Buffer.from(parts[1], 'base64').toString('utf-8');
    return JSON.parse(payload);
  } catch (_error) {
    console.error('Error parsing JWT token:', error);
    return null;
  }
}

/**
 * Check if a session has been revoked
 * @param sessionId Session ID (JTI claim)
 * @returns Promise resolving to true if session is revoked
 */
export async function isSessionRevoked(sessionId: string): Promise<boolean> {
  try {
    return await securityClient.isSessionRevoked(sessionId);
  } catch (_error) {
    console.error('Failed to check if session is revoked:', error);
    return false;
  }
}

/**
 * Revoke a specific session
 * @param sessionId Session ID (JTI claim)
 * @param reason Reason for revocation
 * @returns Promise resolving to true if revocation was successful
 */
export async function revokeSession(
  sessionId: string,
  reason: string
): Promise<boolean> {
  try {
    return await securityClient.revokeSession(sessionId, reason);
  } catch (_error) {
    console.error('Failed to revoke session:', error);
    return false;
  }
}

/**
 * Revoke all sessions for the current user
 * @param reason Reason for revocation
 * @returns Promise resolving to true if revocation was successful
 */
export async function revokeAllSessions(reason: string): Promise<boolean> {
  try {
    return await securityClient.revokeAllUserSessions(reason);
  } catch (_error) {
    console.error('Failed to revoke all sessions:', error);
    return false;
  }
}

/**
 * Log a token usage event
 * @param tokenId Token ID (JTI claim)
 * @param userId User ID
 * @param eventType Event type (e.g., 'token_used', 'token_refreshed')
 * @returns Promise resolving to true if logging was successful
 */
export async function logTokenUsage(
  tokenId: string,
  userId: string,
  eventType: string
): Promise<boolean> {
  try {
    // Use the existing security context to get the device fingerprint
    const { deviceFingerprint } = useSecurity();

    return await securityClient.logAuthEvent(
      eventType,
      'auth.token',
      tokenId,
      {
        userId,
        deviceFingerprint
      }
    );
  } catch (_error) {
    console.error('Failed to log token usage:', error);
    return false;
  }
}

/**
 * Get active sessions for the current user
 * @returns Promise resolving to array of active sessions
 */
export async function getActiveSessions(): Promise<any[]> {
  try {
    // Get the current user
    const { data: { user } } = await supabase.auth.getUser();

    if (!user) {
      return [];
    }

    // Query the auth.sessions table to get active sessions
    // Use any type assertion to bypass TypeScript schema limitations
    const { data, error } = await (supabase as any)
      .from('auth.sessions')
      .select('*')
      .eq('user_id', user.id);

    if (error) {
      console.error('Error getting active sessions:', error);
      return [];
    }

    return data || [];
  } catch (_error) {
    console.error('Failed to get active sessions:', error);
    return [];
  }
}

/**
 * Get device information for the current user
 * @returns Promise resolving to array of devices
 */
export async function getUserDevices(): Promise<any[]> {
  try {
    return await securityClient.getUserDevices();
  } catch (_error) {
    console.error('Failed to get user devices:', error);
    return [];
  }
}
