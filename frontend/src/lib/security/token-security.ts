/**
 * Token Security Manager
 *
 * Handles token security features including:
 * - Token tracking
 * - Device binding
 * - Suspicious token usage detection
 * - Token revocation
 */

import { supabase } from '@/lib/supabase/client';
import { generateDeviceFingerprint, logSuspiciousTokenUsage } from './auth-security-logger';
import { <PERSON>uff<PERSON> } from 'buffer';
import { createSecurityClient } from './security-client';

// Create security client instance
const securityClient = createSecurityClient(supabase);

// Interface for parsed JWT token
interface ParsedToken {
  jti?: string;        // JWT ID
  sub: string;         // Subject (user ID)
  exp: number;         // Expiration time
  iat: number;         // Issued at time
  aud: string;         // Audience
  role?: string;       // User role
  email?: string;      // User email
  tenant_id?: string;  // Tenant ID
  device_id?: string;  // Device ID (if we add it to the token)
}

/**
 * Parse a JWT token
 * @param token JWT token string
 * @returns Parsed token payload or null if invalid
 */
export function parseJwtToken(token: string): ParsedToken | null {
  try {
    // Split the token and get the payload (second part)
    const parts = token.split('.');
    if (parts.length !== 3) return null;

    // Decode the base64 payload
    const payload = Buffer.from(parts[1], 'base64').toString('utf-8');
    return JSON.parse(payload);
  } catch (_error) {
    console.error('Error parsing JWT token:', error);
    return null;
  }
}

/**
 * Check if a token is associated with the current device
 * @param userId User ID
 * @param tokenId Token ID (JTI claim)
 * @returns Promise resolving to true if token is valid for this device
 */
export async function validateTokenDevice(
  userId: string,
  tokenId?: string
): Promise<boolean> {
  try {
    // If no token ID, we can't validate
    if (!tokenId) return true; // Default to true for tokens without JTI

    // Get current device fingerprint
    const deviceFingerprint = await generateDeviceFingerprint();

    // Use our security client to validate the token-device association
    const valid = await securityClient.validateTokenDevice(tokenId, deviceFingerprint);

    // If validation fails, log suspicious activity
    if (!valid) {
      await logSuspiciousTokenUsage(
        supabase,
        userId,
        deviceFingerprint,
        'unknown',
        tokenId,
        'Token used on unexpected device'
      );
    }

    return valid;
  } catch (_error) {
    console.error('Failed to validate token device:', error);
    return false;
  }
}

/**
 * Register a token with the current device
 * @param userId User ID
 * @param tokenId Token ID (JTI claim)
 * @returns Promise resolving to true if registration was successful
 */
export async function registerTokenWithDevice(
  userId: string,
  tokenId: string
): Promise<boolean> {
  try {
    // Get current device fingerprint
    const deviceFingerprint = await generateDeviceFingerprint();

    // Get browser and device details
    const deviceDetails = {
      userId: userId,
      userAgent: typeof navigator !== 'undefined' ? navigator.userAgent : 'unknown',
      timestamp: new Date().toISOString()
    };

    // Use our security client to register the token-device association
    return await securityClient.registerTokenDevice(tokenId, deviceFingerprint, deviceDetails);
  } catch (_error) {
    console.error('Failed to register token with device:', error);
    return false;
  }
}

/**
 * Check if a token has been revoked
 * @param tokenId Token ID (JTI claim)
 * @returns Promise resolving to true if token is revoked
 */
export async function isTokenRevoked(tokenId: string): Promise<boolean> {
  try {
    return await securityClient.isTokenRevoked(tokenId);
  } catch (_error) {
    console.error('Failed to check if token is revoked:', error);
    return true; // Assume revoked if there's an error
  }
}

/**
 * Revoke a specific token
 * @param tokenId Token ID (JTI claim)
 * @param userId User ID
 * @param reason Reason for revocation
 * @returns Promise resolving to true if revocation was successful
 */
export async function revokeToken(
  tokenId: string,
  userId: string,
  reason: string
): Promise<boolean> {
  try {
    return await securityClient.revokeToken(tokenId, reason);
  } catch (_error) {
    console.error('Failed to revoke token:', error);
    return false;
  }
}

/**
 * Revoke all tokens for a user
 * @param userId User ID
 * @param reason Reason for revocation
 * @param exceptCurrentToken Whether to exclude the current token
 * @returns Promise resolving to true if revocation was successful
 */
export async function revokeAllUserTokens(
  userId: string,
  reason: string,
  exceptCurrentToken: boolean = true
): Promise<boolean> {
  try {
    return await securityClient.revokeAllUserTokens(userId, reason);
  } catch (_error) {
    console.error('Failed to revoke all user tokens:', error);
    return false;
  }
}
