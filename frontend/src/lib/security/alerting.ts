/**
 * Security alerting system
 * Sends alerts for security events based on severity and configuration
 */

import { createClient } from '@supabase/supabase-js';
import { AnomalyScore } from './anomaly-detection';

export interface AlertConfig {
  userId: string;
  email: boolean;
  inApp: boolean;
  sms: boolean;
  minSeverity: 'low' | 'medium' | 'high' | 'critical';
}

export interface Alert {
  id: string;
  userId: string;
  title: string;
  message: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  eventId: string;
  read: boolean;
  createdAt: string;
}

/**
 * Send an alert for a security event
 * @param userId User ID
 * @param title Alert title
 * @param message Alert message
 * @param severity Alert severity
 * @param eventId Related event ID
 */
export async function sendAlert(
  userId: string,
  title: string,
  message: string,
  severity: 'low' | 'medium' | 'high' | 'critical',
  eventId: string
): Promise<void> {
  try {
    // Get user's alert configuration
    const config = await getUserAlertConfig(userId);

    // Check if the alert meets the minimum severity threshold
    const severityLevels = { low: 1, medium: 2, high: 3, critical: 4 };
    const configSeverity = severityLevels[config.minSeverity] || 1;
    const alertSeverity = severityLevels[severity] || 1;

    if (alertSeverity < configSeverity) {
      console.log(`Alert severity ${severity} below threshold ${config.minSeverity}, not sending`);
      return;
    }

    // Create a Supabase client with service role
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_KEY!
    );

    // Create the alert in the database
    const { data, error } = await supabase
      .schema('security')
      .from('alerts')
      .insert({
        user_id: userId,
        title,
        message,
        severity,
        event_id: eventId,
        read: false,
        created_at: new Date().toISOString()
      })
      .select('id')
      .single();

    if (error) {
      console.error('Error creating alert:', error);
      return;
    }

    const alertId = data.id;

    // Send email alert if configured
    if (config.email) {
      await sendEmailAlert(userId, title, message, severity, alertId);
    }

    // Send SMS alert if configured
    if (config.sms) {
      await sendSmsAlert(userId, title, message, severity, alertId);
    }

    // Log the alert as a security event
    await supabase
      .schema('security')
      .from('events')
      .insert({
        event_type: 'system.alert_sent',
        event_category: 'system',
        user_id: userId,
        details: {
          alertId,
          title,
          severity,
          relatedEventId: eventId
        },
        created_at: new Date().toISOString()
      });
  } catch (_err) {
    console.error('Error sending alert:', err);
  }
}

/**
 * Send an alert for an anomaly
 * @param anomaly Anomaly score and details
 */
export async function sendAnomalyAlert(anomaly: AnomalyScore): Promise<void> {
  const title = `Security Alert: ${getSeverityText(anomaly.severity)} Risk Detected`;
  const message = `We detected unusual activity on your account: ${anomaly.reasons.join(', ')}. If this wasn't you, please contact support immediately.`;

  await sendAlert(
    anomaly.userId,
    title,
    message,
    anomaly.severity,
    anomaly.eventId
  );
}

/**
 * Get a user's alert configuration
 * @param userId User ID
 * @returns Alert configuration
 */
async function getUserAlertConfig(userId: string): Promise<AlertConfig> {
  try {
    // Create a Supabase client with service role
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_KEY!
    );

    // Get the user's alert configuration
    const { data, error } = await supabase
      .schema('security')
      .from('alert_configs')
      .select('*')
      .eq('user_id', userId)
      .single();

    if (error || !data) {
      console.log('No alert config found for user, using defaults');
      // Return default configuration if none found
      return {
        userId,
        email: true,
        inApp: true,
        sms: false,
        minSeverity: 'medium'
      };
    }

    return {
      userId: data.user_id,
      email: data.email,
      inApp: data.in_app,
      sms: data.sms,
      minSeverity: data.min_severity
    };
  } catch (_err) {
    console.error('Error getting user alert config:', err);
    // Return default configuration in case of error
    return {
      userId,
      email: true,
      inApp: true,
      sms: false,
      minSeverity: 'medium'
    };
  }
}

/**
 * Send an email alert
 * @param userId User ID
 * @param title Alert title
 * @param message Alert message
 * @param severity Alert severity
 * @param alertId Alert ID
 */
async function sendEmailAlert(
  userId: string,
  title: string,
  message: string,
  severity: 'low' | 'medium' | 'high' | 'critical',
  alertId: string
): Promise<void> {
  try {
    // In a real implementation, this would send an email
    // For now, we'll just log it
    console.log(`[EMAIL ALERT] To: ${userId}, Title: ${title}, Severity: ${severity}`);
    console.log(`[EMAIL ALERT] Message: ${message}`);
    console.log(`[EMAIL ALERT] Alert ID: ${alertId}`);
  } catch (_err) {
    console.error('Error sending email alert:', err);
  }
}

/**
 * Send an SMS alert
 * @param userId User ID
 * @param title Alert title
 * @param message Alert message
 * @param severity Alert severity
 * @param alertId Alert ID
 */
async function sendSmsAlert(
  userId: string,
  title: string,
  message: string,
  severity: 'low' | 'medium' | 'high' | 'critical',
  alertId: string
): Promise<void> {
  try {
    // In a real implementation, this would send an SMS
    // For now, we'll just log it
    console.log(`[SMS ALERT] To: ${userId}, Title: ${title}, Severity: ${severity}`);
    console.log(`[SMS ALERT] Message: ${message}`);
    console.log(`[SMS ALERT] Alert ID: ${alertId}`);
  } catch (_err) {
    console.error('Error sending SMS alert:', err);
  }
}

/**
 * Get user-friendly text for severity level
 * @param severity Severity level
 * @returns User-friendly text
 */
function getSeverityText(severity: 'low' | 'medium' | 'high' | 'critical'): string {
  switch (severity) {
    case 'low':
      return 'Low';
    case 'medium':
      return 'Medium';
    case 'high':
      return 'High';
    case 'critical':
      return 'Critical';
    default:
      return 'Unknown';
  }
}

/**
 * Get all alerts for a user
 * @param userId User ID
 * @returns List of alerts
 */
export async function getUserAlerts(userId: string): Promise<Alert[]> {
  try {
    // Create a Supabase client with service role
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_KEY!
    );

    // Get the user's alerts
    const { data, error } = await supabase
      .schema('security')
      .from('alerts')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error getting user alerts:', error);
      return [];
    }

    return data.map(alert => ({
      id: alert.id,
      userId: alert.user_id,
      title: alert.title,
      message: alert.message,
      severity: alert.severity,
      eventId: alert.event_id,
      read: alert.read,
      createdAt: alert.created_at
    }));
  } catch (_err) {
    console.error('Error getting user alerts:', err);
    return [];
  }
}

/**
 * Mark an alert as read
 * @param alertId Alert ID
 */
export async function markAlertAsRead(alertId: string): Promise<void> {
  try {
    // Create a Supabase client with service role
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_KEY!
    );

    // Mark the alert as read
    await supabase
      .schema('security')
      .from('alerts')
      .update({ read: true })
      .eq('id', alertId);
  } catch (_err) {
    console.error('Error marking alert as read:', err);
  }
}
