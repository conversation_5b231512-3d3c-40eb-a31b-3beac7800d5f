/**
 * Auth Security Logger
 *
 * Provides specialized logging for authentication-related security events
 * This module is focused on detecting and logging potential security issues
 * related to authentication, tokens, and session management.
 */

import { SupabaseClient } from '@supabase/supabase-js';
import { Database, Json } from '@/lib/supabase/database.types';
import { supabase } from '@/lib/supabase/client';
import { v4 as uuidv4 } from 'uuid';
import { logSecurityEvent, SecurityEventDetails } from './forensics';
import { createSecurityClient } from './security-client';

// Device fingerprint generation
import FingerprintJS from '@fingerprintjs/fingerprintjs';

// Initialize fingerprint provider (lazy-loaded)
let fpPromise: Promise<any> | null = null;

/**
 * Get the fingerprint provider instance (lazy initialization)
 */
function getFingerprint() {
  if (!fpPromise && typeof window !== 'undefined') {
    fpPromise = FingerprintJS.load();
  }
  return fpPromise;
}

/**
 * Generate a device fingerprint
 * @returns Promise resolving to device fingerprint
 */
export async function generateDeviceFingerprint(): Promise<string> {
  try {
    const fp = await getFingerprint();
    if (!fp) return 'unknown-device';

    const result = await fp.get();
    return result.visitorId;
  } catch (_error) {
    console.error('Error generating device fingerprint:', error);
    return `error-${uuidv4().slice(0, 8)}`;
  }
}

/**
 * Log a successful authentication
 * @param supabaseClient Supabase client instance
 * @param userId User ID
 * @param email User email
 * @param deviceFingerprint Device fingerprint
 * @param ipAddress IP address (optional)
 */
export async function logSuccessfulAuth(
  supabaseClient: SupabaseClient<Database>,
  userId: string,
  email: string,
  deviceFingerprint: string,
  ipAddress?: string
) {
  try {
    // Log to security events
    const details: SecurityEventDetails = {
      userId,
      email,
      deviceFingerprint,
      ipAddress,
      timestamp: new Date().toISOString()
    };
    await logSecurityEvent(supabaseClient, 'auth.login_success', details);

    // Register/update device fingerprint in database
    await registerDeviceFingerprint(userId, deviceFingerprint);

    return true;
  } catch (_error) {
    console.error('Failed to log successful authentication:', error);
    return false;
  }
}

/**
 * Log a failed authentication attempt
 * @param supabaseClient Supabase client instance
 * @param email Email that was used in the attempt
 * @param reason Reason for failure
 * @param deviceFingerprint Device fingerprint
 * @param ipAddress IP address (optional)
 */
export async function logFailedAuth(
  supabaseClient: SupabaseClient<Database>,
  email: string,
  reason: string,
  deviceFingerprint: string,
  ipAddress?: string
) {
  try {
    const details: SecurityEventDetails = {
      email,
      reason,
      deviceFingerprint,
      ipAddress,
      timestamp: new Date().toISOString()
    };
    await logSecurityEvent(supabaseClient, 'auth.login_failed', details);

    return true;
  } catch (_error) {
    console.error('Failed to log failed authentication:', error);
    return false;
  }
}

/**
 * Register or update a device fingerprint in the database
 * @param userId User ID
 * @param fingerprint Device fingerprint
 * @param deviceName Optional device name
 */
export async function registerDeviceFingerprint(
  userId: string,
  fingerprint: string,
  deviceName?: string
) {
  try {
    // Get user agent and use it for device name if not provided
    const userAgent = typeof window !== 'undefined' ? window.navigator.userAgent : 'unknown';
    const deviceNameToUse = deviceName || inferDeviceNameFromUserAgent(userAgent);

    // Use our security client to register the device fingerprint
    const securityClient = createSecurityClient(supabase);
    const fingerprintData = {
      user_id: userId,
      fingerprint: fingerprint,
      user_agent: userAgent,
      device_name: deviceNameToUse
    };

    return await securityClient.registerDeviceFingerprint(fingerprintData);
  } catch (_error) {
    console.error('Failed to register device fingerprint:', error);
    return false;
  }
}

/**
 * Log a token refresh event
 * @param supabaseClient Supabase client instance
 * @param userId User ID
 * @param deviceFingerprint Device fingerprint
 * @param tokenId Token ID (JTI claim)
 */
export async function logTokenRefresh(
  supabaseClient: SupabaseClient<Database>,
  userId: string,
  deviceFingerprint: string,
  tokenId?: string
) {
  try {
    const details: SecurityEventDetails = {
      userId,
      deviceFingerprint,
      tokenId,
      timestamp: new Date().toISOString()
    };
    await logSecurityEvent(supabaseClient, 'auth.token_refresh', details);

    return true;
  } catch (_error) {
    console.error('Failed to log token refresh:', error);
    return false;
  }
}

/**
 * Log a suspicious token usage event
 * @param supabaseClient Supabase client instance
 * @param userId User ID
 * @param deviceFingerprint Current device fingerprint
 * @param expectedFingerprint Expected device fingerprint
 * @param tokenId Token ID (JTI claim)
 * @param reason Reason for suspicion
 */
export async function logSuspiciousTokenUsage(
  supabaseClient: SupabaseClient<Database>,
  userId: string,
  deviceFingerprint: string,
  expectedFingerprint: string,
  tokenId: string,
  reason: string
) {
  try {
    const details: SecurityEventDetails = {
      userId,
      deviceFingerprint,
      expectedFingerprint,
      tokenId,
      reason,
      timestamp: new Date().toISOString(),
      severity: 'high'
    };
    await logSecurityEvent(supabaseClient, 'auth.suspicious_token_usage', details);

    // Consider additional actions like token revocation here

    return true;
  } catch (_error) {
    console.error('Failed to log suspicious token usage:', error);
    return false;
  }
}

/**
 * Infer a device name from user agent string
 * @param userAgent User agent string
 * @returns Inferred device name
 */
function inferDeviceNameFromUserAgent(userAgent: string): string {
  // Extract device information from user agent
  let deviceName = 'Unknown Device';

  // Check for mobile devices
  if (/iPhone/.test(userAgent)) {
    deviceName = 'iPhone';
  } else if (/iPad/.test(userAgent)) {
    deviceName = 'iPad';
  } else if (/Android/.test(userAgent)) {
    deviceName = 'Android Device';
    if (/Mobile/.test(userAgent)) {
      deviceName = 'Android Phone';
    } else {
      deviceName = 'Android Tablet';
    }
  } else if (/Windows/.test(userAgent)) {
    deviceName = 'Windows PC';
  } else if (/Macintosh/.test(userAgent)) {
    deviceName = 'Mac';
  } else if (/Linux/.test(userAgent)) {
    deviceName = 'Linux PC';
  }

  return deviceName;
}
