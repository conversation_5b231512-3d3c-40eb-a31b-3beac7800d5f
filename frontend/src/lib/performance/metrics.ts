/**
 * Performance Metrics for AG-UI
 * 
 * This module provides utilities for monitoring and measuring performance metrics
 * for the AG-UI CopilotKit integration, including:
 * - First token latency measurement
 * - Response time tracking
 * - Performance data collection
 */

// Type definitions for performance metrics
export interface PerformanceMetrics {
  // Timing metrics in milliseconds
  firstTokenLatency?: number;  // Time to first token
  totalResponseTime?: number;  // Total time from request to completion
  tokenGenerationRate?: number; // Tokens per second
  
  // Request metadata
  requestSize?: number;        // Size of request in bytes/chars
  responseSize?: number;       // Size of response in bytes/chars
  tokenCount?: number;         // Total tokens in response
  
  // Context info
  agentId?: string;           // Which agent was used
  promptTemplate?: string;    // Which prompt template was used
  timestamp: number;          // When the request occurred
  
  // Cache metrics
  cacheHit: boolean;          // Whether response was served from cache
  cacheTier?: 'memory' | 'local' | 'remote' | null; // Source of cache hit
}

// Singleton class for tracking performance across the application
export class PerformanceMonitor {
  private static instance: PerformanceMonitor;
  private metrics: PerformanceMetrics[] = [];
  private activeRequests: Map<string, {
    startTime: number;
    firstTokenTime?: number;
    promptSize?: number;
  }> = new Map();
  private isServerSide: boolean;

  private constructor() {
    // Detect if we're running on the server
    this.isServerSide = typeof window === 'undefined';
  }

  public static getInstance(): PerformanceMonitor {
    if (!PerformanceMonitor.instance) {
      PerformanceMonitor.instance = new PerformanceMonitor();
    }
    return PerformanceMonitor.instance;
  }
  
  /**
   * Start tracking a new request
   * @param requestId Unique identifier for the request
   * @param promptSize Size of the prompt in characters
   * @returns The current timestamp
   */
  public startRequest(requestId: string, promptSize?: number): number {
    // Skip tracking on server-side during build
    if (this.isServerSide) {
      return Date.now();
    }

    const startTime = Date.now();
    this.activeRequests.set(requestId, {
      startTime,
      promptSize
    });
    return startTime;
  }
  
  /**
   * Mark when the first token is received
   * @param requestId Unique identifier for the request
   * @returns The calculated latency in ms, or undefined if request not found
   */
  public markFirstToken(requestId: string): number | undefined {
    // Skip tracking on server-side during build
    if (this.isServerSide) {
      return undefined;
    }

    const request = this.activeRequests.get(requestId);
    if (!request) return undefined;

    const now = Date.now();
    request.firstTokenTime = now;
    return now - request.startTime;
  }
  
  /**
   * Complete a request and record its metrics
   * @param requestId Unique identifier for the request
   * @param metrics Additional metrics to record
   * @returns The complete metrics for this request
   */
  public completeRequest(
    requestId: string,
    metrics: Partial<PerformanceMetrics>
  ): PerformanceMetrics | undefined {
    // Skip tracking on server-side during build
    if (this.isServerSide) {
      return undefined;
    }

    const request = this.activeRequests.get(requestId);
    if (!request) return undefined;

    const now = Date.now();
    const completeMetrics: PerformanceMetrics = {
      firstTokenLatency: request.firstTokenTime
        ? request.firstTokenTime - request.startTime
        : undefined,
      totalResponseTime: now - request.startTime,
      requestSize: request.promptSize,
      timestamp: now,
      cacheHit: metrics.cacheHit || false,
      ...metrics
    };

    // Calculate token generation rate if we have both count and time
    if (completeMetrics.tokenCount && completeMetrics.totalResponseTime) {
      completeMetrics.tokenGenerationRate =
        (completeMetrics.tokenCount * 1000) / completeMetrics.totalResponseTime;
    }

    this.metrics.push(completeMetrics);
    this.activeRequests.delete(requestId);

    // Log metrics to console when in development
    if (process.env.NODE_ENV === 'development') {
      console.log('Performance metrics:', completeMetrics);
    }

    return completeMetrics;
  }
  
  /**
   * Get all recorded metrics
   */
  public getAllMetrics(): PerformanceMetrics[] {
    // Return empty array on server-side during build
    if (this.isServerSide) {
      return [];
    }
    return [...this.metrics];
  }
  
  /**
   * Get average first token latency
   */
  public getAverageFirstTokenLatency(): number | null {
    // Return null on server-side during build
    if (this.isServerSide) {
      return null;
    }

    const validMetrics = this.metrics.filter(m =>
      typeof m.firstTokenLatency === 'number');

    if (validMetrics.length === 0) return null;

    const sum = validMetrics.reduce(
      (total, current) => total + (current.firstTokenLatency || 0),
      0
    );
    return sum / validMetrics.length;
  }
  
  /**
   * Clear recorded metrics
   */
  public clearMetrics(): void {
    this.metrics = [];
  }
}

// Export singleton instance - only create in browser environment
export const performanceMonitor = typeof window !== 'undefined'
  ? PerformanceMonitor.getInstance()
  : null as any;

/**
 * Hook to measure streaming performance in React components
 */
export function usePerformanceMeasurement() {
  const generateRequestId = () => `req_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;

  return {
    trackRequest: (promptSize?: number) => {
      const requestId = generateRequestId();
      performanceMonitor?.startRequest(requestId, promptSize);
      return requestId;
    },

    markFirstToken: (requestId: string) => {
      return performanceMonitor?.markFirstToken(requestId);
    },

    completeRequest: (requestId: string, metrics: Partial<PerformanceMetrics>) => {
      return performanceMonitor?.completeRequest(requestId, metrics);
    },

    getAverageFirstTokenLatency: () => {
      return performanceMonitor?.getAverageFirstTokenLatency();
    },

    getAllMetrics: () => {
      return performanceMonitor?.getAllMetrics() || [];
    }
  };
}

/**
 * Performance measurement middleware for API routes
 * @param handler The API route handler
 * @returns A wrapped handler that measures performance
 */
export function withPerformanceTracking(handler: Function) {
  return async (req: Request, ...args: any[]) => {
    const requestId = `api_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
    const startTime = Date.now();

    try {
      // Call the original handler
      const response = await handler(req, ...args);

      // Record metrics if this was a successful response and monitor is available
      if (response && response.status >= 200 && response.status < 300 && performanceMonitor) {
        performanceMonitor.completeRequest(requestId, {
          totalResponseTime: Date.now() - startTime,
          cacheHit: false // API handlers don't use cache by default
        });
      }

      return response;
    } catch (_error) {
      // Still record metrics on error for tracking purposes if monitor is available
      if (performanceMonitor) {
        performanceMonitor.completeRequest(requestId, {
          totalResponseTime: Date.now() - startTime,
          cacheHit: false
        });
      }
      throw error;
    }
  };
}
