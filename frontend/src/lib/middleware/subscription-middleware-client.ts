import { SupabaseClient } from '@supabase/supabase-js';
import { Database } from '../supabase/database.types';

/**
 * Interface for quota check response
 */
export interface QuotaCheckResult {
  within_quota: boolean;
  current_usage: number;
  quota_limit: number;
  percent_used: number;
}

/**
 * Interface for trial subscription
 */
export interface TrialSubscription {
  status: string;
  trial_end: string;
}

/**
 * Creates a client for middleware subscription operations
 * This handles tenant schema access in a type-safe way
 *
 * @param client The Supabase client
 * @returns An object with methods for subscription middleware
 */
export function createSubscriptionMiddlewareClient(client: SupabaseClient<Database>) {
  return {
    /**
     * Check if a tenant has access to a feature based on their subscription
     * @param tenantId The tenant ID
     * @param featureKey The feature key to check
     * @returns Whether the tenant has access to the feature
     */
    async checkTenantFeatureAccess(tenantId: string, featureKey: string): Promise<boolean> {
      const { data, error } = await (client as any).rpc('check_tenant_feature_access', {
        p_tenant_id: tenantId,
        p_feature_key: featureKey
      });

      if (error) {
        console.error('Error checking feature access:', error);
        return false;
      }

      return !!data;
    },

    /**
     * Check if a tenant has an active subscription
     * @param tenantId The tenant ID
     * @returns Whether the tenant has an active subscription
     */
    async checkActiveSubscription(tenantId: string): Promise<boolean> {
      try {
        const { data, error } = await (client as any)
          .schema('tenants')
          .from('tenant_subscriptions')
          .select('status')
          .eq('tenant_id', tenantId)
          .in('status', ['active', 'trialing'])
          .maybeSingle();

        if (error) {
          console.error('Error checking subscription status:', error);
          return false;
        }

        return !!data;
      } catch (_error) {
        console.error('Failed to check active subscription:', error);
        return false;
      }
    },

    /**
     * Check if a tenant has a trial subscription
     * @param tenantId The tenant ID
     * @returns The trial subscription details or null
     */
    async checkTrialSubscription(tenantId: string): Promise<TrialSubscription | null> {
      try {
        const { data, error } = await (client as any)
          .schema('tenants')
          .from('tenant_subscriptions')
          .select('status, trial_end')
          .eq('tenant_id', tenantId)
          .eq('status', 'trialing')
          .maybeSingle();

        if (error) {
          console.error('Error checking trial subscription:', error);
          return null;
        }

        return data as TrialSubscription;
      } catch (_error) {
        console.error('Failed to check trial subscription:', error);
        return null;
      }
    },

    /**
     * Check if a tenant has exceeded their quota for a specific resource
     * @param tenantId The tenant ID
     * @param usageType The type of resource to check quota for
     * @param incrementBy The amount to increment the usage by
     * @returns The quota check result
     */
    async checkTenantQuota(
      tenantId: string,
      usageType: string,
      incrementBy: number = 1
    ): Promise<QuotaCheckResult | null> {
      const { data, error } = await (client as any).rpc('check_tenant_quota', {
        p_tenant_id: tenantId,
        p_usage_type: usageType,
        p_increment_by: incrementBy
      });

      if (error) {
        console.error('Error checking quota:', error);
        return null;
      }

      return data as QuotaCheckResult;
    }
  };
}
