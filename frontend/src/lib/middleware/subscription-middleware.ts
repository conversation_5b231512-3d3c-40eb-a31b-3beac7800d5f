import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';
import { Database } from '../supabase/database.types';
import {
  createSubscriptionMiddlewareClient,
  QuotaCheckResult,
  TrialSubscription
} from './subscription-middleware-client';

// Create a Supabase client for the middleware
const supabase = createClient<Database>(
  process.env.NEXT_PUBLIC_SUPABASE_URL || '',
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || ''
);

// Create a subscription middleware client
const subscriptionClient = createSubscriptionMiddlewareClient(supabase);

// Cache for feature access to reduce database calls
const featureAccessCache = new Map<string, { access: boolean; timestamp: number }>();
const CACHE_TTL = 5 * 60 * 1000; // 5 minutes in milliseconds

/**
 * Middleware to check if a tenant has access to a feature based on their subscription
 * @param req The Next.js request object
 * @param res The Next.js response object
 * @param featureKey The feature key to check access for
 * @returns The Next.js response object
 */
export async function subscriptionMiddleware(
  req: NextRequest,
  res: NextResponse,
  featureKey: string
) {
  // Get user session
  const { data: { session } } = await supabase.auth.getSession();

  if (!session) {
    return NextResponse.redirect(new URL('/login', req.url));
  }

  // Get tenant ID from JWT claims
  const tenantId = session.user.app_metadata.tenant_id;

  if (!tenantId) {
    return NextResponse.json(
      { error: 'Tenant ID not found in user claims' },
      { status: 403 }
    );
  }

  // Check cache first
  const cacheKey = `${tenantId}:${featureKey}`;
  const cachedAccess = featureAccessCache.get(cacheKey);

  if (cachedAccess && Date.now() - cachedAccess.timestamp < CACHE_TTL) {
    if (!cachedAccess.access) {
      return NextResponse.json(
        { error: 'Subscription does not include access to this feature' },
        { status: 403 }
      );
    }
    return res;
  }

  // Check if tenant has access to the feature
  try {
    const hasAccess = await subscriptionClient.checkTenantFeatureAccess(tenantId, featureKey);

    // Update cache
    featureAccessCache.set(cacheKey, { access: hasAccess, timestamp: Date.now() });

    if (!hasAccess) {
      return NextResponse.json(
        { error: 'Subscription does not include access to this feature' },
        { status: 403 }
      );
    }
  } catch (_error) {
    console.error('Error checking feature access:', error);
    return NextResponse.json(
      { error: 'Error checking subscription status' },
      { status: 500 }
    );
  }

  return res;
}

/**
 * Middleware to check if a tenant's subscription is active
 * @param req The Next.js request object
 * @param res The Next.js response object
 * @returns The Next.js response object
 */
export async function activeSubscriptionMiddleware(
  req: NextRequest,
  res: NextResponse
) {
  // Get user session
  const { data: { session } } = await supabase.auth.getSession();

  if (!session) {
    return NextResponse.redirect(new URL('/login', req.url));
  }

  // Get tenant ID from JWT claims
  const tenantId = session.user.app_metadata.tenant_id;

  if (!tenantId) {
    return NextResponse.json(
      { error: 'Tenant ID not found in user claims' },
      { status: 403 }
    );
  }

  // Check if tenant has an active subscription
  try {
    const hasActiveSubscription = await subscriptionClient.checkActiveSubscription(tenantId);

    if (!hasActiveSubscription) {
      return NextResponse.redirect(new URL('/subscription/expired', req.url));
    }
  } catch (_error) {
    console.error('Error checking subscription status:', error);
    return NextResponse.json(
      { error: 'Error checking subscription status' },
      { status: 500 }
    );
  }

  return res;
}

/**
 * Middleware to check if a tenant's subscription is in trial mode
 * @param req The Next.js request object
 * @param res The Next.js response object
 * @returns The Next.js response object
 */
export async function trialSubscriptionMiddleware(
  req: NextRequest,
  res: NextResponse
) {
  // Get user session
  const { data: { session } } = await supabase.auth.getSession();

  if (!session) {
    return NextResponse.redirect(new URL('/login', req.url));
  }

  // Get tenant ID from JWT claims
  const tenantId = session.user.app_metadata.tenant_id;

  if (!tenantId) {
    return NextResponse.json(
      { error: 'Tenant ID not found in user claims' },
      { status: 403 }
    );
  }

  // Check if tenant has a trial subscription
  try {
    const subscription = await subscriptionClient.checkTrialSubscription(tenantId);

    if (!subscription) {
      return NextResponse.redirect(new URL('/subscription/upgrade', req.url));
    }

    // Check if trial is about to expire (less than 3 days)
    const trialEnd = new Date(subscription.trial_end);
    const now = new Date();
    const daysRemaining = Math.ceil((trialEnd.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));

    if (daysRemaining <= 3) {
      // Add trial expiration warning to the response headers
      const response = res.clone();
      response.headers.set('X-Trial-Expiring', 'true');
      response.headers.set('X-Trial-Days-Remaining', daysRemaining.toString());
      return response;
    }

    return res;
  } catch (_error) {
    console.error('Error checking trial subscription:', error);
    return NextResponse.json(
      { error: 'Error checking subscription status' },
      { status: 500 }
    );
  }
}

/**
 * Middleware to check if a tenant has exceeded their quota for a specific resource
 * @param req The Next.js request object
 * @param res The Next.js response object
 * @param usageType The type of resource to check quota for
 * @param incrementBy The amount to increment the usage by
 * @returns The Next.js response object
 */
export async function quotaMiddleware(
  req: NextRequest,
  res: NextResponse,
  usageType: string,
  incrementBy: number = 1
) {
  // Get user session
  const { data: { session } } = await supabase.auth.getSession();

  if (!session) {
    return NextResponse.redirect(new URL('/login', req.url));
  }

  // Get tenant ID from JWT claims
  const tenantId = session.user.app_metadata.tenant_id;

  if (!tenantId) {
    return NextResponse.json(
      { error: 'Tenant ID not found in user claims' },
      { status: 403 }
    );
  }

  // Check if tenant has exceeded their quota
  try {
    const quota = await subscriptionClient.checkTenantQuota(tenantId, usageType, incrementBy);

    if (!quota || !quota.within_quota) {
      return NextResponse.json(
        {
          error: 'Quota exceeded',
          details: {
            usageType,
            currentUsage: quota?.current_usage || 0,
            quotaLimit: quota?.quota_limit || 0,
            percentUsed: quota?.percent_used || 100
          }
        },
        { status: 403 }
      );
    }

    // Add quota information to the response headers
    const response = res.clone();
    response.headers.set('X-Quota-Current', quota.current_usage.toString());
    response.headers.set('X-Quota-Limit', quota.quota_limit.toString());
    response.headers.set('X-Quota-Percent', quota.percent_used.toString());

    return response;
  } catch (_error) {
    console.error('Error checking quota:', error);
    return NextResponse.json(
      { error: 'Error checking quota' },
      { status: 500 }
    );
  }
}
