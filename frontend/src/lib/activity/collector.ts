/**
 * Activity Collector
 *
 * Centralized system for collecting user activity events on the frontend.
 * Buffers activities and flushes them periodically or for critical events.
 */

import { AuthUser, ActivityContext } from '@/lib/types';
import { supabase } from '../supabase/client'; // Import supabase client

const ACTIVITY_BUFFER_LIMIT = 10; // Flush after 10 activities
const FLUSH_INTERVAL_MS = 60 * 1000; // Flush every 60 seconds
const CRITICAL_ACTIVITIES = ['CASE_CREATED', 'DOCUMENT_UPLOADED', 'MEETING_SCHEDULED']; // Example critical events

let activityBuffer: { context: ActivityContext, user: AuthUser, timestamp: string }[] = [];
let flushTimeoutId: NodeJS.Timeout | null = null;

/**
 * Adds an activity to the buffer.
 */
function addToBuffer(context: ActivityContext, user: AuthUser) {
  activityBuffer.push({ context, user, timestamp: new Date().toISOString() });
  console.log('[ActivityCollector] Buffered:', context.action, 'Buffer size:', activityBuffer.length);

  // Schedule a flush if not already scheduled
  if (!flushTimeoutId) {
    flushTimeoutId = setTimeout(flushActivityBuffer, FLUSH_INTERVAL_MS);
  }

  // Flush immediately if buffer limit reached
  if (activityBuffer.length >= ACTIVITY_BUFFER_LIMIT) {
    flushActivityBuffer();
  }
}

/**
 * Flushes the activity buffer to the backend.
 */
export async function flushActivityBuffer(): Promise<void> {
  if (flushTimeoutId) {
    clearTimeout(flushTimeoutId);
    flushTimeoutId = null;
  }

  if (activityBuffer.length === 0) {
    return;
  }

  const activitiesToFlush = [...activityBuffer];
  activityBuffer = []; // Clear buffer immediately

  try {
    // 1. Get token from the singleton client
    const { data: { session }, error: sessionError } = await supabase.auth.getSession();

    // 2. Prepare headers
    const headers: HeadersInit = {
      'Content-Type': 'application/json',
    };
    if (session?.access_token) {
      headers['Authorization'] = `Bearer ${session.access_token}`;
    } else {
      console.warn('[ActivityCollector] No access token found for flushing activities. Sending unauthenticated.');
      if (sessionError) {
         console.error('[ActivityCollector] Session error:', sessionError.message);
      }
    }

    // 3. Send activities to the API endpoint
    const response = await fetch('/api/activity/log', {
      method: 'POST',
      headers: headers, // Use prepared headers
      body: JSON.stringify(activitiesToFlush),
    });

    if (!response.ok) {
      // Log the error but don't re-add to buffer to avoid infinite loops on persistent API errors
      const errorData = await response.json();
      console.error('[ActivityCollector] Failed to flush activities via API:', response.status, errorData.message);
    } else {
      console.log(`[ActivityCollector] Successfully flushed ${activitiesToFlush.length} activities via API.`);
    }

  } catch (_error) {
    console.error('[ActivityCollector] Network or other error flushing activities via API:', error);
    // Optionally, handle retry logic or re-queueing failed activities here,
    // but be cautious about potential infinite loops.
    // For now, we just log the error.
  }
}

/**
 * Tracks a user activity event.
 * This should be called from various UI components.
 */
export const trackActivity = async (
  context: ActivityContext,
  user: AuthUser | null // User might not always be available immediately
): Promise<void> => {
  if (!user) {
    console.warn('[ActivityCollector] User not available, skipping activity tracking for:', context.action);
    return;
  }

  addToBuffer(context, user);

  // For critical activities, flush immediately
  if (CRITICAL_ACTIVITIES.includes(context.action)) {
    await flushActivityBuffer();
  }
};

// Ensure buffer is flushed when the user navigates away or closes the tab
if (typeof window !== 'undefined') {
  window.addEventListener('beforeunload', () => {
    // Use sendBeacon for reliable background sending if needed for batching
    // navigator.sendBeacon('/api/activity/log-batch', JSON.stringify(activityBuffer));
    // Or trigger a final synchronous flush (less reliable)
    if (activityBuffer.length > 0) {
      console.log('[ActivityCollector] Flushing buffer on page unload...');
      // Note: Async operations in 'beforeunload' are not guaranteed.
      // A dedicated batch endpoint using sendBeacon is more robust.
      flushActivityBuffer(); // Attempt best-effort flush
    }
  });
}
