/**
 * Laws-API Client
 * Handles all communication with the laws-API service
 */

import { useAuthenticatedFetch } from '@/hooks/useAuthenticatedFetch';
import { LAWS_API_BASE_URL } from '@/lib/config';
import {
  LawsApiConfig,
  LawsApiResponse,
  LawsApiException,
  SearchRequest,
  SearchResult,
  RecommendRequest,
  RecommendationResult,
  GraphRequest,
  GraphResult,
  RequestOptions,
  DEFAULT_LAWS_API_CONFIG
} from '@/types/laws-api';

export class LawsApiClient {
  private config: LawsApiConfig;
  private authedFetch: ReturnType<typeof useAuthenticatedFetch>['authedFetch'];

  constructor(
    config: Partial<LawsApiConfig> = {},
    authedFetch?: ReturnType<typeof useAuthenticatedFetch>['authedFetch']
  ) {
    this.config = {
      ...DEFAULT_LAWS_API_CONFIG,
      baseUrl: LAWS_API_BASE_URL,
      ...config
    };
    
    if (authedFetch) {
      this.authedFetch = authedFetch;
    } else {
      // Fallback for server-side usage
      this.authedFetch = this.createFallbackFetch();
    }
  }

  /**
   * Search for legal documents using v1 API
   */
  async search(
    request: SearchRequest,
    options?: RequestOptions
  ): Promise<LawsApiResponse<SearchResult[]>> {
    return this.makeRequest('/v1/search', {
      method: 'POST',
      body: JSON.stringify(request),
      ...options
    });
  }

  /**
   * Get recommendations based on document or content
   */
  async recommend(
    request: RecommendRequest,
    options?: RequestOptions
  ): Promise<LawsApiResponse<RecommendationResult[]>> {
    return this.makeRequest('/v0/recommend', {
      method: 'POST',
      body: JSON.stringify(request),
      ...options
    });
  }

  /**
   * Query the legal knowledge graph
   */
  async graph(
    request: GraphRequest,
    options?: RequestOptions
  ): Promise<LawsApiResponse<GraphResult>> {
    return this.makeRequest('/v0/graph', {
      method: 'POST',
      body: JSON.stringify(request),
      ...options
    });
  }

  /**
   * Make authenticated request to laws-API
   */
  private async makeRequest<T>(
    endpoint: string,
    options: RequestInit & RequestOptions = {}
  ): Promise<LawsApiResponse<T>> {
    const url = `${this.config.baseUrl}${endpoint}`;
    const { retryAttempts = this.config.retryAttempts, ...fetchOptions } = options;

    let lastError: Error | null = null;

    for (let attempt = 0; attempt <= (retryAttempts || 0); attempt++) {
      try {
        const response = await this.authedFetch<LawsApiResponse<T>>(url, {
          ...fetchOptions,
          headers: {
            'Content-Type': 'application/json',
            ...fetchOptions.headers,
          },
        });

        return response;
      } catch (_error) {
        lastError = error as Error;

        // Handle rate limiting
        if (this.isRateLimitError(error)) {
          const retryAfter = this.extractRetryAfter(error);
          if (retryAfter && attempt < (retryAttempts || 0)) {
            await this.delay(retryAfter * 1000);
            continue;
          }
        }

        // Handle temporary errors
        if (this.isRetryableError(error) && attempt < (retryAttempts || 0)) {
          const delay = this.calculateBackoffDelay(attempt);
          await this.delay(delay);
          continue;
        }

        // Non-retryable error or max attempts reached
        break;
      }
    }

    // Transform error to LawsApiException
    throw this.transformError(lastError);
  }

  /**
   * Check if error is rate limiting (429)
   */
  private isRateLimitError(error: any): boolean {
    return error?.status === 429 || error?.code === 'RATE_LIMIT_EXCEEDED';
  }

  /**
   * Extract retry-after header value
   */
  private extractRetryAfter(error: any): number | null {
    const retryAfter = error?.retryAfter || error?.headers?.['retry-after'];
    return retryAfter ? parseInt(retryAfter, 10) : null;
  }

  /**
   * Check if error is retryable (5xx, network errors)
   */
  private isRetryableError(error: any): boolean {
    const status = error?.status;
    return (
      !status || // Network error
      status >= 500 || // Server error
      status === 408 || // Request timeout
      status === 429 // Rate limit (handled separately)
    );
  }

  /**
   * Calculate exponential backoff delay
   */
  private calculateBackoffDelay(attempt: number): number {
    const baseDelay = this.config.retryDelay || 1000;
    return Math.min(baseDelay * Math.pow(2, attempt), 10000);
  }

  /**
   * Delay execution
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Transform generic error to LawsApiException
   */
  private transformError(error: any): LawsApiException {
    if (error instanceof LawsApiException) {
      return error;
    }

    const status = error?.status || 500;
    const code = error?.code || 'UNKNOWN_ERROR';
    const message = error?.message || 'An unknown error occurred';
    const retryAfter = this.extractRetryAfter(error);

    return new LawsApiException(message, code, status, retryAfter);
  }

  /**
   * Fallback fetch for server-side usage
   */
  private createFallbackFetch() {
    return async <T>(url: string, options: RequestInit = {}): Promise<T> => {
      const response = await fetch(url, options);
      
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new LawsApiException(
          errorData.message || response.statusText,
          errorData.code || 'HTTP_ERROR',
          response.status,
          response.headers.get('retry-after') ? 
            parseInt(response.headers.get('retry-after')!, 10) : undefined
        );
      }

      return response.json();
    };
  }
}

/**
 * React hook for laws-API client
 */
export function useLawsApiClient(config?: Partial<LawsApiConfig>): LawsApiClient {
  const { authedFetch } = useAuthenticatedFetch();
  
  return new LawsApiClient(config, authedFetch);
}

/**
 * Server-side laws-API client factory
 */
export function createLawsApiClient(
  config?: Partial<LawsApiConfig>,
  authToken?: string
): LawsApiClient {
  const clientConfig = { ...DEFAULT_LAWS_API_CONFIG, ...config };
  
  // Create authenticated fetch function for server-side
  const authedFetch = async <T>(url: string, options: RequestInit = {}): Promise<T> => {
    const headers = {
      'Content-Type': 'application/json',
      ...(authToken && { 'Authorization': `Bearer ${authToken}` }),
      ...options.headers,
    };

    const response = await fetch(url, {
      ...options,
      headers,
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new LawsApiException(
        errorData.message || response.statusText,
        errorData.code || 'HTTP_ERROR',
        response.status,
        response.headers.get('retry-after') ? 
          parseInt(response.headers.get('retry-after')!, 10) : undefined
      );
    }

    return response.json();
  };

  return new LawsApiClient(clientConfig, authedFetch);
}
