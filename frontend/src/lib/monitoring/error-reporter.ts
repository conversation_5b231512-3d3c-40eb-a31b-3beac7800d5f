/**
 * Error Reporting Service for AG-UI Integration
 * 
 * This module provides centralized error reporting functionality
 * with support for Supabase logging and external monitoring services.
 */

import { createClient } from '@supabase/supabase-js';
import { AGError as IAGError } from '@/lib/error-handling/error-types';
import { AGError } from '@/lib/error-handling/error-classes';

// Configuration interface for the reporter
export interface ErrorReporterConfig {
  // Whether error reporting is enabled
  enabled: boolean;
  
  // Supabase config for error logging
  supabase?: {
    url: string;
    key: string;
    table: string;
  };
  
  // External error monitoring service
  externalService?: {
    enabled: boolean;
    endpoint?: string;
    apiKey?: string;
    projectId?: string;
  };
  
  // Sampling rate (0.0-1.0) for error reporting
  samplingRate?: number;
  
  // Custom metadata to include with all reports
  metadata?: Record<string, any>;
  
  // Whether to include environment info
  includeEnvironmentInfo?: boolean;
  
  // Whether to log to console in development
  consoleReporting?: boolean;
}

// Error reporter singleton
export class ErrorReporter {
  private static instance: ErrorReporter;
  private config: ErrorReporterConfig;
  private supabaseClient?: ReturnType<typeof createClient>;
  private sessionId: string;
  private userIdentifier?: string;
  private metadata: Record<string, any> = {};
  
  private constructor(config: ErrorReporterConfig) {
    this.config = {
      ...config,
      samplingRate: config.samplingRate !== undefined ? config.samplingRate : 1.0,
      includeEnvironmentInfo: config.includeEnvironmentInfo !== undefined ? config.includeEnvironmentInfo : true,
      consoleReporting: config.consoleReporting !== undefined ? config.consoleReporting : process.env.NODE_ENV === 'development'
    };
    
    // Initialize Supabase client if configured
    if (this.config.supabase) {
      try {
        this.supabaseClient = createClient(
          this.config.supabase.url,
          this.config.supabase.key
        );
      } catch (_error) {
        console.error('Failed to initialize Supabase client for error reporting:', error);
      }
    }
    
    // Generate a session ID for grouping errors
    this.sessionId = this.generateSessionId();
    
    // Set initial metadata
    if (config.metadata) {
      this.metadata = { ...config.metadata };
    }
  }
  
  /**
   * Get the singleton instance of the error reporter
   */
  public static getInstance(config?: ErrorReporterConfig): ErrorReporter {
    if (!ErrorReporter.instance) {
      if (!config) {
        config = { 
          enabled: false,
          consoleReporting: process.env.NODE_ENV === 'development' 
        };
      }
      ErrorReporter.instance = new ErrorReporter(config);
    } else if (config) {
      // Update config if provided
      ErrorReporter.instance.updateConfig(config);
    }
    
    return ErrorReporter.instance;
  }
  
  /**
   * Update the configuration of the error reporter
   */
  public updateConfig(config: Partial<ErrorReporterConfig>): void {
    this.config = {
      ...this.config,
      ...config,
    };
    
    // Re-initialize Supabase if URL or key changed
    if (config.supabase && (config.supabase.url || config.supabase.key)) {
      try {
        this.supabaseClient = createClient(
          config.supabase.url || (this.config.supabase?.url || ''),
          config.supabase.key || (this.config.supabase?.key || '')
        );
      } catch (_error) {
        console.error('Failed to update Supabase client for error reporting:', error);
      }
    }
    
    // Update metadata if provided
    if (config.metadata) {
      this.metadata = {
        ...this.metadata,
        ...config.metadata
      };
    }
  }
  
  /**
   * Set the user identifier for error reporting
   */
  public setUser(identifier: string): void {
    this.userIdentifier = identifier;
  }
  
  /**
   * Clear the user identifier
   */
  public clearUser(): void {
    this.userIdentifier = undefined;
  }
  
  /**
   * Add custom metadata to be included with error reports
   */
  public addMetadata(key: string, value: any): void {
    this.metadata[key] = value;
  }
  
  /**
   * Report an error to configured monitoring systems
   */
  public async reportError(error: Error | AGError, context?: Record<string, any>): Promise<void> {
    // Skip if reporting is disabled
    if (!this.config.enabled) {
      return;
    }
    
    // Apply sampling rate
    if (Math.random() > (this.config.samplingRate || 1.0)) {
      return;
    }
    
    try {
      // Normalize the error
      const normalizedError = this.normalizeError(error);
      
      // Add context from parameters
      if (context) {
        normalizedError.context = {
          ...normalizedError.context,
          ...context
        };
      }
      
      // Add environment info if configured
      if (this.config.includeEnvironmentInfo) {
        normalizedError.environment = this.getEnvironmentInfo();
      }
      
      // Add metadata
      normalizedError.metadata = {
        ...this.metadata,
        sessionId: this.sessionId,
        ...(this.userIdentifier ? { userId: this.userIdentifier } : {})
      };
      
      // Report to console if in development
      if (this.config.consoleReporting) {
        this.reportToConsole(normalizedError);
      }
      
      // Report to Supabase if configured
      if (this.supabaseClient && this.config.supabase?.table) {
        await this.reportToSupabase(normalizedError);
      }
      
      // Report to external service if configured
      if (this.config.externalService?.enabled) {
        await this.reportToExternalService(normalizedError);
      }
    } catch (reportingError) {
      // Log error but don't throw to avoid cascading failures
      if (process.env.NODE_ENV === 'development') {
        console.error('Error during error reporting:', reportingError);
      }
    }
  }
  
  /**
   * Normalize error to a standard format
   */
  private normalizeError(error: Error | AGError): any {
    // If it's already an AGError, use its properties
    if (this.isAGError(error)) {
      return {
        message: error.message,
        type: error.type,
        severity: error.severity,
        timestamp: error.timestamp || Date.now(),
        context: error.context || {},
        stack: (error.originalError instanceof Error) ? error.originalError.stack : error.stack
      };
    }
    
    // Otherwise normalize a standard Error
    if (error instanceof Error) {
      return {
        message: error.message,
        name: error.name,
        stack: error.stack,
        timestamp: Date.now(),
        context: {}
      };
    }
    
    // Handle non-Error objects
    return {
      message: String(error),
      timestamp: Date.now(),
      context: {},
      type: 'unknown'
    };
  }
  
  /**
   * Check if an object is an AGError
   */
  private isAGError(obj: any): obj is AGError {
    return obj instanceof AGError;
  }
  
  /**
   * Report error to console
   */
  private reportToConsole(error: any): void {
    console.group('Error Report');
    console.error('Error:', error.message);
    console.info('Type:', error.type || error.name);
    console.info('Timestamp:', new Date(error.timestamp).toISOString());
    if (error.context && Object.keys(error.context).length > 0) {
      console.info('Context:', error.context);
    }
    if (error.stack) {
      console.info('Stack:', error.stack);
    }
    console.groupEnd();
  }
  
  /**
   * Report error to Supabase
   */
  private async reportToSupabase(error: any): Promise<void> {
    if (!this.supabaseClient || !this.config.supabase?.table) {
      return;
    }
    
    try {
      const { error: insertError } = await this.supabaseClient
        .from(this.config.supabase.table)
        .insert({
          message: error.message,
          error_type: error.type || error.name,
          severity: error.severity,
          context: error.context,
          metadata: error.metadata,
          stack_trace: error.stack,
          environment: error.environment,
          timestamp: new Date(error.timestamp).toISOString(),
          created_at: new Date().toISOString()
        });
        
      if (insertError && process.env.NODE_ENV === 'development') {
        console.error('Failed to log error to Supabase:', insertError);
      }
    } catch (_e) {
      if (process.env.NODE_ENV === 'development') {
        console.error('Exception logging error to Supabase:', e);
      }
    }
  }
  
  /**
   * Report error to external service (like Sentry, LogRocket, etc.)
   */
  private async reportToExternalService(error: any): Promise<void> {
    if (!this.config.externalService?.endpoint) {
      return;
    }
    
    try {
      // Simple implementation using fetch - this would be replaced
      // with specific SDK for your chosen error monitoring service
      const response = await fetch(this.config.externalService.endpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          ...(this.config.externalService.apiKey 
            ? { 'Authorization': `Bearer ${this.config.externalService.apiKey}` } 
            : {})
        },
        body: JSON.stringify({
          ...error,
          project_id: this.config.externalService.projectId
        }),
        // Don't block main thread
        keepalive: true
      });
      
      if (!response.ok && process.env.NODE_ENV === 'development') {
        console.error('Failed to report error to external service:', 
          await response.text().catch(() => 'Unknown error'));
      }
    } catch (_e) {
      if (process.env.NODE_ENV === 'development') {
        console.error('Exception reporting error to external service:', e);
      }
    }
  }
  
  /**
   * Get information about the current environment
   */
  private getEnvironmentInfo(): Record<string, any> {
    return {
      userAgent: typeof navigator !== 'undefined' ? navigator.userAgent : undefined,
      url: typeof window !== 'undefined' ? window.location.href : undefined,
      referrer: typeof document !== 'undefined' ? document.referrer : undefined,
      screenSize: typeof window !== 'undefined' 
        ? `${window.screen.width}x${window.screen.height}` 
        : undefined,
      viewportSize: typeof window !== 'undefined' 
        ? `${window.innerWidth}x${window.innerHeight}` 
        : undefined,
      timestamp: new Date().toISOString(),
      environment: process.env.NODE_ENV || 'development',
      version: process.env.NEXT_PUBLIC_APP_VERSION || 'unknown'
    };
  }
  
  /**
   * Generate a unique session ID
   */
  private generateSessionId(): string {
    const timestamp = Date.now().toString(36);
    const randomChars = Math.random().toString(36).substring(2, 8);
    return `session_${timestamp}_${randomChars}`;
  }
}

// Create a hook for using the error reporter in React components
export function useErrorReporter() {
  const reporter = ErrorReporter.getInstance();
  
  return {
    reportError: (error: Error | AGError, context?: Record<string, any>) => 
      reporter.reportError(error, context),
    
    setUser: (userId: string) => reporter.setUser(userId),
    
    clearUser: () => reporter.clearUser(),
    
    addMetadata: (key: string, value: any) => reporter.addMetadata(key, value)
  };
}
