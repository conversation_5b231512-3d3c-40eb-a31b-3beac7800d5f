/**
 * Insight Repository
 *
 * Handles data access operations for the AiLex Insight Engine.
 * This repository follows a clean architecture approach and uses
 * the insight-mapper to convert between database models and domain models.
 */

import { createClient, SupabaseClient } from '@supabase/supabase-js';
// We're using a more flexible typing approach to handle schema mismatches
import {
  Insight,
  InsightCategory,
  InsightStatus,
  SuggestedAction,
  CreateInsightParams,
  UpdateInsightParams,
  InsightTemplate,
  InsightFeedback,
  InsightFilterParams,
  InsightListResponse
} from '../types/domain/insights';
import { mapInsightRowToInsight, mapInsightToInsertRow, mapInsightToUpdateRow } from './insight-mapper';

// Initialize Supabase client
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!;
// Use non-generic createClient to avoid type issues with custom tables
const supabase = createClient(supabaseUrl, supabaseKey);

export class InsightRepository {
  private db: SupabaseClient;

  constructor(db: SupabaseClient = supabase) {
    this.db = db;
  }

  /**
   * Get insights with optional filtering parameters
   * @param tenantId The tenant ID
   * @param filterParams Optional filter parameters
   */
  async getInsights(
    tenantId: string,
    filterParams?: InsightFilterParams
  ): Promise<InsightListResponse> {
    try {
      const {
        status,
        category,
        matterId,
        priority,
        limit = 20,
        page = 1
      } = filterParams || {};

      // Build query
      let query = this.db
        .from('insights')
        .select('*', { count: 'exact' })
        .eq('tenant_id', tenantId);

      // Apply filters
      if (status) {
        query = query.eq('status', status);
      }

      if (category) {
        query = query.eq('category', category);
      }

      if (matterId) {
        query = query.eq('matter_id', matterId);
      }

      if (priority) {
        query = query.eq('priority', String(priority));
      }

      // Add pagination
      const from = (page - 1) * limit;
      const to = from + limit - 1;

      query = query
        .order('created_at', { ascending: false })
        .range(from, to);

      // Execute query
      const { data, error, count } = await query;

      if (error) {
        console.error('Error fetching insights:', error.message);
        return {
          insights: [],
          totalCount: 0,
          currentPage: page,
          totalPages: 0
        };
      }

      // Map database rows to domain objects
      const insights = (data || []).map(row => mapInsightRowToInsight(row));
      const totalCount = count || 0;
      const totalPages = Math.ceil(totalCount / limit);

      return {
        insights,
        totalCount,
        currentPage: page,
        totalPages
      };
    } catch (_error) {
      console.error('Error in getInsights:', error);
      return {
        insights: [],
        totalCount: 0,
        currentPage: 1,
        totalPages: 0
      };
    }
  }

  /**
   * Get insights related to a specific matter
   */
  async getInsightsForMatter(
    matterId: string,
    tenantId: string,
    limit = 20
  ): Promise<Insight[]> {
    try {
      const { data, error } = await this.db
        .from('insights')
        .select('*')
        .eq('tenant_id', tenantId)
        .eq('matter_id', matterId)
        .order('created_at', { ascending: false })
        .limit(limit);

      if (error) {
        console.error('Error fetching matter insights:', error.message);
        return [];
      }

      return (data || []).map(row => mapInsightRowToInsight(row));
    } catch (_error) {
      console.error('Error in getInsightsForMatter:', error);
      return [];
    }
  }

  /**
   * Get an insight by ID
   */
  async getInsightById(
    insightId: string,
    tenantId: string
  ): Promise<Insight | null> {
    try {
      const { data, error } = await this.db
        .from('insights')
        .select('*')
        .eq('tenant_id', tenantId)
        .eq('id', insightId)
        .single();

      if (error || !data) {
        console.error('Error fetching insight by ID:', error?.message);
        return null;
      }

      return mapInsightRowToInsight(data);
    } catch (_error) {
      console.error('Error in getInsightById:', error);
      return null;
    }
  }

  /**
   * Create a new insight
   */
  async createInsight(
    insightData: CreateInsightParams
  ): Promise<Insight | null> {
    try {
      // Map domain object to database row
      const insertData = mapInsightToInsertRow(insightData);

      // Insert into database
      const { data, error } = await this.db
        .from('insights')
        .insert(insertData)
        .select()
        .single();

      if (error || !data) {
        console.error('Error creating insight:', error?.message);
        return null;
      }

      // Map database row back to domain object
      return mapInsightRowToInsight(data);
    } catch (_error) {
      console.error('Error in createInsight:', error);
      return null;
    }
  }

  /**
   * Update an existing insight
   */
  async updateInsight(
    insightData: UpdateInsightParams
  ): Promise<Insight | null> {
    try {
      // Map domain object to database update
      const updateData = mapInsightToUpdateRow(insightData);

      // Update database
      const { data, error } = await this.db
        .from('insights')
        .update(updateData)
        .eq('id', insightData.id)
        .eq('tenant_id', insightData.tenantId)
        .select()
        .single();

      if (error || !data) {
        console.error('Error updating insight:', error?.message);
        return null;
      }

      // Map database row back to domain object
      return mapInsightRowToInsight(data);
    } catch (_error) {
      console.error('Error in updateInsight:', error);
      return null;
    }
  }

  /**
   * Update the status of an insight
   */
  async updateInsightStatus(
    insightId: string,
    tenantId: string,
    status: InsightStatus
  ): Promise<boolean> {
    try {
      const { error } = await this.db
        .from('insights')
        .update({ status })
        .eq('id', insightId)
        .eq('tenant_id', tenantId);

      return !error;
    } catch (_error) {
      console.error('Error in updateInsightStatus:', error);
      return false;
    }
  }

  /**
   * Delete an insight by ID
   */
  async deleteInsight(
    insightId: string,
    tenantId: string
  ): Promise<boolean> {
    try {
      const { error } = await this.db
        .from('insights')
        .delete()
        .eq('id', insightId)
        .eq('tenant_id', tenantId);

      return !error;
    } catch (_error) {
      console.error('Error in deleteInsight:', error);
      return false;
    }
  }

  /**
   * Add feedback for an insight
   */
  async addInsightFeedback(
    feedback: Omit<InsightFeedback, 'id' | 'createdAt'>
  ): Promise<InsightFeedback | null> {
    try {
      const { data, error } = await this.db
        .from('insight_feedback')
        .insert({
          insight_id: feedback.insightId,
          user_id: feedback.userId,
          is_helpful: feedback.isHelpful,
          feedback_text: feedback.feedbackText,
          tenant_id: feedback.tenantId
        })
        .select()
        .single();

      if (error || !data) {
        console.error('Error adding insight feedback:', error?.message);
        return null;
      }

      return {
        id: data.id,
        insightId: data.insight_id,
        userId: data.user_id,
        isHelpful: data.is_helpful,
        feedbackText: data.feedback_text || undefined,
        createdAt: data.created_at,
        tenantId: data.tenant_id
      };
    } catch (_error) {
      console.error('Error in addInsightFeedback:', error);
      return null;
    }
  }

  /**
   * Get templates for a tenant
   */
  async getInsightTemplates(tenantId: string): Promise<InsightTemplate[]> {
    try {
      const { data, error } = await this.db
        .from('insight_templates')
        .select('*')
        .eq('tenant_id', tenantId)
        .eq('enabled', true);

      if (error) {
        console.error('Error fetching insight templates:', error.message);
        return [];
      }

      return (data || []).map(row => ({
        id: row.id,
        name: row.name,
        category: row.category as InsightCategory,
        prompt: row.prompt,
        scoringLogic: row.scoring_logic,
        enabled: row.enabled,
        tenantId: row.tenant_id
      }));
    } catch (_error) {
      console.error('Error in getInsightTemplates:', error);
      return [];
    }
  }

  /**
   * Create a new insight template
   */
  async createInsightTemplate(
    template: Omit<InsightTemplate, 'id'>
  ): Promise<InsightTemplate | null> {
    try {
      const { data, error } = await this.db
        .from('insight_templates')
        .insert({
          name: template.name,
          category: template.category,
          prompt: template.prompt,
          scoring_logic: template.scoringLogic,
          enabled: template.enabled,
          tenant_id: template.tenantId
        })
        .select()
        .single();

      if (error || !data) {
        console.error('Error creating insight template:', error?.message);
        return null;
      }

      return {
        id: data.id,
        name: data.name,
        category: data.category as InsightCategory,
        prompt: data.prompt,
        scoringLogic: data.scoring_logic,
        enabled: data.enabled,
        tenantId: data.tenant_id
      };
    } catch (_error) {
      console.error('Error in createInsightTemplate:', error);
      return null;
    }
  }
}

// Export singleton instance
export const insightRepository = new InsightRepository();
