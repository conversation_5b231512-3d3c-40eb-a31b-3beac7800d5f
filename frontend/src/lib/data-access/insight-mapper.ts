/**
 * Insights Data Mapper
 *
 * Maps between database insight records, AI-generated insights, and domain insight objects.
 * Following the AiLex Insight Engine PRD requirements.
 *
 * Insights come from multiple sources:
 * 1. Supabase "insights" table - stores persisted insights
 * 2. Neo4j activity data - source for dynamically generated insights
 * 3. LangGraph/OpenAI - source for AI-generated insights
 */

import { z } from 'zod';
import { Database } from '../supabase/database.types';
import {
  Insight,
  InsightCategory,
  InsightStatus,
  SuggestedAction,
  CreateInsightParams,
  UpdateInsightParams
} from '../types/domain/insights';

/**
 * Type definitions for AI-generated insights from external sources
 */
export interface AIGeneratedInsight {
  id: string;
  message: string;
  suggestions: string[];
  priority: number; // 1-10 scale
  relatedEntity?: {
    type: 'matter' | 'document' | 'task';
    id?: string;
    name?: string | null;
  };
  timestamp: string;
  groupKey?: string;
  relatedActivities?: string[];
  source?: string;
}

/**
 * Maps numeric priorities between different scales
 * - Domain priority: 1 (low) to 3 (high)
 * - AI priority: 1 (lowest) to 10 (highest)
 */
export function mapAIPriorityToDomainPriority(aiPriority: number): 1 | 2 | 3 {
  if (aiPriority >= 8) return 3; // High
  if (aiPriority >= 4) return 2; // Medium
  return 1; // Low
}

/**
 * Zod schemas for validation
 */
export const SuggestedActionSchema = z.object({
  type: z.enum(['task', 'draft', 'email', 'call', 'review', 'custom']),
  payload: z.record(z.unknown())
});

export const InsightSchema = z.object({
  id: z.string().uuid(),
  userId: z.string(),
  matterId: z.string().optional(),
  title: z.string(),
  description: z.string().optional(),
  category: z.nativeEnum(InsightCategory),
  priority: z.union([z.literal(1), z.literal(2), z.literal(3)]),
  confidence: z.number().min(0).max(1),
  suggestedAction: SuggestedActionSchema.optional(),
  source: z.string(),
  status: z.nativeEnum(InsightStatus),
  createdAt: z.string(),
  expiresAt: z.string().optional(),
  tenantId: z.string(),
  relatedMatter: z.object({
    id: z.string(),
    title: z.string()
  }).optional()
});

export const CreateInsightSchema = z.object({
  userId: z.string(),
  matterId: z.string().optional(),
  title: z.string(),
  description: z.string().optional(),
  category: z.nativeEnum(InsightCategory),
  priority: z.union([z.literal(1), z.literal(2), z.literal(3)]),
  confidence: z.number().min(0).max(1),
  suggestedAction: SuggestedActionSchema.optional(),
  source: z.string(),
  tenantId: z.string()
});

export const UpdateInsightSchema = z.object({
  id: z.string().uuid(),
  tenantId: z.string(),
  title: z.string().optional(),
  description: z.string().optional(),
  category: z.nativeEnum(InsightCategory).optional(),
  priority: z.union([z.literal(1), z.literal(2), z.literal(3)]).optional(),
  confidence: z.number().min(0).max(1).optional(),
  suggestedAction: SuggestedActionSchema.optional(),
  status: z.nativeEnum(InsightStatus).optional(),
  expiresAt: z.string().optional()
});

/**
 * Maps a database insight row to a domain insight object
 */
export function mapInsightRowToInsight(row: Database['tenants']['Tables']['insights']['Row'], matterTitle?: string): Insight {
  return {
    id: row.id,
    tenantId: row.tenant_id,
    userId: row.user_id || '',
    matterId: row.matter_id || undefined,
    title: row.title,
    description: row.description || undefined,
    category: row.category as InsightCategory,
    priority: Number(row.priority) as 1 | 2 | 3,
    confidence: row.confidence || 0,
    suggestedAction: row.suggested_action ?
      (typeof row.suggested_action === 'string' ?
        JSON.parse(row.suggested_action) : row.suggested_action) as SuggestedAction :
      undefined,
    source: row.source || '',
    status: row.status as InsightStatus,
    createdAt: row.created_at || new Date().toISOString(),
    expiresAt: row.expires_at || undefined,

    // Optional related entities
    relatedMatter: row.matter_id && matterTitle ? {
      id: row.matter_id,
      title: matterTitle
    } : undefined
  };
}

/**
 * Maps a domain insight creation object to a database insight row for insertion
 */
export function mapInsightToInsertRow(
  insight: CreateInsightParams
): Database['tenants']['Tables']['insights']['Insert'] {
  return {
    id: undefined, // Let the database generate the ID
    tenant_id: insight.tenantId,
    user_id: insight.userId,
    matter_id: insight.matterId,
    title: insight.title,
    description: insight.description,
    category: insight.category,
    priority: insight.priority,
    confidence: insight.confidence,
    suggested_action: insight.suggestedAction ? JSON.stringify(insight.suggestedAction) : null,
    source: insight.source,
    status: InsightStatus.NEW,
    created_at: new Date().toISOString(),
    expires_at: undefined
  };
}

/**
 * Maps a domain insight update object to a database insight row for update
 */
export function mapInsightToUpdateRow(
  insight: UpdateInsightParams
): Database['tenants']['Tables']['insights']['Update'] {
  const update: Database['tenants']['Tables']['insights']['Update'] = {};

  if (insight.title !== undefined) update.title = insight.title;
  if (insight.description !== undefined) update.description = insight.description;
  if (insight.category !== undefined) update.category = insight.category;
  if (insight.priority !== undefined) update.priority = insight.priority;
  if (insight.confidence !== undefined) update.confidence = insight.confidence;
  if (insight.suggestedAction !== undefined) {
    update.suggested_action = insight.suggestedAction ?
      JSON.stringify(insight.suggestedAction) : null;
  }
  if (insight.status !== undefined) update.status = insight.status;
  if (insight.expiresAt !== undefined) update.expires_at = insight.expiresAt;

  return update;
}

/**
 * Maps an AI-generated insight to a domain insight object
 */
export function mapAIInsightToInsight(aiInsight: AIGeneratedInsight, tenantId: string, userId: string): Insight {
  // Create a suggested action from AI suggestions if available
  const suggestedAction: SuggestedAction | undefined = aiInsight.suggestions && aiInsight.suggestions.length > 0
    ? {
        type: 'custom',
        payload: {
          suggestions: aiInsight.suggestions
        }
      }
    : undefined;

  // Determine the insight category based on related entity type
  let category = InsightCategory.TASK; // Default
  if (aiInsight.relatedEntity) {
    if (aiInsight.relatedEntity.type === 'document') {
      category = InsightCategory.EFFICIENCY;
    } else if (aiInsight.relatedEntity.type === 'matter') {
      category = InsightCategory.RISK;
    }
  }

  // Create the insight object
  return {
    id: aiInsight.id,
    tenantId,
    userId,
    matterId: aiInsight.relatedEntity?.type === 'matter' ? aiInsight.relatedEntity.id : undefined,
    title: aiInsight.message.slice(0, 100), // Truncate to reasonable title length
    description: aiInsight.message,
    category,
    priority: mapAIPriorityToDomainPriority(aiInsight.priority),
    confidence: aiInsight.priority / 10, // Convert 1-10 scale to 0-1 confidence
    suggestedAction,
    source: aiInsight.source || 'ai',
    status: InsightStatus.NEW,
    createdAt: aiInsight.timestamp || new Date().toISOString(),
    relatedMatter: aiInsight.relatedEntity?.type === 'matter' && aiInsight.relatedEntity.id && aiInsight.relatedEntity.name
      ? {
          id: aiInsight.relatedEntity.id,
          title: aiInsight.relatedEntity.name
        }
      : undefined
  };
}

/**
 * Maps multiple AI-generated insights to domain insight objects
 */
export function mapAIInsightsToDomainInsights(
  aiInsights: AIGeneratedInsight[],
  tenantId: string,
  userId: string
): Insight[] {
  return aiInsights.map(aiInsight => mapAIInsightToInsight(aiInsight, tenantId, userId));
}

/**
 * Validation utilities for insight data
 */
export const InsightValidation = {
  /**
   * Validate a new insight creation payload
   */
  validateCreateInsight(data: unknown): CreateInsightParams | null {
    try {
      const result = CreateInsightSchema.parse(data);
      return result as CreateInsightParams;
    } catch (_error) {
      console.error('Invalid insight creation data:', error);
      return null;
    }
  },

  /**
   * Validate an insight update payload
   */
  validateUpdateInsight(data: unknown): UpdateInsightParams | null {
    try {
      const result = UpdateInsightSchema.parse(data);
      return result as UpdateInsightParams;
    } catch (_error) {
      console.error('Invalid insight update data:', error);
      return null;
    }
  },

  /**
   * Check if an insight is high priority
   */
  isHighPriority(insight: Insight): boolean {
    return insight.priority === 3;
  },

  /**
   * Check if an insight is related to a specific matter
   */
  isRelatedToMatter(insight: Insight, matterId: string): boolean {
    return insight.matterId === matterId ||
      (insight.relatedMatter?.id === matterId);
  },

  /**
   * Check if an insight is recent (within specified days)
   */
  isRecent(insight: Insight, days: number = 7): boolean {
    const insightDate = new Date(insight.createdAt);
    const compareDate = new Date();
    compareDate.setDate(compareDate.getDate() - days);
    return insightDate >= compareDate;
  }
};
