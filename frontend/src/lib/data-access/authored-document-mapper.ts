/**
 * Authored Document Data Mapper
 *
 * Maps between database authored document records and domain authored document objects.
 * <PERSON><PERSON> authored documents from the tenants.authored_documents table.
 *
 * Authored documents are documents drafted by lawyers/users, as opposed to case documents
 * which are typically documents uploaded to the system.
 */

import { z } from 'zod';
import { createClient } from '@supabase/supabase-js';
import { Database } from '../supabase/database.types';

// Initialize Supabase client
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!;
const supabase = createClient<Database>(supabaseUrl, supabaseKey);

// Document status enum
export enum AuthoredDocumentStatus {
  DRAFT = 'DRAFT',
  READY = 'READY',
  SENT = 'SENT',
  SIGNED = 'SIGNED',
  ARCHIVED = 'ARCHIVED'
}

// Embedding status enum
export enum EmbeddingStatus {
  PENDING = 'PENDING',
  PROCESSING = 'PROCESSING',
  COMPLETED = 'COMPLETED',
  FAILED = 'FAILED'
}

// Domain Authored Document Model
export interface AuthoredDocument {
  id: string;
  tenantId: string;
  title: string;
  content: string;
  caseId: string | null;
  clientId: string | null;
  templateId: string | null;
  status: AuthoredDocumentStatus;
  embeddingStatus: EmbeddingStatus | null;
  gcsPath: string | null;
  sentAt: string | null;
  signedAt: string | null;
  lastEmbeddedAt: string | null;
  version: number | null;
  variablesUsed: Record<string, unknown> | null;
  metadata: Record<string, unknown> | null;
  createdAt: string | null;
  createdBy: string | null;
  updatedAt: string | null;
  updatedBy: string | null;
  // Relations
  case?: {
    id: string;
    title: string;
  } | null;
  client?: {
    id: string;
    fullName: string;
  } | null;
  template?: {
    id: string;
    title: string;
  } | null;
}

// Zod schemas for validation
export const AuthoredDocumentSchema = z.object({
  id: z.string().uuid(),
  tenantId: z.string().uuid(),
  title: z.string().min(1, "Title is required"),
  content: z.string(),
  caseId: z.string().uuid().nullable(),
  clientId: z.string().uuid().nullable(),
  templateId: z.string().uuid().nullable(),
  status: z.enum(['DRAFT', 'READY', 'SENT', 'SIGNED', 'ARCHIVED']),
  embeddingStatus: z.enum(['PENDING', 'PROCESSING', 'COMPLETED', 'FAILED']).nullable(),
  gcsPath: z.string().nullable(),
  sentAt: z.string().nullable(),
  signedAt: z.string().nullable(),
  lastEmbeddedAt: z.string().nullable(),
  version: z.number().nullable(),
  variablesUsed: z.record(z.unknown()).nullable(),
  metadata: z.record(z.unknown()).nullable(),
  createdAt: z.string().nullable(),
  createdBy: z.string().uuid().nullable(),
  updatedAt: z.string().nullable(),
  updatedBy: z.string().uuid().nullable(),
});

export const CreateAuthoredDocumentSchema = AuthoredDocumentSchema.omit({
  id: true,
  createdAt: true,
  createdBy: true,
  updatedAt: true,
  updatedBy: true,
  lastEmbeddedAt: true,
  embeddingStatus: true
}).extend({
  content: z.string().min(1, "Content is required"),
  status: z.enum(['DRAFT', 'READY', 'SENT', 'SIGNED', 'ARCHIVED']).default('DRAFT')
});

export const UpdateAuthoredDocumentSchema = AuthoredDocumentSchema.partial().required({
  id: true,
  tenantId: true
});

// Type alias for TypeScript to help with Zod validation
export type UpdateAuthoredDocumentInput = z.infer<typeof UpdateAuthoredDocumentSchema>;

/**
 * Maps a database authored document row to a domain authored document object
 */
export function mapAuthoredDocumentRowToAuthoredDocument(
  row: Database['tenants']['Tables']['authored_documents']['Row']
): AuthoredDocument {
  return {
    id: row.id,
    tenantId: row.tenant_id,
    title: row.title,
    content: row.content,
    caseId: row.case_id,
    clientId: row.client_id,
    templateId: row.template_id,
    status: (row.status as AuthoredDocumentStatus),
    embeddingStatus: (row.embedding_status as EmbeddingStatus | null),
    gcsPath: row.gcs_path,
    sentAt: row.sent_at,
    signedAt: row.signed_at,
    lastEmbeddedAt: row.last_embedded_at,
    version: row.version,
    variablesUsed: typeof row.variables_used === 'string'
      ? JSON.parse(row.variables_used)
      : row.variables_used,
    metadata: typeof row.metadata === 'string'
      ? JSON.parse(row.metadata)
      : row.metadata,
    createdAt: row.created_at,
    createdBy: row.created_by,
    updatedAt: row.updated_at,
    updatedBy: row.updated_by
  };
}

/**
 * Maps a domain authored document object to a database authored document insert
 */
export function mapCreateAuthoredDocumentDtoToAuthoredDocumentInsert(
  document: z.infer<typeof CreateAuthoredDocumentSchema>,
  tenantId: string,
  userId: string
): Database['tenants']['Tables']['authored_documents']['Insert'] {
  return {
    tenant_id: tenantId,
    title: document.title,
    content: document.content,
    case_id: document.caseId,
    client_id: document.clientId,
    template_id: document.templateId,
    status: document.status,
    gcs_path: document.gcsPath,
    sent_at: document.sentAt,
    signed_at: document.signedAt,
    version: document.version,
    variables_used: document.variablesUsed as any,
    metadata: document.metadata as any,
    created_by: userId,
    embedding_status: 'PENDING' // Default for new documents
  };
}

/**
 * Maps a domain authored document update object to a database authored document update
 */
export function mapUpdateAuthoredDocumentDtoToAuthoredDocumentUpdate(
  document: UpdateAuthoredDocumentInput,
  userId: string
): Database['tenants']['Tables']['authored_documents']['Update'] {
  const update: Database['tenants']['Tables']['authored_documents']['Update'] = {};

  if (document.title !== undefined) update.title = document.title;
  if (document.content !== undefined) update.content = document.content;
  if (document.caseId !== undefined) update.case_id = document.caseId;
  if (document.clientId !== undefined) update.client_id = document.clientId;
  if (document.templateId !== undefined) update.template_id = document.templateId;
  if (document.status !== undefined) update.status = document.status;
  if (document.embeddingStatus !== undefined) update.embedding_status = document.embeddingStatus;
  if (document.gcsPath !== undefined) update.gcs_path = document.gcsPath;
  if (document.sentAt !== undefined) update.sent_at = document.sentAt;
  if (document.signedAt !== undefined) update.signed_at = document.signedAt;
  if (document.lastEmbeddedAt !== undefined) update.last_embedded_at = document.lastEmbeddedAt;
  if (document.version !== undefined) update.version = document.version;
  if (document.variablesUsed !== undefined) update.variables_used = document.variablesUsed as any;
  if (document.metadata !== undefined) update.metadata = document.metadata as any;

  update.updated_at = new Date().toISOString();
  update.updated_by = userId;

  return update;
}

/**
 * Generate a GCS path for an authored document
 */
export function generateAuthoredDocumentGcsPath(
  tenantId: string,
  documentId: string,
  caseId?: string | null,
  version?: number | null
): string {
  const versionSuffix = version ? `-v${version}` : '';
  const casePath = caseId ? `cases/${caseId}/` : '';
  return `tenants/${tenantId}/${casePath}authored-documents/${documentId}${versionSuffix}`;
}

/**
 * Validation utilities for working with authored documents
 */
export const AuthoredDocumentValidation = {
  /**
   * Check if an authored document is in draft status
   */
  isDraft(document: AuthoredDocument): boolean {
    return document.status === AuthoredDocumentStatus.DRAFT;
  },

  /**
   * Check if an authored document has been sent
   */
  isSent(document: AuthoredDocument): boolean {
    return document.status === AuthoredDocumentStatus.SENT;
  },

  /**
   * Check if an authored document has been signed
   */
  isSigned(document: AuthoredDocument): boolean {
    return document.status === AuthoredDocumentStatus.SIGNED;
  },

  /**
   * Check if an authored document has been embedded
   */
  isEmbedded(document: AuthoredDocument): boolean {
    return document.embeddingStatus === EmbeddingStatus.COMPLETED;
  },

  /**
   * Check if an authored document uses a template
   */
  usesTemplate(document: AuthoredDocument): boolean {
    return document.templateId !== null;
  },

  /**
   * Check if an authored document is associated with a case
   */
  hasCase(document: AuthoredDocument): boolean {
    return document.caseId !== null;
  },

  /**
   * Check if an authored document is recent (within specified days)
   */
  isRecent(document: AuthoredDocument, days: number = 7): boolean {
    const documentDate = document.updatedAt || document.createdAt;
    if (!documentDate) return false;

    const date = new Date(documentDate);
    const compareDate = new Date();
    compareDate.setDate(compareDate.getDate() - days);
    return date >= compareDate;
  },

  /**
   * Extract variables from an authored document content
   * Assuming variables are marked with {{ variableName }}
   */
  extractVariables(content: string): string[] {
    const regex = /\{\{\s*([^{}]+)\s*\}\}/g;
    const matches = content.match(regex) || [];
    return matches.map(match => {
      // Remove {{ and }} and trim whitespace
      return match.replace(/\{\{\s*|\s*\}\}/g, '').trim();
    });
  },

  /**
   * Get a document status label
   */
  getStatusLabel(document: AuthoredDocument): string {
    switch (document.status) {
      case AuthoredDocumentStatus.DRAFT:
        return 'Draft';
      case AuthoredDocumentStatus.READY:
        return 'Ready';
      case AuthoredDocumentStatus.SENT:
        return 'Sent';
      case AuthoredDocumentStatus.SIGNED:
        return 'Signed';
      case AuthoredDocumentStatus.ARCHIVED:
        return 'Archived';
      default:
        return document.status;
    }
  }
};

/**
 * AuthoredDocumentRepository class for working with authored documents
 */
export class AuthoredDocumentRepository {
  /**
   * Get all authored documents for a tenant
   */
  static async getAllAuthoredDocuments(
    tenantId: string,
    options: {
      status?: AuthoredDocumentStatus | AuthoredDocumentStatus[];
      limit?: number;
      includeRelations?: boolean;
      caseId?: string;
      clientId?: string;
    } = {}
  ): Promise<AuthoredDocument[]> {
    try {
      const {
        status,
        limit = 50,
        includeRelations = false,
        caseId,
        clientId
      } = options;

      // Set up query with schema context
      let query = supabase
        .schema('tenants')
        .from('authored_documents')
        .select(includeRelations ?
          `*, case:case_id(id, title), client:client_id(id, first_name, last_name), template:template_id(id, title)` :
          '*'
        )
        .eq('tenant_id', tenantId);

      // Apply filters
      if (status) {
        if (Array.isArray(status)) {
          query = query.in('status', status);
        } else {
          query = query.eq('status', status);
        }
      }

      if (caseId) {
        query = query.eq('case_id', caseId);
      }

      if (clientId) {
        query = query.eq('client_id', clientId);
      }

      // Order and limit
      query = query
        .order('updated_at', { ascending: false })
        .limit(limit);

      const { data, error } = await query;

      if (error) {
        console.error('Error fetching authored documents:', error.message);
        return [];
      }

      // Handle potential type issues with the query result
      // Use explicit type assertion to bypass TypeScript's complex union type checking
      const rawData = data as any[] || [];
      const validRows = rawData.filter(row => typeof row === 'object' && row !== null && !('error' in row));
      const authoredDocuments = validRows.map(row => {
          // TypeScript workaround: cast to any to avoid complex union type issues
          const validRow = row as any;
          const doc = mapAuthoredDocumentRowToAuthoredDocument(validRow);

          // Process relations if included and available
          if (includeRelations) {
            // Define proper types for join results
            type JoinRowType = Database['tenants']['Tables']['authored_documents']['Row'] & {
              case?: { id: string; title: string };
              client?: { id: string; first_name: string | null; last_name: string | null };
              template?: { id: string; title: string };
            };

            const typedRow = validRow as JoinRowType;

            if (typedRow.case) {
              doc.case = {
                id: typedRow.case.id,
                title: typedRow.case.title
              };
            }

            if (typedRow.client) {
              doc.client = {
                id: typedRow.client.id,
                fullName: `${typedRow.client.first_name || ''} ${typedRow.client.last_name || ''}`.trim()
              };
            }

            if (typedRow.template) {
              doc.template = {
                id: typedRow.template.id,
                title: typedRow.template.title
              };
            }
          }

          return doc;
        });

      return authoredDocuments;
    } catch (_error) {
      console.error('Error in getAllAuthoredDocuments:', error);
      return [];
    }
  }

  /**
   * Get an authored document by ID
   */
  static async getAuthoredDocumentById(
    documentId: string,
    tenantId: string,
    includeRelations: boolean = false
  ): Promise<AuthoredDocument | null> {
    try {
      const { data, error } = await supabase
        .schema('tenants')
        .from('authored_documents')
        .select(includeRelations ?
          `*, case:case_id(id, title), client:client_id(id, first_name, last_name), template:template_id(id, title)` :
          '*'
        )
        .eq('id', documentId)
        .eq('tenant_id', tenantId)
        .single();

      if (error) {
        console.error('Error fetching authored document by ID:', error.message);
        return null;
      }

      if (!data) return null;

      // Ensure we're working with a valid database row
      if (typeof data !== 'object' || data === null) {
        console.error('Invalid data returned from database');
        return null;
      }

      const doc = mapAuthoredDocumentRowToAuthoredDocument(data as Database['tenants']['Tables']['authored_documents']['Row']);

      // Process relations if included and available
      if (includeRelations) {
        // Define proper types for join results
        type JoinRowType = Database['tenants']['Tables']['authored_documents']['Row'] & {
          case?: { id: string; title: string };
          client?: { id: string; first_name: string | null; last_name: string | null };
          template?: { id: string; title: string };
        };

        const typedData = data as JoinRowType;

        if (typedData.case) {
          doc.case = {
            id: typedData.case.id,
            title: typedData.case.title
          };
        }

        if (typedData.client) {
          doc.client = {
            id: typedData.client.id,
            fullName: `${typedData.client.first_name || ''} ${typedData.client.last_name || ''}`.trim()
          };
        }

        if (typedData.template) {
          doc.template = {
            id: typedData.template.id,
            title: typedData.template.title
          };
        }
      }

      return doc;
    } catch (_error) {
      console.error('Error in getAuthoredDocumentById:', error);
      return null;
    }
  }

  /**
   * Create a new authored document
   */
  static async createAuthoredDocument(
    documentData: z.infer<typeof CreateAuthoredDocumentSchema>,
    tenantId: string,
    userId: string
  ): Promise<AuthoredDocument | null> {
    try {
      // Validate the input data
      CreateAuthoredDocumentSchema.parse(documentData);

      const documentInsert = mapCreateAuthoredDocumentDtoToAuthoredDocumentInsert(documentData as any, tenantId, userId);

      // Generate GCS path if not provided
      if (!documentInsert.gcs_path) {
        // We need to get the ID first
        const { data: idData, error: idError } = await supabase
          .schema('tenants')
          .from('authored_documents')
          .insert(documentInsert)
          .select('id')
          .single();

        if (idError) {
          console.error('Error creating authored document:', idError.message);
          return null;
        }

        if (!idData) return null;

        // Now update with GCS path
        const gcsPath = generateAuthoredDocumentGcsPath(
          tenantId,
          idData.id,
          documentInsert.case_id,
          documentInsert.version
        );

        const { data, error } = await supabase
          .schema('tenants')
          .from('authored_documents')
          .update({ gcs_path: gcsPath })
          .eq('id', idData.id)
          .eq('tenant_id', tenantId)
          .select()
          .single();

        if (error) {
          console.error('Error updating GCS path:', error.message);
          return null;
        }

        return mapAuthoredDocumentRowToAuthoredDocument(data as Database['tenants']['Tables']['authored_documents']['Row']);
      } else {
        // GCS path was provided, just insert
        const { data, error } = await supabase
          .schema('tenants')
          .from('authored_documents')
          .insert(documentInsert)
          .select()
          .single();

        if (error) {
          console.error('Error creating authored document:', error.message);
          return null;
        }

        return mapAuthoredDocumentRowToAuthoredDocument(data as Database['tenants']['Tables']['authored_documents']['Row']);
      }
    } catch (_error) {
      console.error('Error in createAuthoredDocument:', error);
      return null;
    }
  }

  /**
   * Update an existing authored document
   */
  static async updateAuthoredDocument(
    documentData: UpdateAuthoredDocumentInput,
    tenantId: string,
    userId: string
  ): Promise<AuthoredDocument | null> {
    try {
      // Assert that id exists in the documentData object
      if (!documentData.id) {
        throw new Error('Missing required property: id');
      }

      // Type cast to fix TypeScript validation error
      const validationData = { ...documentData, id: documentData.id, tenantId };
      UpdateAuthoredDocumentSchema.parse(validationData);

      const { id, ...updateData } = documentData;
      const documentUpdate = mapUpdateAuthoredDocumentDtoToAuthoredDocumentUpdate(updateData as any, userId);

      // Handle updating embedding status when content changes
      if (documentUpdate.content !== undefined) {
        documentUpdate.embedding_status = 'PENDING';
      }

      const { data, error } = await supabase
        .schema('tenants')
        .from('authored_documents')
        .update(documentUpdate)
        .eq('id', id)
        .eq('tenant_id', tenantId)
        .select()
        .single();

      if (error) {
        console.error('Error updating authored document:', error.message);
        return null;
      }

      return mapAuthoredDocumentRowToAuthoredDocument(data as Database['tenants']['Tables']['authored_documents']['Row']);
    } catch (_error) {
      console.error('Error in updateAuthoredDocument:', error);
      return null;
    }
  }

  /**
   * Update an authored document status
   */
  static async updateAuthoredDocumentStatus(
    documentId: string,
    status: AuthoredDocumentStatus,
    tenantId: string,
    userId: string
  ): Promise<AuthoredDocument | null> {
    try {
      // Special handling for SENT and SIGNED statuses
      const update: Database['tenants']['Tables']['authored_documents']['Update'] = {
        status,
        updated_at: new Date().toISOString(),
        updated_by: userId
      };

      // Set sent_at when status changes to SENT
      if (status === AuthoredDocumentStatus.SENT) {
        update.sent_at = new Date().toISOString();
      }

      // Set signed_at when status changes to SIGNED
      if (status === AuthoredDocumentStatus.SIGNED) {
        update.signed_at = new Date().toISOString();
      }

      const { data, error } = await supabase
        .schema('tenants')
        .from('authored_documents')
        .update(update)
        .eq('id', documentId)
        .eq('tenant_id', tenantId)
        .select()
        .single();

      if (error) {
        console.error('Error updating authored document status:', error.message);
        return null;
      }

      return mapAuthoredDocumentRowToAuthoredDocument(data as Database['tenants']['Tables']['authored_documents']['Row']);
    } catch (_error) {
      console.error('Error in updateAuthoredDocumentStatus:', error);
      return null;
    }
  }

  /**
   * Delete an authored document
   */
  static async deleteAuthoredDocument(
    documentId: string,
    tenantId: string
  ): Promise<boolean> {
    try {
      const { error } = await supabase
        .schema('tenants')
        .from('authored_documents')
        .delete()
        .eq('id', documentId)
        .eq('tenant_id', tenantId);

      if (error) {
        console.error('Error deleting authored document:', error.message);
        return false;
      }

      return true;
    } catch (_error) {
      console.error('Error in deleteAuthoredDocument:', error);
      return false;
    }
  }

  /**
   * Get authored documents by case ID
   */
  static async getAuthoredDocumentsByCase(
    caseId: string,
    tenantId: string,
    options: {
      limit?: number;
      includeRelations?: boolean;
    } = {}
  ): Promise<AuthoredDocument[]> {
    try {
      const { limit = 50, includeRelations = false } = options;

      return this.getAllAuthoredDocuments(tenantId, {
        limit,
        includeRelations,
        caseId
      });
    } catch (_error) {
      console.error('Error in getAuthoredDocumentsByCase:', error);
      return [];
    }
  }

  /**
   * Get authored documents by client ID
   */
  static async getAuthoredDocumentsByClient(
    clientId: string,
    tenantId: string,
    options: {
      limit?: number;
      includeRelations?: boolean;
    } = {}
  ): Promise<AuthoredDocument[]> {
    try {
      const { limit = 50, includeRelations = false } = options;

      return this.getAllAuthoredDocuments(tenantId, {
        limit,
        includeRelations,
        clientId
      });
    } catch (_error) {
      console.error('Error in getAuthoredDocumentsByClient:', error);
      return [];
    }
  }

  /**
   * Get authored documents created from a specific template
   */
  static async getAuthoredDocumentsByTemplate(
    templateId: string,
    tenantId: string,
    options: {
      limit?: number;
      includeRelations?: boolean;
    } = {}
  ): Promise<AuthoredDocument[]> {
    try {
      const { limit = 50, includeRelations = false } = options;

      const query = supabase
        .schema('tenants')
        .from('authored_documents')
        .select(includeRelations ?
          `*, case:case_id(id, title), client:client_id(id, first_name, last_name), template:template_id(id, title)` :
          '*'
        )
        .eq('tenant_id', tenantId)
        .eq('template_id', templateId)
        .order('updated_at', { ascending: false })
        .limit(limit);

      const { data, error } = await query;

      if (error) {
        console.error('Error fetching authored documents by template:', error.message);
        return [];
      }

      // Handle potential type issues with the query result
      // Use explicit type assertion to bypass TypeScript's complex union type checking
      const rawData = data as any[] || [];
      const validRows = rawData.filter(row => typeof row === 'object' && row !== null && !('error' in row));
      return validRows.map(row => mapAuthoredDocumentRowToAuthoredDocument(row));
    } catch (_error) {
      console.error('Error in getAuthoredDocumentsByTemplate:', error);
      return [];
    }
  }
}
