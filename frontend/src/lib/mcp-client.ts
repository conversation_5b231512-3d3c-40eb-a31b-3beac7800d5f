// frontend/src/lib/mcp-client.ts
import { z } from 'zod';

// Define the structure of the request payload sent to the MCP server
export const McpCalculationRequestSchema = z.object({
  jurisdiction_id: z.string().min(1, 'Jurisdiction ID is required'),
  trigger_date: z.string().regex(/^\d{4}-\d{2}-\d{2}$/, 'Trigger date must be in YYYY-MM-DD format'),
  rule_id: z.string().min(1, 'Rule ID is required'),
  task_id: z.string().optional(), // Optional task ID for context
});

export type McpCalculationRequest = z.infer<typeof McpCalculationRequestSchema>;

// Define the structure of a single computed deadline in the response
const McpComputedDeadlineSchema = z.object({
  deadline_date: z.string().regex(/^\d{4}-\d{2}-\d{2}$/, 'Deadline date must be in YYYY-MM-DD format'),
  rule_citation: z.string().optional(), // Rule citation might be optional
  calculation_steps: z.array(z.string()).optional(), // Steps might be optional
});

// Define the structure of the successful response payload from the MCP server
export const McpCalculationResponseSchema = z.object({
  deadlines: z.array(McpComputedDeadlineSchema),
});

export type McpCalculationResponse = z.infer<typeof McpCalculationResponseSchema>;

// Custom error class for MCP Client specific errors
export class McpClientError extends Error {
  status?: number;
  details?: unknown;

  constructor(message: string, status?: number, details?: unknown) {
    super(message);
    this.name = 'McpClientError';
    this.status = status;
    this.details = details;
  }
}

const MCP_API_URL = process.env.MCP_RULES_URL;

if (!MCP_API_URL) {
  console.warn('Warning: MCP_RULES_URL environment variable is not set. MCP functionality will be disabled.');
}

/**
 * Calls the MCP Rules Engine server to calculate deadlines.
 *
 * @param {McpCalculationRequest} payload - The data required for calculation.
 * @returns {Promise<McpCalculationResponse>} The calculated deadlines and details.
 * @throws {McpClientError} If the MCP server returns an error or the request fails.
 */
export async function calculateDeadline(payload: McpCalculationRequest): Promise<McpCalculationResponse> {
  if (!MCP_API_URL) {
    throw new McpClientError('MCP Rules URL is not configured in environment variables.');
  }

  // Validate payload structure before sending (optional but recommended)
  const validation = McpCalculationRequestSchema.safeParse(payload);
  if (!validation.success) {
    console.error('Invalid MCP request payload:', validation.error.errors);
    throw new McpClientError('Invalid request payload for MCP calculation.', 400, validation.error.errors);
  }

  console.log(`Sending request to MCP: ${MCP_API_URL} with payload:`, payload);

  try {
    const response = await fetch(MCP_API_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        // Add Authorization header here if authentication is added later
        // 'Authorization': `Bearer ${process.env.MCP_API_KEY}`
      },
      body: JSON.stringify(payload),
    });

    // Attempt to parse JSON regardless of status for potential error details
    let responseBody;
    try {
      responseBody = await response.json();
    } catch (parseError) {
      // Handle cases where the response isn't valid JSON
      if (!response.ok) {
        throw new McpClientError(
          `MCP request failed with status ${response.status}. Response not valid JSON.`,
          response.status
        );
      }
      // If response was ok but not JSON, this is unexpected
      console.error('MCP response was OK but not valid JSON:', parseError);
      throw new McpClientError('Received unexpected non-JSON response from MCP server.', response.status);
    }

    if (!response.ok) {
      console.error(`MCP request failed with status ${response.status}:`, responseBody);
      throw new McpClientError(
        `MCP request failed with status ${response.status}`,
        response.status,
        responseBody // Include error details from MCP if available
      );
    }

    // Validate the successful response structure (optional but recommended)
    const responseValidation = McpCalculationResponseSchema.safeParse(responseBody);
    if (!responseValidation.success) {
        console.error('Invalid MCP response structure:', responseValidation.error.errors);
        throw new McpClientError('Received invalid response structure from MCP server.', response.status, responseValidation.error.errors);
    }

    console.log('Received successful response from MCP:', responseValidation.data);
    return responseValidation.data;

  } catch (_error) {
    // Handle network errors or errors thrown by the checks above
    if (error instanceof McpClientError) {
      // Re-throw specific MCP errors
      throw error;
    } else if (error instanceof Error) {
      // Handle fetch/network errors
      console.error('Network or other error calling MCP:', error);
      throw new McpClientError(`Network error calling MCP: ${error.message}`);
    } else {
      // Handle unexpected errors
      console.error('Unexpected error type calling MCP:', error);
      throw new McpClientError('An unexpected error occurred while contacting the MCP server.');
    }
  }
}

// Types are already exported where defined, no need for extra exports here.
