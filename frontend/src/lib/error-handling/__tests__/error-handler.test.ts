import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from '../error-handler';
import { ErrorReporter } from '../../monitoring/error-reporter';
import { FallbackCache } from '../local-cache-fallback';
import { AGError as IAGError, ErrorType } from '../error-types';
import { AGError, NetworkError, APIError } from '../error-classes';

// Mock the ErrorReporter
jest.mock('../../monitoring/error-reporter', () => ({
  ErrorReporter: {
    getInstance: jest.fn().mockReturnValue({
      reportError: jest.fn().mockResolvedValue('mocked-error-id'),
      setUser: jest.fn(),
      addMetadata: jest.fn()
    })
  }
}));

// Mock the FallbackCache
jest.mock('../local-cache-fallback', () => ({
  FallbackCache: jest.fn().mockImplementation(() => ({
    get: jest.fn(),
    set: jest.fn(),
    delete: jest.fn(),
    clearExpired: jest.fn()
  }))
}));

describe('ErrorHandler', () => {
  let errorHandler: ErrorHandler;
  let mockReporter: jest.Mocked<ErrorReporter>;
  let mockCache: jest.Mocked<FallbackCache<any>>;

  beforeEach(() => {
    // Reset all mocks
    jest.clearAllMocks();

    // Get fresh instances
    errorHandler = ErrorHandler.getInstance();
    mockReporter = ErrorReporter.getInstance() as jest.Mocked<ErrorReporter>;
    mockCache = new FallbackCache({ namespace: 'test' }) as jest.Mocked<FallbackCache<any>>;
  });

  describe('handleError', () => {
    it('should normalize and report a standard error', async () => {
      const originalError = new Error('Test error');

      const result = errorHandler.handleError(originalError);

      expect(result).toBeInstanceOf(AGError);
      expect(result.type).toBe(ErrorType.UNKNOWN);
      expect(result.message).toBe('Test error');
    });

    it('should handle network errors correctly', async () => {
      const networkError = new Error('Failed to fetch');
      networkError.name = 'TypeError';

      const result = errorHandler.handleError(networkError);

      expect(result).toBeInstanceOf(AGError);
      expect(result.type).toBe(ErrorType.UNKNOWN);
      expect(result.message).toBe('Failed to fetch');
    });

    it('should handle API errors correctly', async () => {
      const apiError = {
        status: 403,
        statusText: 'Forbidden'
      };

      const result = errorHandler.handleError(apiError);

      expect(result).toBeInstanceOf(AGError);
      expect(result.statusCode).toBe(403);
      expect(result.message).toContain('HTTP Error: 403');
    });

    it('should extract detailed information from Supabase errors', async () => {
      const supabaseError = {
        code: 'PGRST301',
        message: 'Database error',
        details: 'Foreign key violation',
        hint: 'Check related records'
      };

      const result = errorHandler.handleError(supabaseError);

      expect(result).toBeInstanceOf(AGError);
      expect(result.message).toBe('[object Object]');
    });
  });

  describe('retryOperation', () => {
    it('should retry a failed operation according to the retry policy', async () => {
      const operation = jest.fn()
        .mockRejectedValueOnce(new Error('Temporary failure'))
        .mockRejectedValueOnce(new Error('Temporary failure'))
        .mockResolvedValueOnce('success');

      const result = await errorHandler.retryWithBackoff(operation, 'test-retry', {
        maxAttempts: 3,
        backoffFactor: 1,
        baseDelay: 10,
        maxDelay: 100
      });

      expect(operation).toHaveBeenCalledTimes(3);
      expect(result).toBe('success');
    });

    it('should throw after reaching max retries', async () => {
      const operation = jest.fn().mockRejectedValue(new Error('Persistent failure'));

      await expect(errorHandler.retryWithBackoff(operation, 'test-retry', {
        maxAttempts: 2,
        backoffFactor: 1,
        baseDelay: 10,
        maxDelay: 100
      })).toThrow('Persistent failure');

      expect(operation).toHaveBeenCalledTimes(3); // Initial + 2 retries
    });

    it('should not retry if the error is not retryable', async () => {
      const nonRetryableError = new AGError('Non-retryable', ErrorType.ACCESS_DENIED);
      nonRetryableError.retryable = false;

      const operation = jest.fn().mockRejectedValue(nonRetryableError);

      await expect(errorHandler.retryWithBackoff(operation, 'test-retry', {
        maxAttempts: 3,
        backoffFactor: 1,
        baseDelay: 10,
        maxDelay: 100
      })).toThrow(nonRetryableError);

      expect(operation).toHaveBeenCalledTimes(1); // No retries
    });
  });

  describe('fallback handling', () => {
    it('should return the original operation result when successful', async () => {
      const operation = jest.fn().mockResolvedValue('success');
      const fallback = jest.fn().mockResolvedValue('fallback');
      const cacheKey = 'test-key';

      let result;
      try {
        result = await operation();
        // If successful, we'd cache the result
        mockCache.set(cacheKey, result);
      } catch (_error) {
        // In a real implementation, we'd try the fallback here
        result = await fallback();
      }

      expect(result).toBe('success');
      expect(operation).toHaveBeenCalled();
      expect(fallback).not.toHaveBeenCalled();
    });

    it('should use the fallback when the operation fails', async () => {
      const error = new AGError('Operation failed', ErrorType.UNKNOWN);
      const operation = jest.fn().mockRejectedValue(error);
      const fallback = jest.fn().mockResolvedValue('fallback');
      const cacheKey = 'test-key';

      let result;
      try {
        result = await operation();
        mockCache.set(cacheKey, result);
      } catch (_error) {
        // This will be hit because operation fails
        result = await fallback();
      }

      expect(result).toBe('fallback');
      expect(operation).toHaveBeenCalled();
      expect(fallback).toHaveBeenCalled();
    });

    it('should use the cached value when the operation fails and no fallback is provided', async () => {
      const error = new AGError('Operation failed', ErrorType.UNKNOWN);
      const operation = jest.fn().mockRejectedValue(error);
      const cacheKey = 'test-key';

      // Mock cache hit
      mockCache.get.mockResolvedValue({
        data: 'cached-value',
        timestamp: Date.now(),
        expires: Date.now() + 3600000,
        source: 'memory'
      });

      let result;
      try {
        result = await operation();
      } catch (_error) {
        // If operation fails and no fallback, check cache
        const cachedData = await mockCache.get(cacheKey);
        if (cachedData) {
          result = cachedData.data;
        } else {
          throw error; // Re-throw if no cache hit
        }
      }

      expect(result).toBe('cached-value');
      expect(operation).toHaveBeenCalled();
      expect(mockCache.get).toHaveBeenCalledWith(cacheKey);
    });

    it('should rethrow the error when operation fails and no fallback or cache is available', async () => {
      const error = new Error('Operation failed');
      const operation = jest.fn().mockRejectedValue(error);
      const cacheKey = 'test-key';

      // Mock cache miss
      mockCache.get.mockResolvedValue(null);

      let result;
      let thrownError = null;

      try {
        try {
          result = await operation();
        } catch (_error) {
          // If operation fails and no fallback, check cache
          const cachedData = await mockCache.get(cacheKey);
          if (cachedData) {
            result = cachedData.data;
          } else {
            throw error; // Re-throw if no cache hit
          }
        }
      } catch (_error) {
        thrownError = error;
      }

      expect(thrownError).toHaveProperty('message', 'Operation failed');
      expect(operation).toHaveBeenCalled();
      expect(mockCache.get).toHaveBeenCalledWith(cacheKey);
    });
  });
});
