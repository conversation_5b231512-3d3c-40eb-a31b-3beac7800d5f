/**
 * API Fallback Middleware for AG-UI Integration
 * Provides graceful fallbacks for API calls
 */

import { NextRequest, NextResponse } from 'next/server';
import { errorHandler } from './error-handler';
import { AGError, ErrorType } from './error-types';
import { performanceMonitor } from '@/lib/performance/metrics';

// Type for handler with fallback support
export type ApiHandlerWithFallback = (
  req: NextRequest,
  fallbackData?: any
) => Promise<Response>;

// Options for withApiFallbacks
export interface ApiFallbackOptions {
  // Cached response to use as fallback (if available)
  cacheFallback?: boolean;
  
  // Whether to retry failed requests
  retry?: boolean;
  
  // Maximum number of retries
  maxRetries?: number;
  
  // Timeout for requests in ms
  timeoutMs?: number;
  
  // Key for tracking retry attempts
  requestKey?: string;
  
  // Static fallback data if all else fails
  staticFallback?: any;
  
  // Whether to transform error responses into user-friendly format
  transformErrors?: boolean;
}

// Default options
const DEFAULT_OPTIONS: ApiFallbackOptions = {
  cacheFallback: true,
  retry: true,
  maxRetries: 3,
  timeoutMs: 30000,
  transformErrors: true
};

/**
 * Middleware to add graceful fallbacks to API handlers
 * @param handler Original API handler function
 * @param options Fallback options
 * @returns Enhanced handler with fallbacks
 */
export function withApiFallbacks(
  handler: ApiHandlerWithFallback,
  options: ApiFallbackOptions = {}
): ApiHandlerWithFallback {
  const mergedOptions = { ...DEFAULT_OPTIONS, ...options };
  const requestKey = mergedOptions.requestKey || `api_${Date.now()}`;
  
  // Return the enhanced handler
  return async (_req: NextRequest, fallbackData?: any) => {
    // Start performance monitoring
    const startTime = performanceMonitor.startRequest(requestKey);

    try {
      // Clone request for potential retries
      const reqClone = req.clone();
      const requestType = req.method === 'GET' ? 'query' : 'mutation';
      
      // Try cached response if enabled and this is a GET request
      let cachedResponse = null;
      if (mergedOptions.cacheFallback && req.method === 'GET') {
        cachedResponse = await tryGetCachedResponse(req);
        if (cachedResponse) {
          performanceMonitor.completeRequest(requestKey, { 
            cacheHit: true, 
            cacheTier: 'memory'
          });
          return cachedResponse;
        }
      }
      
      // Handle timeout if configured
      const timeoutPromise = mergedOptions.timeoutMs
        ? createTimeoutPromise(mergedOptions.timeoutMs)
        : null;
      
      // Try to execute the handler
      const responsePromise = handler(req, fallbackData);
      
      // Race with timeout if configured
      const response = timeoutPromise 
        ? await Promise.race([responsePromise, timeoutPromise])
        : await responsePromise;
      
      // Record successful response
      performanceMonitor.completeRequest(requestKey, {
        cacheHit: false
      });
      
      return response;
    } catch (_error) {
      // Handle error with appropriate fallback
      const agError = errorHandler.handleError(error, { 
        url: req.url,
        method: req.method
      });
      
      // Try retry if enabled
      if (mergedOptions.retry && agError.retryable) {
        try {
          // Skip the middleware wrapper to avoid infinite loops
          const retryResponse = await errorHandler.retryWithBackoff(
            () => handler(req, fallbackData),
            requestKey
          );
          
          performanceMonitor.completeRequest(requestKey, {
            cacheHit: false
          });
          
          return retryResponse;
        } catch (retryError) {
          // Retry failed, continue to other fallbacks
          console.warn('Retry failed', retryError);
        }
      }
      
      // Try static fallback if provided
      if (mergedOptions.staticFallback) {
        performanceMonitor.completeRequest(requestKey, {
          cacheHit: false
        });
        
        return createFallbackResponse(mergedOptions.staticFallback);
      }
      
      // If no fallbacks worked, return transformed error
      performanceMonitor.completeRequest(requestKey, {
        cacheHit: false
      });
      
      return createErrorResponse(agError, mergedOptions.transformErrors);
    }
  };
}

// Create a timeout promise that rejects after specified time
function createTimeoutPromise(timeoutMs: number): Promise<never> {
  return new Promise((_, reject) => {
    setTimeout(() => {
      reject(new Error(`Request timed out after ${timeoutMs}ms`));
    }, timeoutMs);
  });
}

// Try to get a cached response for a request
async function tryGetCachedResponse(_req: NextRequest): Promise<Response | null> {
  // Implementation depends on your caching strategy
  // This is a placeholder - integrate with your actual cache
  return null;
}

// Create a response from fallback data
function createFallbackResponse(data: any): Response {
  return new Response(JSON.stringify({
    data,
    meta: {
      fromFallback: true,
      timestamp: Date.now()
    }
  }), {
    status: 200,
    headers: {
      'Content-Type': 'application/json',
      'X-Fallback': 'true'
    }
  });
}

// Create an error response
function createErrorResponse(error: AGError, transform: boolean = true): Response {
  // If not transforming, just return a simple error
  if (!transform) {
    return new Response(JSON.stringify({ error: error.message }), {
      status: error.statusCode || 500,
      headers: {
        'Content-Type': 'application/json'
      }
    });
  }
  
  // Transform into user-friendly format with guidance
  const responseBody = {
    error: {
      message: getUserFriendlyMessage(error),
      type: error.type,
      actionable: getActionableSteps(error),
      retryable: error.retryable,
      id: `err_${Date.now().toString(36)}`
    }
  };
  
  // Status code based on error type
  const statusCode = error.statusCode || getStatusCodeFromErrorType(error.type);
  
  return new Response(JSON.stringify(responseBody), {
    status: statusCode,
    headers: {
      'Content-Type': 'application/json',
      'X-Error-Type': error.type,
      'X-Error-Id': responseBody.error.id
    }
  });
}

// Get status code from error type
function getStatusCodeFromErrorType(errorType: ErrorType): number {
  switch (errorType) {
    case ErrorType.NETWORK_OFFLINE:
    case ErrorType.NETWORK_TIMEOUT:
      return 503;
      
    case ErrorType.AUTH_EXPIRED:
    case ErrorType.AUTH_INVALID:
    case ErrorType.AUTH_MISSING:
      return 401;
      
    case ErrorType.ACCESS_DENIED:
    case ErrorType.TENANT_MISMATCH:
      return 403;
      
    case ErrorType.QUOTA_EXCEEDED:
      return 429;
      
    case ErrorType.SERVER_ERROR:
    case ErrorType.SERVER_OVERLOAD:
    case ErrorType.SERVICE_UNAVAILABLE:
      return 500;
      
    case ErrorType.DATA_VALIDATION:
      return 400;
      
    default:
      return 500;
  }
}

// Get user-friendly message from error
function getUserFriendlyMessage(error: AGError): string {
  switch (error.type) {
    case ErrorType.NETWORK_OFFLINE:
      return "You appear to be offline. Please check your internet connection and try again.";
      
    case ErrorType.NETWORK_TIMEOUT:
      return "The request took too long to complete. This might be due to a slow connection or server issues.";
      
    case ErrorType.AUTH_EXPIRED:
      return "Your session has expired. Please log in again to continue.";
      
    case ErrorType.AUTH_INVALID:
    case ErrorType.AUTH_MISSING:
      return "Authentication failed. Please log in and try again.";
      
    case ErrorType.ACCESS_DENIED:
      return "You don't have permission to perform this action.";
      
    case ErrorType.TENANT_MISMATCH:
      return "This resource belongs to a different organization.";
      
    case ErrorType.QUOTA_EXCEEDED:
      return "You've reached your usage limit. Please try again later or upgrade your plan.";
      
    case ErrorType.SERVER_ERROR:
    case ErrorType.SERVER_OVERLOAD:
    case ErrorType.SERVICE_UNAVAILABLE:
      return "The service is temporarily unavailable. Our team has been notified and is working on it.";
      
    case ErrorType.LLM_CONTEXT_LIMIT:
      return "The conversation is too long for the AI to process. Try starting a new conversation.";
      
    case ErrorType.LLM_CONTENT_FILTER:
    case ErrorType.LLM_GUARDRAIL:
      return "The request was blocked by content safety filters.";
      
    default:
      return error.message || "An unexpected error occurred.";
  }
}

// Get actionable steps based on error type
function getActionableSteps(error: AGError): string[] {
  switch (error.type) {
    case ErrorType.NETWORK_OFFLINE:
      return [
        "Check your internet connection",
        "Try refreshing the page",
        "If you're on mobile, make sure you're not in airplane mode"
      ];
      
    case ErrorType.AUTH_EXPIRED:
    case ErrorType.AUTH_INVALID:
    case ErrorType.AUTH_MISSING:
      return [
        "Log out and log back in",
        "Clear your browser cache and cookies",
        "Contact support if the problem persists"
      ];
      
    case ErrorType.ACCESS_DENIED:
      return [
        "Verify you have the necessary permissions",
        "Contact your administrator for access"
      ];
      
    case ErrorType.QUOTA_EXCEEDED:
      return [
        "Try again later when your limit resets",
        "Contact your administrator about upgrading your plan"
      ];
      
    case ErrorType.LLM_CONTEXT_LIMIT:
      return [
        "Start a new conversation",
        "Be more specific with your queries"
      ];
      
    case ErrorType.LLM_CONTENT_FILTER:
      return [
        "Rephrase your request without using prohibited content",
        "Check our content policy for guidelines"
      ];
      
    default:
      if (error.retryable) {
        return ["Try again", "Refresh the page", "Contact support if the problem persists"];
      } else {
        return ["Contact support if the problem persists"];
      }
  }
}

// Helper for wrapping Next.js API route handlers
export function withFallbacks(
  handler: (_req: NextRequest) => Promise<NextResponse>,
  options?: ApiFallbackOptions
) {
  const wrappedHandler: ApiHandlerWithFallback = async (req) => {
    try {
      return await handler(req);
    } catch (_error) {
      throw error; // Let the middleware handle it
    }
  };

  return withApiFallbacks(wrappedHandler, options);
}
