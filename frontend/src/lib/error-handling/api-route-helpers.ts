/**
 * API Route Helpers for AG-UI Error Handling
 * 
 * This file provides utility functions to easily incorporate error handling
 * into API routes while maintaining correct type safety.
 */

import { NextRequest, NextResponse } from 'next/server';
import { performanceMonitor } from '../performance/metrics';
import { withFallbacks } from './api-fallback-middleware';
import { errorHandler } from './error-handler';
import { ErrorType } from './error-types';
import ConnectionMonitor from './offline-detection';

/**
 * Creates an API route handler with built-in error handling, performance tracking,
 * and offline detection.
 */
export function createAPIHandler<T = any>(
  handler: (req: NextRequest, requestId: string) => Promise<NextResponse<T>>,
  options?: {
    cacheFallback?: boolean;
    retry?: boolean;
    transformErrors?: boolean;
  }
) {
  return withFallbacks(async (req: NextRequest) => {
    // Generate request ID for performance tracking
    const requestId = `api_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
    
    // Start tracking performance
    performanceMonitor.startRequest(requestId);
    
    // Check connection status
    const connectionMonitor = ConnectionMonitor.getInstance();
    if (!connectionMonitor.isOnline()) {
      throw errorHandler.handleError(new Error('Network offline'), {
        type: ErrorType.NETWORK_OFFLINE,
        status: 503
      });
    }

    try {
      // Call the handler with the request ID
      const response = await handler(req, requestId);
      
      // Complete performance tracking on success
      performanceMonitor.completeRequest(requestId, {
        cacheHit: false,
        tokenCount: 0
      });
      
      return response;
    } catch (_error) {
      // Complete performance tracking on failure
      performanceMonitor.completeRequest(requestId, {
        cacheHit: false,
        tokenCount: 0
      });
      
      // Let the error handler deal with it
      throw error;
    }
  }, options);
}

/**
 * Wraps an OPTIONS handler with minimal error handling
 */
export function createOptionsHandler(
  headers: Record<string, string> = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    'Access-Control-Max-Age': '86400'
  }
) {
  return withFallbacks(async () => {
    return NextResponse.json(null, { 
      status: 200,
      headers
    });
  }, {
    cacheFallback: false,
    retry: false,
    transformErrors: false
  });
}
