/**
 * AG-UI Error Handler
 * Core error handling utilities for graceful fallbacks and error reporting
 */

import {
  ErrorType,
  ErrorSeverity,
  AGError as IAGError,
  RetryPolicy,
  mapStatusCodeToErrorType,
  isNetworkError,
  isAuthError,
  getRetryPolicy
} from './error-types';
import { AGError } from './error-classes';
import ConnectionMonitor from './offline-detection';

// Import optional error reporter - will be initialized separately
let errorReporter: any;
try {
  // Import dynamically to avoid circular dependencies
  import('@/lib/monitoring/error-reporter').then(module => {
    errorReporter = module.ErrorReporter.getInstance();
  }).catch(error => {
    console.warn('Error reporter not available:', error);
  });
} catch (_error) {
  // Error reporter is optional, so we can continue without it
  console.warn('Error reporter not available');
}

// Singleton for centralized error handling
export class ErrorHandler {
  private static instance: ErrorHandler;
  private connectionMonitor: ConnectionMonitor;
  private errorListeners: Array<(error: AGError) => void> = [];
  private retryAttempts: Map<string, number> = new Map();
  
  // Error reporting config
  private reportingConfig = {
    enabled: false,
    endpoint: '',
    samplingRate: 1.0,
    useErrorReporter: true // Use the error reporter if available
  };
  
  private constructor() {
    this.connectionMonitor = ConnectionMonitor.getInstance();
  }
  
  // Get singleton instance
  public static getInstance(): ErrorHandler {
    if (!ErrorHandler.instance) {
      ErrorHandler.instance = new ErrorHandler();
    }
    return ErrorHandler.instance;
  }
  
  // Subscribe to error events
  public subscribe(callback: (error: AGError) => void): () => void {
    this.errorListeners.push(callback);
    return () => {
      this.errorListeners = this.errorListeners.filter(listener => listener !== callback);
    };
  }

  // Configure error reporting
  public configureReporting(config: {
    enabled: boolean;
    endpoint?: string;
    samplingRate?: number;
    useErrorReporter?: boolean;
  }): void {
    this.reportingConfig = {
      ...this.reportingConfig,
      ...config
    };
  }
  
  // Main error handling method
  public handleError(
    error: Error | unknown,
    context?: Record<string, any>
  ): AGError {
    // Normalize to AGError format
    const normalizedError = this.normalizeError(error, context);
    
    // Notify listeners
    this.notifyErrorListeners(normalizedError);
    
    // Report error if configured
    if (this.shouldReportError(normalizedError)) {
      this.reportError(normalizedError);
    }
    
    return normalizedError;
  }
  
  // Attempt to retry an operation that failed
  public async retryWithBackoff<T>(
    operation: () => Promise<T>,
    errorKey: string,
    customRetryPolicy?: RetryPolicy
  ): Promise<T> {
    try {
      return await operation();
    } catch (_error) {
      const normalizedError = this.normalizeError(error);
      const retryPolicy = customRetryPolicy || getRetryPolicy(normalizedError.type);
      
      // If not retryable or no policy, just throw
      if (!normalizedError.retryable || !retryPolicy) {
        throw normalizedError;
      }
      
      // Get current attempt count
      const attemptCount = this.retryAttempts.get(errorKey) || 0;
      this.retryAttempts.set(errorKey, attemptCount + 1);
      
      // Check if we've exceeded max attempts
      if (attemptCount >= retryPolicy.maxAttempts) {
        // Clean up and throw
        this.retryAttempts.delete(errorKey);
        throw normalizedError;
      }
      
      // Calculate backoff delay
      const delay = Math.min(
        retryPolicy.baseDelay * Math.pow(retryPolicy.backoffFactor, attemptCount),
        retryPolicy.maxDelay
      );
      
      // Wait for backoff period
      await new Promise(resolve => setTimeout(resolve, delay));
      
      // Retry the operation
      return this.retryWithBackoff(operation, errorKey, customRetryPolicy);
    }
  }
  
  // Check if we should report this error
  private shouldReportError(error: AGError): boolean {
    if (!this.reportingConfig.enabled) return false;
    
    // Random sampling based on configured rate
    return Math.random() <= this.reportingConfig.samplingRate;
  }
  
  // Report error to monitoring system
  private reportError(error: AGError): void {
    // Use the error reporter if available and enabled
    if (this.reportingConfig.useErrorReporter && errorReporter) {
      try {
        errorReporter.reportError(error);
        return; // Don't continue to legacy reporting if reporter handled it
      } catch (_e) {
        // Silently ignore and fall back to legacy reporting
      }
    }
    
    // Legacy direct endpoint reporting as fallback
    if (!this.reportingConfig.endpoint) return;
    
    // Fire and forget error report
    try {
      fetch(this.reportingConfig.endpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          error: {
            type: error.type,
            message: error.message,
            severity: error.severity,
            statusCode: error.statusCode,
            timestamp: error.timestamp,
            context: error.context
          }
        }),
        // Don't wait for response and don't block main thread
        keepalive: true
      }).catch(() => {
        // Intentionally ignore errors when reporting errors
        // to avoid infinite loops
      });
    } catch (_e) {
      // Silently ignore - we don't want error reporting to cause more errors
    }
  }
  
  // Notify all error listeners
  private notifyErrorListeners(error: AGError): void {
    this.errorListeners.forEach(listener => {
      try {
        listener(error);
      } catch (_e) {
        // Prevent listener errors from blocking others
        console.error('Error in error listener', e);
      }
    });
  }
  
  // Normalize any error into AGError format
  private normalizeError(
    error: Error | unknown,
    context?: Record<string, any>
  ): AGError {
    if (error instanceof AGError) {
      // Already an AGError, just ensure timestamp is present
      if (!error.timestamp) {
        error.timestamp = Date.now();
      }
      if (context) {
        error.context = { ...error.context, ...context };
      }
      return error;
    }

    // Check if response object
    if (this.isResponse(error)) {
      const errorType = mapStatusCodeToErrorType(error.status);
      const agError = new AGError(
        `HTTP Error: ${error.status} ${error.statusText || ''}`,
        errorType,
        this.getSeverityFromErrorType(errorType),
        !!getRetryPolicy(errorType)
      );
      agError.statusCode = error.status;
      agError.context = context;
      return agError;
    }
    
    // Handle standard Error objects
    if (error instanceof Error) {
      let errorType = ErrorType.UNKNOWN;

      // Classify error
      if (!this.connectionMonitor.isOnline() || isNetworkError(error)) {
        errorType = ErrorType.NETWORK_OFFLINE;
      } else if (isAuthError(error)) {
        errorType = ErrorType.AUTH_INVALID;
      }

      const agError = new AGError(
        error.message,
        errorType,
        this.getSeverityFromErrorType(errorType),
        !!getRetryPolicy(errorType)
      );
      agError.originalError = error;
      agError.context = context;
      return agError;
    }

    // Handle other types
    const agError = new AGError(
      String(error),
      ErrorType.UNKNOWN,
      ErrorSeverity.ERROR,
      false
    );
    agError.originalError = error;
    agError.context = context;
    return agError;
  }
  
  // Check if object is a Response
  private isResponse(obj: any): obj is Response {
    return obj && typeof obj.status === 'number';
  }
  
  // Check if object is an AGError
  private isAGError(obj: any): obj is AGError {
    return obj instanceof AGError;
  }
  
  // Map error type to severity
  private getSeverityFromErrorType(errorType: ErrorType): ErrorSeverity {
    switch (errorType) {
      case ErrorType.NETWORK_OFFLINE:
      case ErrorType.NETWORK_TIMEOUT:
      case ErrorType.SERVER_OVERLOAD:
      case ErrorType.SERVICE_UNAVAILABLE:
        return ErrorSeverity.WARNING;
        
      case ErrorType.AUTH_EXPIRED:
      case ErrorType.AUTH_INVALID:
      case ErrorType.AUTH_MISSING:
      case ErrorType.ACCESS_DENIED:
        return ErrorSeverity.ERROR;
        
      case ErrorType.LLM_GUARDRAIL:
      case ErrorType.LLM_CONTENT_FILTER:
        return ErrorSeverity.INFO;
        
      default:
        return ErrorSeverity.ERROR;
    }
  }
  
  // Clear retry attempts for an operation
  public clearRetryAttempts(errorKey: string): void {
    this.retryAttempts.delete(errorKey);
  }
}

// Export singleton instance
export const errorHandler = ErrorHandler.getInstance();

// React hook to use error handler
export function useErrorHandler() {
  return {
    handleError: (error: Error | unknown, context?: Record<string, any>) => {
      return errorHandler.handleError(error, context);
    },
    
    retryWithBackoff: <T>(
      operation: () => Promise<T>,
      errorKey: string,
      customRetryPolicy?: RetryPolicy
    ) => {
      return errorHandler.retryWithBackoff(operation, errorKey, customRetryPolicy);
    },
    
    subscribe: (callback: (error: AGError) => void) => {
      return errorHandler.subscribe(callback);
    }
  };
}
