/**
 * Local Cache Fallback System for AG-UI Integration
 * Extends the existing cache to serve as fallback when API is unavailable
 */

import { useConnectionStatus } from './use-connection-status';
import { ErrorType } from './error-types';
import { useEffect, useState } from 'react';

// Cache entry with metadata
export interface CacheEntry<T> {
  data: T;
  timestamp: number;
  source: 'memory' | 'local' | 'remote';
  expires?: number;
  version?: string;
}

// Configuration for fallback cache
export interface FallbackCacheConfig {
  namespace: string;
  localStorageKey?: string;
  maxAge?: number; // max age in ms
  version?: string; // for cache invalidation
  maxEntries?: number; // max entries to store
}

/**
 * Fallback cache that works in memory and localStorage
 * Acts as a fallback data source when online services are unavailable
 */
export class FallbackCache<T = any> {
  private memoryCache: Map<string, CacheEntry<T>> = new Map();
  private config: Required<FallbackCacheConfig>;
  
  constructor(config: FallbackCacheConfig) {
    // Set defaults for optional config
    this.config = {
      namespace: config.namespace,
      localStorageKey: config.localStorageKey || `agui_fallback_${config.namespace}`,
      maxAge: config.maxAge || 24 * 60 * 60 * 1000, // 24 hours
      version: config.version || '1.0',
      maxEntries: config.maxEntries || 100
    };
    
    // Initialize from localStorage if available
    if (typeof window !== 'undefined') {
      this.loadFromLocalStorage();
    }
  }
  
  /**
   * Get an item from cache with fallback support
   */
  public async get(key: string): Promise<CacheEntry<T> | null> {
    // Try memory cache first (fastest)
    const memoryItem = this.memoryCache.get(key);
    if (memoryItem && this.isValid(memoryItem)) {
      return { ...memoryItem, source: 'memory' };
    }
    
    // Try localStorage if available
    if (typeof window !== 'undefined') {
      const localItem = this.getFromLocalStorage(key);
      if (localItem && this.isValid(localItem)) {
        // Update memory cache
        this.memoryCache.set(key, localItem);
        return { ...localItem, source: 'local' };
      }
    }
    
    // No valid cache found
    return null;
  }
  
  /**
   * Set an item in the cache
   */
  public set(key: string, data: T, options?: { expires?: number }): void {
    const entry: CacheEntry<T> = {
      data,
      timestamp: Date.now(),
      source: 'memory',
      expires: options?.expires || (Date.now() + this.config.maxAge),
      version: this.config.version
    };
    
    // Update memory cache
    this.memoryCache.set(key, entry);
    
    // Enforce max entries limit
    if (this.memoryCache.size > this.config.maxEntries) {
      // Remove oldest entry
      const oldestKey = this.findOldestEntry();
      if (oldestKey) {
        this.memoryCache.delete(oldestKey);
      }
    }
    
    // Update localStorage if available
    if (typeof window !== 'undefined') {
      this.saveToLocalStorage();
    }
  }
  
  /**
   * Remove an item from cache
   */
  public remove(key: string): void {
    this.memoryCache.delete(key);
    
    // Update localStorage if available
    if (typeof window !== 'undefined') {
      this.saveToLocalStorage();
    }
  }
  
  /**
   * Clear all cache entries
   */
  public clear(): void {
    this.memoryCache.clear();
    
    // Clear localStorage if available
    if (typeof window !== 'undefined') {
      localStorage.removeItem(this.config.localStorageKey);
    }
  }
  
  /**
   * Check if a cache entry is still valid
   */
  private isValid(entry: CacheEntry<T>): boolean {
    // Version check
    if (entry.version !== this.config.version) {
      return false;
    }
    
    // Expiration check
    if (entry.expires && entry.expires < Date.now()) {
      return false;
    }
    
    return true;
  }
  
  /**
   * Find the oldest entry in the cache
   */
  private findOldestEntry(): string | undefined {
    let oldestKey: string | undefined;
    let oldestTime = Infinity;
    
    for (const [key, entry] of this.memoryCache.entries()) {
      if (entry.timestamp < oldestTime) {
        oldestTime = entry.timestamp;
        oldestKey = key;
      }
    }
    
    return oldestKey;
  }
  
  /**
   * Load cache from localStorage
   */
  private loadFromLocalStorage(): void {
    try {
      const stored = localStorage.getItem(this.config.localStorageKey);
      
      if (stored) {
        const parsed = JSON.parse(stored);
        
        // Check if the stored cache matches our expected format
        if (parsed && typeof parsed === 'object') {
          Object.entries(parsed).forEach(([key, value]) => {
            const entry = value as CacheEntry<T>;
            if (this.isValid(entry)) {
              this.memoryCache.set(key, entry);
            }
          });
        }
      }
    } catch (_e) {
      console.warn('Failed to load fallback cache from localStorage', e);
      // If localStorage fails, just continue with empty cache
    }
  }
  
  /**
   * Get a single item from localStorage
   */
  private getFromLocalStorage(key: string): CacheEntry<T> | null {
    try {
      const stored = localStorage.getItem(this.config.localStorageKey);
      
      if (stored) {
        const parsed = JSON.parse(stored);
        
        if (parsed && parsed[key]) {
          return parsed[key] as CacheEntry<T>;
        }
      }
    } catch (_e) {
      console.warn(`Failed to get item ${key} from localStorage`, e);
    }
    
    return null;
  }
  
  /**
   * Save entire cache to localStorage
   */
  private saveToLocalStorage(): void {
    try {
      // Convert Map to plain object
      const cacheObj: Record<string, CacheEntry<T>> = {};
      
      this.memoryCache.forEach((value, key) => {
        cacheObj[key] = value;
      });
      
      localStorage.setItem(
        this.config.localStorageKey, 
        JSON.stringify(cacheObj)
      );
    } catch (_e) {
      console.warn('Failed to save fallback cache to localStorage', e);
      // If localStorage fails, memory cache still works
    }
  }
}

/**
 * React hook for fallback cache with connection awareness
 */
export function useFallbackCache<T>(
  config: FallbackCacheConfig
): {
  get: (key: string) => Promise<T | null>;
  set: (key: string, data: T, options?: { expires?: number }) => void;
  remove: (key: string) => void;
  clear: () => void;
  isOffline: boolean;
} {
  // Create a ref to the cache instance
  const [cache] = useState(() => new FallbackCache<T>(config));
  const connectionStatus = useConnectionStatus();
  const isOffline = connectionStatus !== 'online';
  
  // For debugging
  useEffect(() => {
    if (process.env.NODE_ENV === 'development') {
      if (isOffline) {
        console.log(`[FallbackCache:${config.namespace}] Operating in offline mode`);
      }
    }
  }, [isOffline, config.namespace]);

  return {
    get: async (key: string) => {
      const result = await cache.get(key);
      return result ? result.data : null;
    },
    set: (key: string, data: T, options?: { expires?: number }) => {
      cache.set(key, data, options);
    },
    remove: (key: string) => {
      cache.remove(key);
    },
    clear: () => {
      cache.clear();
    },
    isOffline
  };
}

/**
 * Create a hash key from a request
 */
export function createCacheKey(request: Request | string, params?: Record<string, any>): string {
  const url = typeof request === 'string' ? request : request.url;
  const method = typeof request === 'string' ? 'GET' : request.method;
  
  const keyParts = [method, url];
  
  // Add params if provided
  if (params) {
    keyParts.push(JSON.stringify(params));
  }
  
  // For POST requests, try to include body
  if (method === 'POST' && typeof request !== 'string') {
    try {
      // Clone request to avoid consuming the body
      const clonedReq = request.clone();
      
      // Try to read and hash the body
      clonedReq.text().then(body => {
        if (body) {
          keyParts.push(body);
        }
      }).catch(() => {
        // Ignore errors reading body
      });
    } catch (_e) {
      // Ignore errors, just create key without body
    }
  }
  
  // Create a simple hash
  return btoa(keyParts.join('|')).replace(/[+/=]/g, '');
}

/**
 * Wrapper for fetch that uses cache for fallbacks
 */
export async function fetchWithFallback<T = any>(
  input: RequestInfo,
  init?: RequestInit,
  cacheNamespace: string = 'fetch',
  cacheOptions?: { 
    maxAge?: number;
    alwaysCache?: boolean;
    skipCache?: boolean;
  }
): Promise<T> {
  const cache = new FallbackCache<T>({
    namespace: cacheNamespace,
    maxAge: cacheOptions?.maxAge,
  });
  
  // Create cache key from request
  const cacheKey = createCacheKey(
    typeof input === 'string' ? input : input.url
  );
  
  // Skip cache if requested
  if (!cacheOptions?.skipCache) {
    // Try to get from cache first
    const cachedData = await cache.get(cacheKey);
    if (cachedData) {
      return cachedData.data;
    }
  }
  
  try {
    // Make the actual request
    const response = await fetch(input, init);
    
    if (!response.ok) {
      throw new Error(`HTTP error! Status: ${response.status}`);
    }
    
    // Parse the response
    const data = await response.json() as T;
    
    // Cache the successful response if configured
    if (cacheOptions?.alwaysCache !== false) {
      cache.set(cacheKey, data);
    }
    
    return data;
  } catch (_error) {
    // On error, try fallback from cache again
    // (in case we skipped cache initially but now need it)
    const cachedData = await cache.get(cacheKey);
    if (cachedData) {
      return cachedData.data;
    }
    
    // No cache available, rethrow
    throw error;
  }
}
