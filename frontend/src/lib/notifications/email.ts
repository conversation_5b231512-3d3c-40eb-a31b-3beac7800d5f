/**
 * Email notification service using Resend
 */

import { createClient } from '@supabase/supabase-js';

interface EmailOptions {
  to: string;
  subject: string;
  html: string;
  text?: string;
  from?: string;
}

/**
 * Send an email using Resend
 * @param options Email options
 * @returns Success status
 */
export async function sendEmail(options: EmailOptions): Promise<boolean> {
  try {
    // In a real implementation, this would use the Resend API
    // For now, we'll just log the email and return success
    console.log('Sending email:', options);

    // Log the email in the security events table
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_KEY!
    );

    await supabase
      .schema('security')
      .from('events')
      .insert({
        event_type: 'system.email_sent',
        event_category: 'system',
        details: {
          to: options.to,
          subject: options.subject,
          provider: 'Resend'
        },
        created_at: new Date().toISOString()
      });

    return true;
  } catch (_error) {
    console.error('Error sending email:', error);
    return false;
  }
}

/**
 * Send a security alert email
 * @param to Recipient email address
 * @param title Alert title
 * @param message Alert message
 * @param severity Alert severity
 * @returns Success status
 */
export async function sendSecurityAlertEmail(
  to: string,
  title: string,
  message: string,
  severity: 'low' | 'medium' | 'high' | 'critical'
): Promise<boolean> {
  const subject = `Security Alert: ${title}`;

  // Create HTML content with proper styling
  const html = `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>${subject}</title>
      <style>
        body {
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
          line-height: 1.5;
          color: #333;
          padding: 20px;
          margin: 0;
        }
        .container {
          max-width: 600px;
          margin: 0 auto;
          border: 1px solid #e0e0e0;
          border-radius: 5px;
          overflow: hidden;
        }
        .header {
          background-color: ${
            severity === 'critical' ? '#ef4444' :
            severity === 'high' ? '#f97316' :
            severity === 'medium' ? '#eab308' :
            '#3b82f6'
          };
          color: white;
          padding: 20px;
          text-align: center;
        }
        .content {
          padding: 20px;
          background-color: #fff;
        }
        .footer {
          background-color: #f9fafb;
          padding: 15px 20px;
          text-align: center;
          font-size: 12px;
          color: #6b7280;
        }
        .button {
          display: inline-block;
          background-color: #3b82f6;
          color: white;
          text-decoration: none;
          padding: 10px 20px;
          border-radius: 5px;
          margin-top: 20px;
        }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="header">
          <h1 style="margin: 0; font-size: 24px;">${subject}</h1>
        </div>
        <div class="content">
          <p>We detected a security concern that requires your attention:</p>
          <p><strong>${message}</strong></p>
          <p>If you did not perform this action, please secure your account immediately by changing your password and enabling two-factor authentication.</p>
          <p>
            <a href="https://example.com/security" class="button">Review Security Settings</a>
          </p>
        </div>
        <div class="footer">
          <p>This is an automated security alert from PI Lawyer AI. Please do not reply to this email.</p>
          <p>If you need assistance, please contact our support team.</p>
        </div>
      </div>
    </body>
    </html>
  `;

  // Create plain text version
  const text = `
    Security Alert: ${title}

    We detected a security concern that requires your attention:

    ${message}

    If you did not perform this action, please secure your account immediately by changing your password and enabling two-factor authentication.

    Review your security settings at: https://example.com/security

    This is an automated security alert from PI Lawyer AI. Please do not reply to this email.
    If you need assistance, please contact our support team.
  `;

  return sendEmail({
    to,
    subject,
    html,
    text,
    from: '<EMAIL>'
  });
}
