/**
 * SMS notification service using Twilio
 */

import { createClient } from '@supabase/supabase-js';

interface SmsOptions {
  to: string;
  message: string;
  from?: string;
}

/**
 * Send an SMS using Twilio
 * @param options SMS options
 * @returns Success status
 */
export async function sendSms(options: SmsOptions): Promise<boolean> {
  try {
    // In a real implementation, this would use the Twilio API
    // For now, we'll just log the SMS and return success
    console.log('Sending SMS:', options);

    // Log the SMS in the security events table
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_KEY!
    );

    await supabase
      .schema('security')
      .from('events')
      .insert({
        event_type: 'system.sms_sent',
        event_category: 'system',
        details: {
          to: options.to,
          provider: 'Twilio'
        },
        created_at: new Date().toISOString()
      });

    return true;
  } catch (_error) {
    console.error('Error sending SMS:', error);
    return false;
  }
}

/**
 * Send a security alert SMS
 * @param to Recipient phone number
 * @param title Alert title
 * @param message Alert message
 * @param severity Alert severity
 * @returns Success status
 */
export async function sendSecurityAlertSms(
  to: string,
  title: string,
  message: string,
  severity: 'low' | 'medium' | 'high' | 'critical'
): Promise<boolean> {
  // Create a concise SMS message
  const smsMessage = `
    PI Lawyer AI Security Alert (${severity.toUpperCase()}): ${title}

    ${message}

    If this wasn't you, please secure your account immediately.
  `.trim();

  return sendSms({
    to,
    message: smsMessage,
    from: 'PI Lawyer AI'
  });
}
