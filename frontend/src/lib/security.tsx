'use client'

import { createContext, useContext, useState, useEffect, ReactNode } from 'react'
import { useSupabase } from './supabase/provider'
import { useSession } from '@/contexts/SessionContext'
import FingerprintJS from '@fingerprintjs/fingerprintjs'
import { useAuthenticatedFetch } from '@/hooks/useAuthenticatedFetch'

interface SecurityContextType {
  deviceFingerprint: string | null
  blockDevice: (fingerprint: string) => Promise<boolean>
  trustDevice: (fingerprint: string) => Promise<boolean>
}

interface FunctionExistsApiResponse {
  exists: boolean;
}

const SecurityContext = createContext<SecurityContextType>({
  deviceFingerprint: null,
  blockDevice: async () => false,
  trustDevice: async () => false,
})

export function SecurityProvider({ children }: { children: ReactNode }) {
  const { supabase } = useSupabase()
  const { session } = useSession()
  const { authedFetch, isReady } = useAuthenticatedFetch()
  const [deviceFingerprint, setDeviceFingerprint] = useState<string | null>(null)

  // Generate device fingerprint on component mount
  useEffect(() => {
    if (!session) return
    const generateFingerprint = async () => {
      try {
        // Load FingerprintJS
        const fp = await FingerprintJS.load()

        // Get visitor identifier
        const result = await fp.get()
        const visitorId = result.visitorId

        setDeviceFingerprint(visitorId)

        // For admin login page, we don't need to register the device
        // This prevents errors when the security schema or functions don't exist
        const isAdminPage = window.location.pathname.includes('/loginadmin') ||
                           window.location.pathname.includes('/admin')

        // Register device if user session exists and not on admin pages
        if (session.user && !isAdminPage) {
          // Register the device - wrapped in try/catch to prevent unhandled errors
          try {
            // Check if the function exists using our API
            if (!authedFetch || !isReady) {
              console.warn('SecurityProvider: authedFetch not ready for function check.');
              return; // Exit if fetch hook isn't ready
            }

            // Call authedFetch and handle potential thrown errors
            try {
              const functionCheckData = await authedFetch<FunctionExistsApiResponse>('/api/utils/function-exists?name=register_device');

              // Check the actual data returned on success
              if (!functionCheckData?.exists) {
                // Function doesn't exist, skip registration silently
                console.log('Device registration skipped: function not available');
                // No need to proceed further in this inner try block
              } else {
                // Function exists, try to register
                const { error } = await supabase.rpc('register_device', {
                  p_fingerprint: visitorId,
                  p_user_agent: navigator.userAgent,
                  p_ip_address: 'client-side' // IP will be captured server-side
                });

                if (error) {
                  // Log supabase.rpc error
                  console.log('Device registration via rpc skipped:', error.message);
                }
              }
            } catch (fetchError) {
               // Handle errors thrown by authedFetch (HTTP, network, etc.)
               console.error('Error checking function existence (authedFetch failed):', fetchError);
               // Decide if you need to return or stop further execution
            }

          } catch (_error) {
            // Catch any other unexpected errors from the outer try block
            console.log('Device registration process skipped due to unexpected error:', error);
          }
        }
      } catch (_error) {
        // Just log the error without throwing
        console.log('Fingerprint generation skipped:', error);
      }
    }

    generateFingerprint()
  }, [supabase, session, authedFetch, isReady])

  // Block a device
  const blockDevice = async (fingerprint: string): Promise<boolean> => {
    try {
      // Check if the fetch hook is ready
      if (!authedFetch || !isReady) {
        console.warn('SecurityProvider: authedFetch not ready for function check.');
        return false;
      }

      // Call authedFetch and handle potential thrown errors
      try {
         const functionCheckData = await authedFetch<FunctionExistsApiResponse>('/api/utils/function-exists?name=block_device');

         // Check the actual data returned on success
         if (!functionCheckData?.exists) {
           // Function doesn't exist, log and return
           console.log('Device blocking skipped: function not available');
           return false;
         }

         // Function exists, proceed to block
         const { error } = await supabase.rpc(
           'block_device',
           { p_fingerprint: fingerprint }
         )

         if (error) {
           console.log('Device blocking via rpc skipped:', error.message);
           return false;
         }

         return true; // Successfully blocked

      } catch(fetchError) {
          // Handle errors thrown by authedFetch (HTTP, network, etc.)
          console.error('Error checking/blocking device (authedFetch failed):', fetchError);
          return false; // Indicate failure
      }

    } catch (_error) {
      // Catch any other unexpected errors from the outer try block
      console.log('Error blocking device (unexpected), operation skipped:', error);
      return false
    }
  }

  // Trust a device
  const trustDevice = async (fingerprint: string): Promise<boolean> => {
    try {
      // Check if the fetch hook is ready
      if (!authedFetch || !isReady) {
        console.warn('SecurityProvider: authedFetch not ready for function check.');
        return false;
      }

      // Call authedFetch and handle potential thrown errors
      try {
        const functionCheckData = await authedFetch<FunctionExistsApiResponse>('/api/utils/function-exists?name=trust_device');

        // Check the actual data returned on success
        if (!functionCheckData?.exists) {
           // Function doesn't exist, log and return
           console.log('Device trusting skipped: function not available');
           return false;
        }

        // Function exists, proceed to trust
        const { error } = await supabase.rpc(
          'trust_device',
          { p_fingerprint: fingerprint }
        )

        if (error) {
          console.log('Device trusting via rpc skipped:', error.message);
          return false;
        }

        return true; // Successfully trusted

      } catch (fetchError) {
          // Handle errors thrown by authedFetch (HTTP, network, etc.)
          console.error('Error checking/trusting device (authedFetch failed):', fetchError);
          return false; // Indicate failure
      }

    } catch (_error) {
      // Catch any other unexpected errors from the outer try block
      console.log('Error trusting device (unexpected), operation skipped:', error);
      return false
    }
  }

  return (
    <SecurityContext.Provider value={{
      deviceFingerprint,
      blockDevice,
      trustDevice
    }}>
      {children}
    </SecurityContext.Provider>
  )
}

export const useSecurity = () => useContext(SecurityContext)
