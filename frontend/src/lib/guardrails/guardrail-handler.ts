/**
 * Guardrail Utilities for AG-UI Implementation
 * 
 * This file provides utilities for handling guardrail violations in the AG-UI implementation.
 * It includes error parsing, categorization, and UI message formatting.
 */

// Types for guardrail responses
export interface GuardrailViolation {
  type: 'illegal' | 'sexual' | 'harmful' | 'harassment' | 'unknown';
  message: string;
  severity: 'low' | 'medium' | 'high';
  category?: string;
  explanation?: string;
}

/**
 * Default user-friendly messages for each violation type
 */
const DEFAULT_MESSAGES = {
  illegal: 'Your request contains content that may violate legal regulations. Please rephrase your request.',
  sexual: 'Your request contains inappropriate content. Please rephrase your request to be more professional.',
  harmful: 'Your request contains potentially harmful content. For everyone\'s safety, please rephrase your request.',
  harassment: 'Your request contains content that could be perceived as harassment. Please rephrase your request.',
  unknown: 'Your request was blocked by our content safety system. Please rephrase your request.',
};

/**
 * Extract a user-friendly message from an error response
 * @param error The error response from the API
 * @returns A user-friendly message explaining the guardrail violation
 */
export function parseGuardrailError(error: any): GuardrailViolation {
  try {
    // Default violation object
    const violation: GuardrailViolation = {
      type: 'unknown',
      message: DEFAULT_MESSAGES.unknown,
      severity: 'medium',
    };

    // If it's already a GuardrailViolation, return it directly
    if (error && error.type && error.message && error.severity) {
      return error as GuardrailViolation;
    }

    // Extract error message if present
    let errorMessage = '';
    if (error instanceof Error) {
      errorMessage = error.message;
    } else if (typeof error === 'string') {
      errorMessage = error;
    } else if (error && typeof error === 'object') {
      // Try to extract message from various error formats
      errorMessage = error.message || error.error || error.detail || JSON.stringify(error);
    }

    // Check for specific error patterns
    const lowerCaseError = errorMessage.toLowerCase();

    // Determine violation type based on error message patterns
    if (lowerCaseError.includes('illegal') || lowerCaseError.includes('unlawful') || lowerCaseError.includes('legal')) {
      violation.type = 'illegal';
      violation.message = DEFAULT_MESSAGES.illegal;
    } else if (lowerCaseError.includes('sexual') || lowerCaseError.includes('explicit') || lowerCaseError.includes('adult')) {
      violation.type = 'sexual';
      violation.message = DEFAULT_MESSAGES.sexual;
    } else if (lowerCaseError.includes('harmful') || lowerCaseError.includes('damage') || lowerCaseError.includes('injury')) {
      violation.type = 'harmful';
      violation.message = DEFAULT_MESSAGES.harmful;
    } else if (lowerCaseError.includes('harassment') || lowerCaseError.includes('bully') || lowerCaseError.includes('offensive')) {
      violation.type = 'harassment';
      violation.message = DEFAULT_MESSAGES.harassment;
    }

    // Try to extract more details if available
    if (error && error.details) {
      violation.explanation = error.details;
    }

    // Try to extract category if available
    if (error && error.category) {
      violation.category = error.category;
    }

    return violation;
  } catch (_e) {
    // Fallback to generic message if parsing fails
    return {
      type: 'unknown',
      message: DEFAULT_MESSAGES.unknown,
      severity: 'medium',
    };
  }
}

/**
 * Format a guardrail violation for display in the UI
 * @param violation The guardrail violation details
 * @returns A formatted message for UI display with styling information
 */
export function formatViolationForUI(violation: GuardrailViolation): {
  title: string;
  message: string;
  icon: string;
  variant: 'destructive' | 'warning' | 'default';
} {
  // Determine title based on violation type
  let title = 'Content Policy Violation';
  let icon = 'ShieldAlert';
  let variant: 'destructive' | 'warning' | 'default' = 'warning';
  
  // Configure display based on violation type
  switch (violation.type) {
    case 'illegal':
      title = 'Legal Policy Violation';
      icon = 'Scale';
      variant = 'destructive';
      break;
    case 'sexual':
      title = 'Inappropriate Content';
      icon = 'AlertOctagon';
      variant = 'destructive';
      break;
    case 'harmful':
      title = 'Potentially Harmful Content';
      icon = 'AlertTriangle';
      variant = 'destructive';
      break;
    case 'harassment':
      title = 'Harmful Language Detected';
      icon = 'Ban';
      variant = 'warning';
      break;
    case 'unknown':
    default:
      title = 'Content Policy Violation';
      icon = 'ShieldAlert';
      variant = 'warning';
      break;
  }
  
  return {
    title,
    message: violation.message,
    icon,
    variant
  };
}

/**
 * Get the error message from a response object
 * @param error The error response
 * @returns A user-friendly error message
 */
export function getErrorMessageFromResponse(response: Response): Promise<string> {
  // Try to parse the response as JSON
  return response.text().then(text => {
    try {
      const data = JSON.parse(text);
      return data.message || data.error || data.detail || 'An error occurred';
    } catch (_e) {
      // If parsing fails, return the raw text or a default message
      return text || 'An unknown error occurred';
    }
  });
}

/**
 * Check if an error is likely a guardrail violation
 * @param error Any error object to check
 * @returns True if the error appears to be a guardrail violation
 */
export function isGuardrailViolation(error: any): boolean {
  if (!error) return false;
  
  // Check for specific status codes
  if (error.status === 400 || error.status === 403) {
    const errorMessage = error.message || error.error || '';
    const lowerCaseError = errorMessage.toLowerCase();
    
    // Check for guardrail-related keywords
    return (
      lowerCaseError.includes('policy') ||
      lowerCaseError.includes('guardrail') ||
      lowerCaseError.includes('content') ||
      lowerCaseError.includes('violation') ||
      lowerCaseError.includes('moderation') ||
      lowerCaseError.includes('inappropriate') ||
      lowerCaseError.includes('harmful') ||
      lowerCaseError.includes('illegal')
    );
  }
  
  return false;
}
