'use client';

/**
 * useGuardrails Hook
 *
 * This hook provides guardrail violation handling for CopilotKit chat components.
 * It integrates with the AG-UI implementation to provide consistent error handling
 * for content policy violations.
 */
import { useState, useCallback } from 'react';
import { GuardrailViolation, parseGuardrailError, isGuardrailViolation } from './guardrail-handler';

interface UseGuardrailsOptions {
  onViolation?: (violation: GuardrailViolation) => void;
  autoResetAfter?: number; // Time in ms to automatically reset violation state
}

interface UseGuardrailsReturn {
  violation: GuardrailViolation | null;
  handleError: (error: any) => boolean; // Returns true if handled as guardrail violation
  clearViolation: () => void;
  isGuardrailError: boolean;
}

/**
 * Hook for handling guardrail violations
 */
export function useGuardrails(options: UseGuardrailsOptions = {}): UseGuardrailsReturn {
  const { onViolation, autoResetAfter = 0 } = options;
  
  // State to track the current violation
  const [violation, setViolation] = useState<GuardrailViolation | null>(null);
  
  // Clear the current violation
  const clearViolation = useCallback(() => {
    setViolation(null);
  }, []);
  
  // Handle errors and detect guardrail violations
  const handleError = useCallback((error: any): boolean => {
    // Check if this is likely a guardrail violation
    if (!error || !isGuardrailViolation(error)) {
      return false; // Not a guardrail violation
    }
    
    // Parse the error into a guardrail violation
    const parsedViolation = parseGuardrailError(error);
    
    // Update state with the violation
    setViolation(parsedViolation);
    
    // Trigger callback if provided
    if (onViolation) {
      onViolation(parsedViolation);
    }
    
    // Set auto-reset timer if configured
    if (autoResetAfter > 0) {
      setTimeout(clearViolation, autoResetAfter);
    }
    
    return true; // Handled as a guardrail violation
  }, [onViolation, autoResetAfter, clearViolation]);
  
  return {
    violation,
    handleError,
    clearViolation,
    isGuardrailError: violation !== null
  };
}

/**
 * Higher-order function to wrap CopilotKit API calls with guardrail handling
 * @param apiCall The original API call function
 * @param handleError The error handler function from useGuardrails
 * @returns A wrapped function that handles guardrail violations
 */
export function withGuardrails<T extends (...args: any[]) => Promise<any>>(
  apiCall: T,
  handleError: (error: any) => boolean
): T {
  return (async (...args: Parameters<T>) => {
    try {
      return await apiCall(...args);
    } catch (_error) {
      // If this wasn't a guardrail violation, re-throw
      if (!handleError(error)) {
        throw error;
      }
      
      // Return a default value or rejection depending on context
      return Promise.reject(new Error('Content policy violation'));
    }
  }) as T;
}

/**
 * Helper to apply guardrails to an object with API methods
 * @param api The API object with methods to wrap
 * @param handleError The error handler function from useGuardrails
 * @returns A new API object with wrapped methods
 */
export function applyGuardrailsToAPI<T extends Record<string, (...args: any[]) => Promise<any>>>(
  api: T,
  handleError: (error: any) => boolean
): T {
  const wrappedApi = { ...api };
  
  for (const key in api) {
    if (typeof api[key] === 'function') {
      wrappedApi[key] = withGuardrails(api[key], handleError);
    }
  }
  
  return wrappedApi;
}
