import { AgentState, AgentConfig, Message } from "./types";
import { SMALL_TALK_PROMPT, PRE_INTAKE_PROMPT } from "./prompts";

export async function classifyInput(config: AgentConfig, state: AgentState): Promise<AgentState> {
  try {
    const classificationResponse = await config.classifierLLM(state.userInput);
    return {
      ...state,
      router: {
        type: classificationResponse.toLowerCase().includes("pre intake") ? "pre intake" : "small talk",
        logic: classificationResponse
      }
    };
  } catch (_error) {
    console.error("Error in classifyInput:", error);
    return {
      ...state,
      router: { type: "small talk", logic: "Error in classification, defaulting to small talk" }
    };
  }
}

export async function handleSmallTalk(config: AgentConfig, state: AgentState): Promise<AgentState> {
  try {
    const prompt = SMALL_TALK_PROMPT
      .replace("{state}", JSON.stringify(state.messages))
      .replace("{message}", state.userInput);
    const response = await config.responseLLM(prompt);
    return {
      ...state,
      messages: [...state.messages, { role: "ai" as const, content: response }]
    };
  } catch (_error) {
    console.error("Error in handleSmallTalk:", error);
    return {
      ...state,
      messages: [...state.messages, { role: "ai" as const, content: "I apologize, but I'm having trouble processing your request right now." }]
    };
  }
}

export async function handlePreIntake(config: AgentConfig, state: AgentState): Promise<AgentState> {
  try {
    const prompt = PRE_INTAKE_PROMPT
      .replace("{state}", JSON.stringify(state.messages))
      .replace("{message}", state.userInput);
    const response = await config.responseLLM(prompt);
    return {
      ...state,
      messages: [...state.messages, { role: "ai" as const, content: response }],
      preIntakeTriggered: true
    };
  } catch (_error) {
    console.error("Error in handlePreIntake:", error);
    return {
      ...state,
      messages: [...state.messages, { role: "ai" as const, content: "I apologize, but I'm having trouble processing your intake request right now." }],
      preIntakeTriggered: false
    };
  }
}
