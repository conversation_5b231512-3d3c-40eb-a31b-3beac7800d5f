import { StateGraph, END } from "@langchain/langgraph";
import { classifyInput, handleSmallTalk, handlePreIntake } from "./nodes";
import { config } from "./config";
import { HumanMessage, AIMessage, SystemMessage } from "@langchain/core/messages";
import { Role } from "@copilotkit/runtime-client-gql";

// Define our own ChatMessage type to avoid conflicts
interface ChatMessage {
  content: string | Record<string, unknown>;
  role?: string;
  name?: string;
  additional_kwargs?: Record<string, unknown>;
}

// Helper function to convert a CopilotKit message to a LangGraph message
function convertToLangGraphMessage(message: Record<string, unknown>): ChatMessage {
  console.log("Converting to LangGraph message:", JSON.stringify(message, null, 2));

  // Use textMessage property if available; otherwise fall back
  const textMessage = message.textMessage as Record<string, unknown> | undefined;
  const content = textMessage?.content as string || message.content as string;
  const role = textMessage?.role as string || message.role as string;

  console.log("Extracted content and role:", { content, role });

  let result;
  switch (role) {
    case Role.User:
    case "user":
      result = new HumanMessage(content);
      break;
    case Role.Assistant:
    case "assistant":
      result = new AIMessage(content);
      break;
    case Role.System:
    case "system":
      result = new SystemMessage(content);
      break;
    default:
      console.error(`Unknown message role: ${role}`, message);
      throw new Error(`Unknown message role: ${role}`);
  }

  console.log("Converted to LangGraph message:", result);
  // Cast to our ChatMessage type
  return {
    content: typeof result.content === 'string' ? result.content : JSON.stringify(result.content),
    role: result._getType ? result._getType() : undefined,
    additional_kwargs: {}
  } as ChatMessage;
}

// Helper function to convert a LangGraph message (or plain object) into a consistent CopilotKit message
function convertToCopilotMessage(msg: Record<string, unknown> | ChatMessage): Record<string, unknown> {
  console.log("Converting to CopilotKit message:", JSON.stringify(msg, null, 2));

  // If the message already has a textMessage property and required fields, return it
  if ('textMessage' in msg && 'id' in msg && 'createdAt' in msg) {
    console.log("Message already in CopilotKit format, returning as is");
    return msg as Record<string, unknown>;
  }

  const id = ('id' in msg) ? msg.id as string : `msg-${Date.now()}`;
  const createdAt = ('createdAt' in msg) ? msg.createdAt as string : new Date().toISOString();
  let role: string;
  let content: string = "";

  // Check if msg is an instance of known LangChain message types
  if (msg instanceof HumanMessage) {
    role = Role.User;
    content = msg.content as string;
  } else if (msg instanceof AIMessage) {
    role = Role.Assistant;
    content = msg.content as string;
  } else if (msg instanceof SystemMessage) {
    role = Role.System;
    content = msg.content as string;
  } else {
    // Handle plain object case
    role = ('role' in msg) ? msg.role as string : Role.User;
    content = ('content' in msg) ? msg.content as string : "";
  }

  const result = {
    id,
    createdAt,
    textMessage: {
      id: `text-${Date.now()}`,
      createdAt,
      content,
      role,
      __typename: "TextMessage"
    },
    __typename: "TextMessageOutput",
    status: {
      code: "SUCCESS",
      __typename: "SuccessMessageStatus"
    }
  };

  console.log("Converted to CopilotKit message:", JSON.stringify(result, null, 2));
  return result;
}

export interface IntakeAgentState {
  messages: ChatMessage[];
  userInput: string;
  router: {
    type: string;
    logic: string;
  };
  preIntakeTriggered: boolean;
  toolResults?: any;
}

export const createIntakeAgent = () => {
  // Define the state type
  interface AgentStateType {
    messages: ChatMessage[];
    userInput: string;
    router: { type: string; logic: string };
    preIntakeTriggered: boolean;
    toolResults: Record<string, unknown>;
  }

  // Create state graph with proper typing
  // @ts-expect-error - Ignoring type errors in StateGraph initialization due to API changes
  const builder: any = new StateGraph<AgentStateType>({
    channels: {
      // @ts-expect-error
      messages: { value: [] },
      // @ts-expect-error
      userInput: { value: "" },
      // @ts-expect-error
      router: { value: { type: "small talk", logic: "" } },
      // @ts-expect-error
      preIntakeTriggered: { value: false },
      // @ts-expect-error
      toolResults: { value: {} }
    }
  });

  // Add nodes
  builder.addNode("classify", {
    work: classifyInput.bind(null, config)
  });

  builder.addNode("small_talk", {
    work: handleSmallTalk.bind(null, config)
  });

  builder.addNode("pre_intake", {
    work: handlePreIntake.bind(null, config)
  });

  // Add conditional edges for routing based on classification
  // @ts-expect-error
  builder.addConditionalEdges(
    "classify",
    (state: any) => {
      return state.router.type === "small talk" ? "small_talk" : "pre_intake";
    },
    {
      "small_talk": "small_talk",
      "pre_intake": "pre_intake"
    }
  );

  // Add edges to end
  // @ts-expect-error
  builder.addEdge("small_talk", END);
  // @ts-expect-error
  builder.addEdge("pre_intake", END);

  // Set entry point
  // @ts-expect-error
  builder.setEntryPoint("classify");

  // Compile graph
  const graph = builder.compile();

  // Create a wrapper that handles state management and ensures outgoing messages have a consistent format
  return {
    invoke: async ({
      message,
      state = null
    }: {
      message: string;
      state: Record<string, unknown> | null;
    }) => {
      console.log("Agent invoke called with:", { message, state });

      // Convert incoming messages to LangGraph format
      const existingMessages = Array.isArray(state?.messages)
        ? (state.messages as Array<Record<string, unknown>>).map(msg => {
            try {
              return convertToLangGraphMessage(msg);
            } catch (_e) {
              console.warn("Error converting message:", e);
              return msg as unknown as ChatMessage;
            }
          })
        : [];

      console.log("Converted existing messages:", existingMessages);

      // Form the input state
      const input = {
        messages: existingMessages,
        userInput: message,
        router: (state?.router as { type: string; logic: string }) || { type: "small talk", logic: "" },
        preIntakeTriggered: (state?.preIntakeTriggered as boolean) || false,
        toolResults: (state?.toolResults as Record<string, unknown>) || {}
      };

      console.log("Running graph with input:", JSON.stringify(input, null, 2));

      // Run the graph
      const result = await graph.invoke(input);
      console.log("Graph result:", JSON.stringify(result, null, 2));

      // Convert every returned message into the consistent CopilotKit format
      const messages = Array.isArray(result.messages)
        ? result.messages.map((msg: ChatMessage | Record<string, unknown>) => convertToCopilotMessage(msg))
        : [];

      console.log("Final converted messages:", JSON.stringify(messages, null, 2));

      return {
        messages: messages,
        done: true,
        agentState: {
          ...result,
          messages: messages
        }
      };
    }
  };
};
