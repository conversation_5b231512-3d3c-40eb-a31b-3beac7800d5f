/**
 * Utility functions for Cypress testing
 */

/**
 * Check if the code is running in a Cypress test environment
 */
export function isCypressTest(): boolean {
  return typeof window !== 'undefined' && (window as any).Cypress !== undefined;
}

/**
 * Get the mock session from localStorage if in Cypress test environment
 */
export function getMockSession(): any | null {
  if (!isCypressTest()) return null;

  try {
    const authData = localStorage.getItem('supabase.auth.data');
    if (!authData) return null;

    const parsedData = JSON.parse(authData);
    return {
      access_token: parsedData.session.access_token,
      refresh_token: parsedData.session.refresh_token,
      expires_at: parsedData.session.expires_at,
      user: parsedData.user
    };
  } catch (_error) {
    console.error('Error getting mock session:', error);
    return null;
  }
}

/**
 * Get the subscription status from localStorage if in Cypress test environment
 */
export function getMockSubscriptionStatus(): string | null {
  if (!isCypressTest()) return null;

  try {
    return localStorage.getItem('cypress-test-subscription-status');
  } catch (_error) {
    console.error('Error getting mock subscription status:', error);
    return null;
  }
}
