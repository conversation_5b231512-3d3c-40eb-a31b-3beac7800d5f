import { SupabaseClient } from '@supabase/supabase-js';
import { CaseService } from '../services/case-service';
import { AuthUser } from '../types';
import { logActivityToNeo4j } from '../neo4j/client';
import { LLMService, LLMProvider } from '../llm/service';

/**
 * Service for bi-directional integration between case management and insights system
 */
export class CaseInsightsIntegration {
  private supabase: SupabaseClient;
  private caseService: CaseService;
  private llmService: LLMService;
  private user: AuthUser;

  constructor(
    supabase: SupabaseClient,
    tenantId: string,
    user: AuthUser,
    options: { defaultProvider?: LLMProvider } = {}
  ) {
    this.supabase = supabase;
    this.caseService = new CaseService(supabase, tenantId);
    this.llmService = new LLMService({
      // @ts-expect-error - LLM config property
      enableCaching: true
    });
    this.user = user;
  }

  /**
   * Generate case-specific insights and update case metadata
   */
  async enrichCaseWithInsights(caseId: string): Promise<{
    insights: any[];
    caseUpdated: boolean;
  }> {
    try {
      // Fetch case details
      const caseData = await this.caseService.getById(caseId);
      if (!caseData) {
        throw new Error(`Case not found: ${caseId}`);
      }

      // Get recent activities for this case
      const { data: activities } = await this.supabase
        .schema('tenants')
        .from('case_activities')
        .select('*')
        .eq('case_id', caseId)
        .order('created_at', { ascending: false })
        .limit(10);

      if (!activities || activities.length === 0) {
        return { insights: [], caseUpdated: false };
      }

      // Generate insights using LLM
      const prompt = `
        Analyze these recent activities for case "${caseData.title}" and identify:
        1. Priority tasks that should be addressed
        2. Potential risks or issues
        3. Suggested next actions

        Activities: ${JSON.stringify(activities)}
      `;

      const insightsResponse = await this.llmService.generateResponse(
        prompt,
        'You are an AI assistant that helps analyze legal case activities and identify important insights and action items.',
        { responseFormat: 'json' }
      );

      const insights = JSON.parse(insightsResponse);

      // Update case metadata with insights
      const metadata = {
        ...(caseData.metadata || {}),
        insights: {
          generated: new Date().toISOString(),
          data: insights,
        }
      };

      // Update the case with the new insights metadata
      await this.caseService.update(
        caseId,
        { metadata },
        this.user.id
      );

      // Log this integration activity
      await logActivityToNeo4j({
        userId: this.user.id,
        tenantId: this.user.tenantId,
        actionType: 'INSIGHTS_GENERATION',
        entityType: 'case',
        entityId: caseId,
        metadata: {
          source: 'case-insights-integration',
          insightCount: insights.length
        }
      });

      return {
        insights,
        caseUpdated: true
      };
    } catch (_error) {
      console.error('[CaseInsightsIntegration] Error enriching case:', error);
      throw error;
    }
  }

  /**
   * Apply user actions from insights to case management
   */
  async applyInsightAction(
    insightId: string,
    action: string,
    caseId: string,
    actionData: Record<string, any> = {}
  ): Promise<{
    success: boolean;
    result: any;
  }> {
    try {
      // Get case details
      const caseData = await this.caseService.getById(caseId);
      if (!caseData) {
        throw new Error(`Case not found: ${caseId}`);
      }

      let result: any = null;

      // Handle different action types
      switch (action) {
        case 'UPDATE_PRIORITY':
          // Update case metadata to reflect priority
          const updatedMetadata = {
            ...(caseData.metadata || {}),
            priority: actionData.priority,
            priorityReason: actionData.reason,
            priorityUpdatedAt: new Date().toISOString()
          };

          result = await this.caseService.update(
            caseId,
            { metadata: updatedMetadata },
            this.user.id
          );
          break;

        case 'ADD_TASK':
          // Create a task linked to this case
          const { data: task, error } = await this.supabase
            .schema('tenants')
            .from('tasks')
            .insert({
              title: actionData.taskTitle,
              description: actionData.taskDescription,
              due_date: actionData.dueDate,
              case_id: caseId,
              tenant_id: (caseData as any).tenant_id,
              created_by: this.user.id,
              status: 'pending'
            })
            .select()
            .single();

          if (error) throw error;
          result = task;
          break;

        case 'UPDATE_STATUS':
          // Update case status
          result = await this.caseService.update(
            caseId,
            {
              status: actionData.status,
              metadata: {
                ...(caseData.metadata || {}),
                statusChangeReason: actionData.reason,
                insightTriggered: true
              }
            },
            this.user.id
          );
          break;

        default:
          throw new Error(`Unsupported action type: ${action}`);
      }

      // Log the action to Neo4j
      await logActivityToNeo4j({
        userId: this.user.id,
        tenantId: this.user.tenantId,
        actionType: 'INSIGHT_ACTION_APPLIED',
        entityType: 'case',
        entityId: caseId,
        metadata: {
          insightId,
          actionType: action,
          result: result ? 'success' : 'no_change'
        }
      });

      return {
        success: true,
        result
      };
    } catch (_error) {
      console.error('[CaseInsightsIntegration] Error applying insight action:', error);
      throw error;
    }
  }

  /**
   * Suggest insights based on case status changes
   */
  async getSuggestionsForCaseStatusChange(
    caseId: string,
    oldStatus: string,
    newStatus: string
  ): Promise<{
    suggestions: string[];
    nextSteps: string[];
  }> {
    try {
      // Generate suggestions based on status change
      const prompt = `
        The case status has changed from "${oldStatus}" to "${newStatus}".
        Based on this change, provide:
        1. A list of 3-5 suggestions for the legal team
        2. 2-3 recommended next steps

        Return the results as a JSON object with two arrays: 'suggestions' and 'nextSteps'.
      `;

      const response = await this.llmService.generateResponse(
        prompt,
        'You are an AI assistant that helps lawyers manage their cases efficiently.',
        { responseFormat: 'json' }
      );

      const result = JSON.parse(response);

      // Log this integration point
      await logActivityToNeo4j({
        userId: this.user.id,
        tenantId: this.user.tenantId,
        actionType: 'CASE_STATUS_SUGGESTIONS',
        entityType: 'case',
        entityId: caseId,
        metadata: {
          oldStatus,
          newStatus,
          suggestionsCount: result.suggestions.length
        }
      });

      return result;
    } catch (_error) {
      console.error('[CaseInsightsIntegration] Error generating status change suggestions:', error);
      // Provide fallback suggestions
      return {
        suggestions: [
          `Consider updating related documents for the new ${newStatus} status`,
          `Review timeline and adjust as needed for ${newStatus} status`
        ],
        nextSteps: [
          `Update team members about the status change to ${newStatus}`,
          `Check if any deadlines need adjustment`
        ]
      };
    }
  }
}
