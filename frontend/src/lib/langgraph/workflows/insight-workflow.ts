import { StateGraph } from "@langchain/langgraph";
import { StateManager } from "../core/state";
import {
  InsightGraphState,
  InsightWorkflowConfig,
  InsightWorkflowResult,
  EntityType,
  InsightSource
} from "../types/insight-graph";
import { SupervisorAgent } from "../agents/supervisor";
import { SwarmAgentSystem, SwarmAgentType } from "../agents/swarm";
import { Activity, AuthUser } from "../../types";

/**
 * InsightWorkflow orchestrates the entire process of generating insights
 * using both Supervisor and Swarm agent patterns
 */
export class InsightWorkflow {
  private supervisorAgent: SupervisorAgent;
  private swarmSystem: SwarmAgentSystem;
  private config: InsightWorkflowConfig;
  private graph: any; // Using any due to StateGraph API changes
  private stateManager: StateManager<InsightGraphState>;

  /**
   * Helper method to generate relevant collaboration questions between agents
   */
  private generateCollaborationQuestion(fromAgent: string, toAgent: string, _state: InsightGraphState): string {
    // Define question templates based on agent combinations
    const questionTemplates: Record<string, Record<string, string>> = {
      ActivityAnalyzer: {
        CaseSpecialist: "What are the key legal implications of the recent activity patterns I've identified?",
        DocumentAnalyzer: "Which documents are most relevant to the recent user activities I've analyzed?",
        TimelineManager: "Based on activity patterns, what deadlines or timeline elements should we prioritize?",
        ActionRecommender: "What actions would you recommend based on the activity patterns I've identified?"
      },
      CaseSpecialist: {
        ActivityAnalyzer: "What recent user activities are most relevant to this case's legal strategy?",
        DocumentAnalyzer: "Which case-related documents require immediate attention based on recent changes?",
        TimelineManager: "What are the most critical upcoming deadlines for this case?",
        ActionRecommender: "What strategic actions would you recommend for this case's current status?"
      },
      DocumentAnalyzer: {
        ActivityAnalyzer: "What user activities relate to changes in this document?",
        CaseSpecialist: "How does this document impact the legal strategy for related cases?",
        TimelineManager: "Are there any timeline implications from the content of these documents?",
        ActionRecommender: "Based on document analysis, what actions should be prioritized?"
      },
      TimelineManager: {
        ActivityAnalyzer: "How do recent activities impact our timeline priorities?",
        CaseSpecialist: "What case developments might affect upcoming deadlines?",
        DocumentAnalyzer: "Are there any documents that need to be prepared for upcoming deadlines?",
        ActionRecommender: "What timeline-related actions should be recommended to the user?"
      },
      ActionRecommender: {
        ActivityAnalyzer: "Which user activities should inform our action recommendations?",
        CaseSpecialist: "What case-specific context is needed for my action recommendations?",
        DocumentAnalyzer: "Which documents are most relevant to include in action recommendations?",
        TimelineManager: "How should timeline constraints influence my action recommendations?"
      }
    };

    // Get default question if specific template not found
    return questionTemplates[fromAgent]?.[toAgent] ||
      `As a ${fromAgent}, I need your expertise as a ${toAgent}. What insights can you provide from your specialized perspective?`;
  }

  constructor(
    user: AuthUser,
    config: InsightWorkflowConfig = {}
  ) {
    // Set default configuration
    this.config = {
      maxAgents: config.maxAgents || 3,
      minInsightsRequired: config.minInsightsRequired || 1,
      maxInsightsToReturn: config.maxInsightsToReturn || 5,
      enableSupervisor: config.enableSupervisor !== false,
      enableSwarm: config.enableSwarm !== false,
      supervisorCriteria: config.supervisorCriteria || {
        minQualityScore: 7,
        requireActionability: true,
        checkForContradictions: true,
        enforceStyleGuide: true,
      },
      defaultProvider: config.defaultProvider || 'openai',
      modelOverrides: config.modelOverrides || {},
    };

    // Initialize supervisor agent
    this.supervisorAgent = new SupervisorAgent(
      {
        userId: user.id,
        tenantId: user.tenantId,
        timestamp: new Date().toISOString(),
        // We would typically fetch these from a database
        userPreferences: {},
        previousInsights: [],
        feedbackHistory: [],
      },
      { provider: this.config.defaultProvider }
    );

    // Initialize swarm agent system
    this.swarmSystem = new SwarmAgentSystem({
      provider: this.config.defaultProvider,
      modelName: this.config.defaultProvider && this.config.modelOverrides
        ? this.config.modelOverrides[this.config.defaultProvider]
        : undefined,
    });

    // Initialize state manager
    this.stateManager = new StateManager<InsightGraphState>({
      messages: [],
      current_step: 0,
      source: 'activity' as InsightSource,
      activeAgents: [],
      agentOutputs: {},
      generatedInsights: [],
      recommendedActions: [],
      errors: [],
      systemPerformance: {
        startTime: new Date().toISOString(),
        agentIterations: {},
      },
    });

    // Create and compile the workflow graph
    this.graph = this.buildWorkflowGraph();
  }


  /**
   * Build the workflow graph with nodes and edges
   *
   * Note: There are TypeScript errors in this method due to API changes in the StateGraph library.
   * We're using 'as any' in several places to work around these issues until we can update to the latest version.
   */
  private buildWorkflowGraph(): any {
    // Create StateGraph with simplified constructor to avoid type issues
    const builder = new StateGraph({} as any);

    // Add Supervisor nodes if enabled
    if (this.config.enableSupervisor) {
      builder.addNode("initialize_supervisor", {
        work: async (state: InsightGraphState) => {
          const stateManager = new StateManager<InsightGraphState>(state);
          await this.supervisorAgent.initializeWorkflow(stateManager);
          return stateManager.getState();
        }
      });

      // Add the agent collaboration node for enhanced agent interaction
      builder.addNode("agent_collaboration", {
        work: async (state: InsightGraphState) => {
          const stateManager = new StateManager<InsightGraphState>(state);

          // Only process collaboration if swarm system is enabled
          if (this.config.enableSwarm) {
            // Check if we have active agents who can collaborate
            const activeAgents = stateManager.getState().activeAgents || [];

            if (activeAgents.length > 1) {
              // Enhance context for all agents
              await this.swarmSystem.enhanceContextWithExternalKnowledge(stateManager);

              // Process any pending collaboration requests
              await this.swarmSystem.processCollaborationRequests(stateManager);

              // Generate collaboration requests between agents based on agent types
              for (let i = 0; i < activeAgents.length; i++) {
                const fromAgent = activeAgents[i];

                // Each agent can collaborate with up to 2 other agents
                const collaborationCount = Math.min(2, activeAgents.length - 1);

                for (let j = 0; j < collaborationCount; j++) {
                  // Select a different agent to collaborate with
                  const toAgentIndex = (i + j + 1) % activeAgents.length;
                  const toAgent = activeAgents[toAgentIndex];

                  if (fromAgent !== toAgent) {
                    // Generate a relevant question based on agent types
                    const question = this.generateCollaborationQuestion(fromAgent, toAgent, stateManager.getState());

                    // Send the collaboration request
                    await this.swarmSystem.requestAgentCollaboration(
                      stateManager,
                      fromAgent,
                      toAgent,
                      question
                    );
                  }
                }
              }
            }
          }

          return stateManager.getState();
        }
      });

      builder.addNode("supervisor_review", {
        work: async (state: InsightGraphState) => {
          const stateManager = new StateManager<InsightGraphState>(state);
          await this.supervisorAgent.reviewInsights(stateManager);
          return stateManager.getState();
        }
      });

      builder.addNode("supervisor_finalize", {
        work: async (state: InsightGraphState) => {
          const stateManager = new StateManager<InsightGraphState>(state);
          await this.supervisorAgent.finalizeWorkflow(stateManager);
          return stateManager.getState();
        }
      });
    }

    // Add Swarm nodes if enabled
    if (this.config.enableSwarm) {
      const agentTypes: SwarmAgentType[] = [
        "ActivityAnalyzer",
        "CaseSpecialist",
        "DocumentAnalyzer",
        "TimelineManager",
        "ActionRecommender"
      ];

      // Add a node for each agent type
      for (const agentType of agentTypes) {
        builder.addNode(`agent_${agentType}`, {
          work: async (state: InsightGraphState) => {
            const stateManager = new StateManager<InsightGraphState>(state);
            await this.swarmSystem.executeAgent(agentType, stateManager);
            return stateManager.getState();
          }
        });
      }

      // Add synthesis node
      builder.addNode("synthesize_insights", {
        work: async (state: InsightGraphState) => {
          const stateManager = new StateManager<InsightGraphState>(state);
          await this.swarmSystem.synthesizeInsights(stateManager);
          return stateManager.getState();
        }
      });
    }

    // Define edges for the workflow
    if (this.config.enableSupervisor && this.config.enableSwarm) {
      // Full workflow with both supervisor and swarm
      (builder as any).setEntryPoint("initialize_supervisor");

      // Add agent collaboration node after supervisor initialization
      (builder as any).addEdge("initialize_supervisor", "agent_collaboration");

      // Connect agent collaboration to agent nodes based on activeAgents
      (builder as any).addConditionalEdges(
        "agent_collaboration",
        (state: any) => {
          const activeAgents = state.activeAgents || [];
          if (activeAgents.length === 0) {
            return "synthesize_insights"; // Skip to synthesis if no agents selected
          }
          return activeAgents[0]; // Start with first active agent
        },
        {
          "ActivityAnalyzer": "agent_ActivityAnalyzer",
          "CaseSpecialist": "agent_CaseSpecialist",
          "DocumentAnalyzer": "agent_DocumentAnalyzer",
          "TimelineManager": "agent_TimelineManager",
          "ActionRecommender": "agent_ActionRecommender",
        }
      );

      // Connect agents in sequence
      const agentTypes: SwarmAgentType[] = [
        "ActivityAnalyzer",
        "CaseSpecialist",
        "DocumentAnalyzer",
        "TimelineManager",
        "ActionRecommender"
      ];

      // Chain agents together in sequence if they're active
      for (let i = 0; i < agentTypes.length - 1; i++) {
        (builder as any).addConditionalEdges(
          `agent_${agentTypes[i]}`,
          (state: any) => {
            const activeAgents = state.activeAgents || [];

            // Find the next active agent
            for (let j = i + 1; j < agentTypes.length; j++) {
              if (activeAgents.includes(agentTypes[j])) {
                return agentTypes[j];
              }
            }

            // If no more active agents, go to synthesis
            return "synthesis";
          },
          {
            "ActivityAnalyzer": "agent_ActivityAnalyzer",
            "CaseSpecialist": "agent_CaseSpecialist",
            "DocumentAnalyzer": "agent_DocumentAnalyzer",
            "TimelineManager": "agent_TimelineManager",
            "ActionRecommender": "agent_ActionRecommender",
            "synthesis": "synthesize_insights",
          }
        );
      }

      // Last agent goes to second collaboration phase
      (builder as any).addEdge("agent_ActionRecommender", "agent_collaboration");

      // Second collaboration phase goes to synthesis
      (builder as any).addEdge("agent_collaboration", "synthesize_insights");

      // Synthesis goes to supervisor review
      (builder as any).addEdge("synthesize_insights", "supervisor_review");

      // Review goes to finalization
      (builder as any).addEdge("supervisor_review", "supervisor_finalize");

      // Finalization ends the workflow
      (builder as any).addEdge("supervisor_finalize", "END");
    }
    else if (this.config.enableSupervisor) {
      // Supervisor-only workflow (minimal)
      (builder as any).setEntryPoint("initialize_supervisor");

      // Add agent collaboration node for context enrichment even without agents
      (builder as any).addEdge("initialize_supervisor", "agent_collaboration");
      (builder as any).addEdge("agent_collaboration", "supervisor_finalize");
      (builder as any).addEdge("supervisor_finalize", "END");
    }
    else if (this.config.enableSwarm) {
      // Swarm-only workflow
      (builder as any).setEntryPoint("agent_ActivityAnalyzer");

      // Connect agents in sequence
      (builder as any).addEdge("agent_ActivityAnalyzer", "agent_CaseSpecialist");
      (builder as any).addEdge("agent_CaseSpecialist", "agent_DocumentAnalyzer");
      (builder as any).addEdge("agent_DocumentAnalyzer", "agent_TimelineManager");
      (builder as any).addEdge("agent_TimelineManager", "agent_ActionRecommender");

      // Add collaboration before synthesis
      (builder as any).addEdge("agent_ActionRecommender", "agent_collaboration");
      (builder as any).addEdge("agent_collaboration", "synthesize_insights");
      (builder as any).addEdge("synthesize_insights", "END");
    }
    else {
      // Fallback to direct execution without agents
      (builder as any).addNode("direct_execution", {
        work: async (state: InsightGraphState) => {
          // Simple pass-through that just returns state
          return state;
        }
      });
      (builder as any).setEntryPoint("direct_execution");
      (builder as any).addEdge("direct_execution", "END");
    }

    // Cast to any to work around type issues with the StateGraph API
    return (builder as any).compile();
  }

  /**
   * Generate insights from activities
   */
  async generateInsightsFromActivities(
    activities: Activity[],
    options: {
      maxResults?: number;
    } = {}
  ): Promise<InsightWorkflowResult> {
    // Update state with activities
    this.stateManager.updateState({
      activities,
      source: 'activity',
      systemPerformance: {
        ...this.stateManager.getState().systemPerformance,
        startTime: new Date().toISOString(),
      },
    });

    return this.executeWorkflow(options);
  }

  /**
   * Generate insights for a specific entity (case, document, deadline)
   */
  async generateInsightsForEntity(
    entityType: EntityType,
    entityId: string,
    entityData: Record<string, unknown>,
    options: {
      activities?: Activity[];
      maxResults?: number;
    } = {}
  ): Promise<InsightWorkflowResult> {
    // Update state with entity information
    this.stateManager.updateState({
      activities: options.activities || [],
      relatedEntity: {
        type: entityType,
        id: entityId,
        ...entityData,
      },
      source: entityType as InsightSource,
      systemPerformance: {
        ...this.stateManager.getState().systemPerformance,
        startTime: new Date().toISOString(),
      },
    });

    return this.executeWorkflow(options);
  }

  /**
   * Execute the workflow and process results
   */
  private async executeWorkflow(
    options: {
      maxResults?: number;
    } = {}
  ): Promise<InsightWorkflowResult> {
    try {
      // Execute the workflow - using any here because the StateGraph API has changed
      // and we need to make it work with the current version
      const result = await (this.graph as any).invoke(this.stateManager.getState());

      // Extract insights and metadata
      const insights = result.generatedInsights || [];
      const maxResults = options.maxResults || this.config.maxInsightsToReturn || 5;

      // Sort insights by priority (highest first) and limit results
      const sortedInsights = [...insights].sort((a, b) => b.priority - a.priority).slice(0, maxResults);

      // Calculate performance metrics
      const startTime = new Date(result.systemPerformance.startTime);
      const endTime = result.systemPerformance.endTime
        ? new Date(result.systemPerformance.endTime)
        : new Date();
      const completionTime = endTime.getTime() - startTime.getTime();

      // Format and return the workflow result
      return {
        insights: sortedInsights,
        recommendedActions: result.recommendedActions || [],
        supervisorFeedback: result.supervisorFeedback,
        performance: {
          startTime: startTime.toISOString(),
          endTime: endTime.toISOString(),
          completionTime,
          tokensUsed: 0, // We don't track tokens in this implementation
          agentCounts: result.systemPerformance.agentIterations || {},
        },
        errors: result.errors || [],
      };
    } catch (_error) {
      console.error("Error executing insight workflow:", error);
      return {
        insights: [],
        recommendedActions: [],
        performance: {
          startTime: new Date().toISOString(),
          endTime: new Date().toISOString(),
          completionTime: 0,
          agentCounts: {},
        },
        errors: [error instanceof Error ? error.message : String(error)],
      };
    }
  }
}
