/**
 * LangGraph Tool Wrapper for Legal Search
 * 
 * This module provides LangGraph-compatible tools that wrap the laws-API
 * for use in agent workflows and CopilotKit integrations.
 */

import { createLawsApiClient } from '@/lib/laws-api/client';
import {
  SearchRequest,
  SearchResult,
  RecommendRequest,
  RecommendationResult,
  GraphRequest,
  GraphResult,
  DocumentType,
  PracticeArea,
  EntityType,
  RelationshipType,
  SortBy
} from '@/types/laws-api';

// Tool input/output schemas for LangGraph
export interface LegalSearchToolInput {
  query: string;
  jurisdiction?: string[];
  document_type?: DocumentType[];
  practice_area?: PracticeArea[];
  limit?: number;
  context?: string;
  // New v1 parameters
  practice_areas?: PracticeArea[];
  date_start?: string;
  date_end?: string;
  sort_by?: SortBy;
  authority_min?: number;
}

export interface LegalSearchToolOutput {
  results: SearchResult[];
  total_results: number;
  query_time_ms: number;
  success: boolean;
  error?: string;
}

export interface LegalRecommendToolInput {
  document_id?: string;
  content?: string;
  context?: string;
  jurisdiction?: string[];
  limit?: number;
  similarity_threshold?: number;
}

export interface LegalRecommendToolOutput {
  recommendations: RecommendationResult[];
  success: boolean;
  error?: string;
}

export interface LegalGraphToolInput {
  entity_id?: string;
  entity_type?: EntityType;
  relationship_types?: RelationshipType[];
  depth?: number;
  limit?: number;
}

export interface LegalGraphToolOutput {
  graph: GraphResult;
  success: boolean;
  error?: string;
}

/**
 * Legal Search Tool for LangGraph
 * Searches legal documents using the laws-API
 */
export async function legalSearchTool(
  input: LegalSearchToolInput,
  authToken?: string
): Promise<LegalSearchToolOutput> {
  const startTime = Date.now();
  
  try {
    const client = createLawsApiClient({}, authToken);
    
    const searchRequest: SearchRequest = {
      query: input.query,
      jurisdiction: input.jurisdiction || ['texas'],
      document_type: input.document_type,
      limit: input.limit || 10,
      // New v1 parameters
      practice_areas: input.practice_areas || input.practice_area || [PracticeArea.PERSONAL_INJURY],
      date_start: input.date_start,
      date_end: input.date_end,
      sort_by: input.sort_by || SortBy.RELEVANCE,
      authority_min: input.authority_min || 0,
      // Legacy filters for backward compatibility
      filters: {
        practice_area: input.practice_area || [PracticeArea.PERSONAL_INJURY]
      }
    };

    const response = await client.search(searchRequest);
    
    if (!response.success) {
      return {
        results: [],
        total_results: 0,
        query_time_ms: Date.now() - startTime,
        success: false,
        error: response.message || 'Search failed'
      };
    }

    return {
      results: response.data,
      total_results: response.pagination?.total || response.data.length,
      query_time_ms: Date.now() - startTime,
      success: true
    };
  } catch (_error) {
    return {
      results: [],
      total_results: 0,
      query_time_ms: Date.now() - startTime,
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

/**
 * Legal Recommendation Tool for LangGraph
 * Gets recommendations based on document or content
 */
export async function legalRecommendTool(
  input: LegalRecommendToolInput,
  authToken?: string
): Promise<LegalRecommendToolOutput> {
  try {
    const client = createLawsApiClient({}, authToken);
    
    const recommendRequest: RecommendRequest = {
      document_id: input.document_id,
      content: input.content,
      context: input.context,
      jurisdiction: input.jurisdiction || ['texas'],
      limit: input.limit || 5,
      similarity_threshold: input.similarity_threshold || 0.7
    };

    const response = await client.recommend(recommendRequest);
    
    if (!response.success) {
      return {
        recommendations: [],
        success: false,
        error: response.message || 'Recommendation failed'
      };
    }

    return {
      recommendations: response.data,
      success: true
    };
  } catch (_error) {
    return {
      recommendations: [],
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

/**
 * Legal Graph Tool for LangGraph
 * Queries the legal knowledge graph
 */
export async function legalGraphTool(
  input: LegalGraphToolInput,
  authToken?: string
): Promise<LegalGraphToolOutput> {
  try {
    const client = createLawsApiClient({}, authToken);
    
    const graphRequest: GraphRequest = {
      entity_id: input.entity_id,
      entity_type: input.entity_type,
      relationship_types: input.relationship_types,
      depth: input.depth || 2,
      limit: input.limit || 50
    };

    const response = await client.graph(graphRequest);
    
    if (!response.success) {
      return {
        graph: { nodes: [], edges: [] },
        success: false,
        error: response.message || 'Graph query failed'
      };
    }

    return {
      graph: response.data,
      success: true
    };
  } catch (_error) {
    return {
      graph: { nodes: [], edges: [] },
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

/**
 * Combined Legal Research Tool
 * Performs search and gets recommendations in one call
 */
export async function legalResearchTool(
  input: {
    query: string;
    get_recommendations?: boolean;
    jurisdiction?: string[];
    practice_area?: PracticeArea[];
    limit?: number;
  },
  authToken?: string
): Promise<{
  search_results: SearchResult[];
  recommendations: RecommendationResult[];
  success: boolean;
  error?: string;
}> {
  try {
    // Perform search
    const searchResult = await legalSearchTool({
      query: input.query,
      jurisdiction: input.jurisdiction,
      practice_area: input.practice_area,
      limit: input.limit
    }, authToken);

    if (!searchResult.success) {
      return {
        search_results: [],
        recommendations: [],
        success: false,
        error: searchResult.error
      };
    }

    let recommendations: RecommendationResult[] = [];
    
    // Get recommendations if requested and we have search results
    if (input.get_recommendations && searchResult.results.length > 0) {
      const recommendResult = await legalRecommendTool({
        content: input.query,
        jurisdiction: input.jurisdiction,
        limit: 3
      }, authToken);
      
      if (recommendResult.success) {
        recommendations = recommendResult.recommendations;
      }
    }

    return {
      search_results: searchResult.results,
      recommendations,
      success: true
    };
  } catch (_error) {
    return {
      search_results: [],
      recommendations: [],
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

/**
 * Tool registry for LangGraph integration
 */
export const legalTools = {
  legal_search: legalSearchTool,
  legal_recommend: legalRecommendTool,
  legal_graph: legalGraphTool,
  legal_research: legalResearchTool
};

/**
 * Tool schemas for LangGraph tool registration
 */
export const legalToolSchemas = {
  legal_search: {
    name: 'legal_search',
    description: 'Search legal documents including statutes, case law, and regulations',
    parameters: {
      type: 'object',
      properties: {
        query: {
          type: 'string',
          description: 'The search query for legal documents'
        },
        jurisdiction: {
          type: 'array',
          items: { type: 'string' },
          description: 'Jurisdictions to search (e.g., ["texas", "federal"])'
        },
        document_type: {
          type: 'array',
          items: { type: 'string' },
          description: 'Types of documents to search'
        },
        practice_area: {
          type: 'array',
          items: { type: 'string' },
          description: 'Practice areas to focus on'
        },
        limit: {
          type: 'number',
          description: 'Maximum number of results to return'
        }
      },
      required: ['query']
    }
  },
  legal_recommend: {
    name: 'legal_recommend',
    description: 'Get recommendations for related legal documents',
    parameters: {
      type: 'object',
      properties: {
        document_id: {
          type: 'string',
          description: 'ID of the document to get recommendations for'
        },
        content: {
          type: 'string',
          description: 'Content to get recommendations for'
        },
        context: {
          type: 'string',
          description: 'Additional context for recommendations'
        },
        jurisdiction: {
          type: 'array',
          items: { type: 'string' },
          description: 'Jurisdictions to search'
        },
        limit: {
          type: 'number',
          description: 'Maximum number of recommendations'
        }
      }
    }
  },
  legal_graph: {
    name: 'legal_graph',
    description: 'Query the legal knowledge graph for relationships',
    parameters: {
      type: 'object',
      properties: {
        entity_id: {
          type: 'string',
          description: 'ID of the entity to explore'
        },
        entity_type: {
          type: 'string',
          description: 'Type of entity (statute, case, court, etc.)'
        },
        relationship_types: {
          type: 'array',
          items: { type: 'string' },
          description: 'Types of relationships to explore'
        },
        depth: {
          type: 'number',
          description: 'Depth of graph traversal'
        },
        limit: {
          type: 'number',
          description: 'Maximum number of nodes to return'
        }
      }
    }
  }
};
