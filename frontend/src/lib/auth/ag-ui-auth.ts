/**
 * AG-UI Authentication Utilities
 * 
 * This file contains utilities for handling authentication with AG-UI and CopilotKit v2.x.
 * It provides functions for generating and validating JWT tokens for secure access to
 * CopilotKit cloud services.
 */

import { cookies } from 'next/headers';
import { createClient } from '@/lib/supabase/server';
import { jwtVerify, SignJWT } from 'jose';

/**
 * Interface for authentication context passed to CopilotKit
 */
export interface AuthContext {
  userId: string;
  organizationId?: string;
  role?: string;
  tenantId?: string;
  authenticated: boolean;
  [key: string]: any;
}

/**
 * Get the current user's authentication context from Supabase
 * 
 * @returns An AuthContext object with user information, or null if not authenticated
 */
export async function getCurrentAuthContext(): Promise<AuthContext | null> {
  try {
    const cookieStore = cookies();
    const supabase = createClient();
    
    // Get session data from Supabase
    const { data: { session }, error } = await supabase.auth.getSession();
    
    if (error || !session) {
      console.log('No active session found:', error?.message || 'User not authenticated');
      return null;
    }
    
    // Extract user information from session
    const user = session.user;
    if (!user) {
      console.log('No user data in session');
      return null;
    }
    
    // Extract organization ID from user metadata
    const organizationId = user.user_metadata?.organization_id as string | undefined;
    const role = user.user_metadata?.role as string | undefined;
    
    return {
      userId: user.id,
      organizationId,
      role: role || 'user',
      tenantId: organizationId, // Alias for organizationId
      authenticated: true,
      email: user.email
    };
  } catch (_error) {
    console.error('Error getting auth context:', error);
    return null;
  }
}

/**
 * Generate a JWT token for CopilotKit API access
 * 
 * @param context The authentication context to encode in the token
 * @returns A signed JWT token
 */
export async function generateCopilotJWT(context: AuthContext): Promise<string> {
  // Ensure we have the JWT secret
  const jwtSecret = process.env.SUPABASE_JWT_SECRET;
  if (!jwtSecret) {
    throw new Error('JWT secret not configured');
  }
  
  // Create a new JWT token with user and organization context
  const token = await new SignJWT({
    // Standard claims
    sub: context.userId,
    // Custom claims
    organization_id: context.organizationId,
    tenant_id: context.tenantId, 
    role: context.role,
    // Add any other needed context
    user_email: context.email,
    authenticated: context.authenticated
  })
    .setProtectedHeader({ alg: 'HS256' })
    .setIssuedAt()
    .setExpirationTime('15m') // Short expiration for security
    .sign(new TextEncoder().encode(jwtSecret));
    
  return token;
}

/**
 * Generate an authorization header for CopilotKit with proper context
 * 
 * @returns A Promise resolving to the authorization header or null if not authenticated
 */
export async function getCopilotAuthHeader(): Promise<string | null> {
  try {
    // Get the current user's authentication context
    const context = await getCurrentAuthContext();
    
    // If not authenticated, return null
    if (!context) {
      return null;
    }
    
    // Generate a JWT token with the context
    const token = await generateCopilotJWT(context);
    
    // Return as a Bearer token
    return `Bearer ${token}`;
  } catch (_error) {
    console.error('Error generating auth header:', error);
    return null;
  }
}

/**
 * Extract thread ID components from user and organization IDs
 * This ensures thread isolation between different organizations
 * 
 * @param userId User ID from auth context
 * @param organizationId Organization ID from auth context
 * @returns An object with threadIdComponents for CopilotKit
 */
export function getThreadComponents(userId: string, organizationId?: string): { threadIdComponents: string[] } {
  // Use deterministic thread ID components to ensure consistent thread retrieval
  return {
    threadIdComponents: [
      // Include organization ID for tenant isolation if available
      ...(organizationId ? [`org-${organizationId}`] : []),
      // Always include user ID for user-specific threads
      `user-${userId}`
    ]
  };
}
