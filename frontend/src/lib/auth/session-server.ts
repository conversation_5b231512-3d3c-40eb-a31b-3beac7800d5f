/**
 * Server-Side Session Management
 * Handles server-side session operations that require next/headers
 */

import { Session, User } from '@supabase/supabase-js';
import { createClient, createServerClientForUser, createServiceClient } from './server';
import { Auth<PERSON><PERSON>, User<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, AuthRouteH<PERSON><PERSON>, SessionExt } from './types';
import { parseJwtPayload, type JwtPayload } from '../supabase/client';
import { enhanceClientWithSchemas } from '../supabase/schema-client';
import { logSecurityEvent } from '../security/forensics';
import { getUnifiedSession, type UnifiedSession } from './getUnifiedSession';
import { Database } from '../supabase/database.types';
import { SupabaseClient } from '@supabase/supabase-js';

/**
 * Gets the current server session using unified session approach
 * This function works in server components and API routes
 *
 * @returns The current session or null if not authenticated
 */
export async function getServerSession(): Promise<Session | null> {
  try {
    // Try unified session first (supports feature flag switching)
    const unifiedSession = await getUnifiedSession();
    if (unifiedSession) {
      // Convert unified session to Supabase session format for compatibility
      return {
        access_token: unifiedSession.access_token,
        refresh_token: unifiedSession.refresh_token || '',
        expires_in: unifiedSession.expires_in || 3600,
        expires_at: unifiedSession.expires_at,
        token_type: 'bearer',
        user: {
          ...unifiedSession.user,
          aud: 'authenticated',
          created_at: (unifiedSession.user as any).created_at || new Date().toISOString()
        } as User
      };
    }

    // Fallback to direct Supabase client
    const supabase = await createClient();
    const { data: { session }, error } = await supabase.auth.getSession();
    
    if (error) {
      console.error('Error getting session:', error);
      return null;
    }
    
    return session;
  } catch (_error) {
    console.error('Error in getServerSession:', error);
    return null;
  }
}

/**
 * Gets the current unified session (server-side)
 * This is the new preferred method that works with both Supabase and legacy auth
 *
 * @returns The unified session or null if not authenticated
 */
export async function getUnifiedServerSession(): Promise<UnifiedSession | null> {
  try {
    return await getUnifiedSession();
  } catch (_error) {
    console.error('Error getting unified session:', error);
    return null;
  }
}

/**
 * Gets the current authenticated user from the server session
 *
 * @returns The current user or null if not authenticated
 */
export async function getUser(): Promise<AuthUser | null> {
  try {
    const session = await getServerSession();
    if (!session?.user) return null;

    return createAuthUserFromSession(session);
  } catch (_error) {
    console.error('Error getting user:', error);
    return null;
  }
}

/**
 * Creates an AuthUser from a Supabase session
 * Extracts role and tenant information from JWT claims
 *
 * @param session The Supabase session
 * @returns AuthUser object with role and tenant information
 */
export function createAuthUserFromSession(session: Session): AuthUser {
  const user = session.user;
  
  // Parse JWT claims for additional user information
  let claims: JwtPayload | null = null;
  try {
    claims = parseJwtPayload(session.access_token);
  } catch (_error) {
    console.warn('Failed to parse JWT claims:', error);
  }

  // Extract role from various possible locations
  let role: UserRole = UserRole.Authenticated;
  
  if (claims?.role) {
    role = claims.role as UserRole;
  } else if (user.user_metadata?.role) {
    role = user.user_metadata.role as UserRole;
  } else if (user.app_metadata?.role) {
    role = user.app_metadata.role as UserRole;
  }

  // Extract tenant information
  let tenantId: string | undefined;
  let tenantClaims: TenantClaims | undefined;

  if (claims?.tenant_id) {
    tenantId = claims.tenant_id;
    tenantClaims = {
      sub: claims.sub,
      tenant_id: claims.tenant_id,
      tenant_name: claims.tenant_name,
      tenant_role: claims.tenant_role
    };
  }

  return {
    id: user.id,
    email: user.email || '',
    role,
    tenantId: tenantId || null,
    metadata: {
      ...user.user_metadata,
      ...user.app_metadata,
      tenantClaims
    }
  };
}

/**
 * Higher-order function that requires authentication for API routes
 * Automatically handles authentication checking and error responses
 *
 * @param handler The API route handler to protect
 * @returns Protected API route handler
 */
export function requireAuth(
  handler: AuthRouteHandler
): AuthRouteHandler {
  return async (_req: any, res: any) => {
    try {
      const session = await getServerSession();

      if (!session?.user) {
        return res.status(401).json({
          error: 'Unauthorized',
          message: 'Authentication required'
        });
      }

      const user = createAuthUserFromSession(session);
      const supabase = await createClient();

      // Log security event
      await logSecurityEvent(supabase, 'api_access', {
        userId: user.id,
        tenantId: user.tenantId,
        resource: req.url,
        userAgent: req.headers?.['user-agent']
      });

      return handler(req, user, supabase, { session });
    } catch (_error) {
      console.error('Auth middleware error:', error);
      return res.status(500).json({
        error: 'Internal Server Error',
        message: 'Authentication check failed'
      });
    }
  };
}

/**
 * Simplified auth callback wrapper for API routes
 * Provides just the user ID to the callback function
 *
 * @param req The Next.js request object
 * @param callback The callback function that receives the user ID
 * @returns Response from the callback or error response
 */
export async function withAuthCallback(
  req: any,
  callback: (userId: string) => Promise<Response>
): Promise<Response> {
  try {
    const session = await getServerSession();

    if (!session?.user) {
      return new Response(
        JSON.stringify({
          error: 'Unauthorized',
          message: 'Authentication required'
        }),
        {
          status: 401,
          headers: { 'Content-Type': 'application/json' }
        }
      );
    }

    const user = createAuthUserFromSession(session);
    return await callback(user.id);
  } catch (_error) {
    console.error('Auth callback error:', error);
    return new Response(
      JSON.stringify({
        error: 'Internal Server Error',
        message: 'Authentication check failed'
      }),
      {
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      }
    );
  }
}

/**
 * Higher-order function that requires service role access
 * Used for admin operations that bypass RLS
 *
 * @param handler The API route handler to protect
 * @returns Protected API route handler with service role access
 */
export function withServiceRole(
  handler: (_req: any, res: any, context: { supabase: SupabaseClient<Database> }) => Promise<any>
): AuthRouteHandler {
  return async (_req: any, res: any) => {
    try {
      const supabase = createServiceClient();
      
      // Log security event for service role usage
      await logSecurityEvent(supabase, 'service_role_access', {
        userId: 'system',
        resource: req.url,
        userAgent: req.headers?.['user-agent']
      });

      return handler(req, res, { supabase });
    } catch (_error) {
      console.error('Service role middleware error:', error);
      return res.status(500).json({ 
        error: 'Internal Server Error',
        message: 'Service role access failed' 
      });
    }
  };
}

/**
 * Higher-order function that requires authentication and specific roles
 * Combines authentication and authorization in one middleware
 *
 * @param roles Array of roles that are allowed to access the route
 * @param handler The API route handler to protect
 * @returns Protected API route handler
 */
export function withAuth(
  roles: UserRole[],
  handler: AuthRouteHandler
): AuthRouteHandler {
  return async (_req: any, res: any) => {
    try {
      const session = await getServerSession();
      
      if (!session?.user) {
        return res.status(401).json({ 
          error: 'Unauthorized',
          message: 'Authentication required' 
        });
      }

      const user = createAuthUserFromSession(session);
      const supabase = await createClient();

      // Check if user has required role
      if (!roles.includes(user.role)) {
        await logSecurityEvent(supabase, 'unauthorized_access_attempt', {
          userId: user.id,
          tenantId: user.tenantId,
          resource: req.url,
          requiredRoles: roles,
          userRoles: [user.role]
        });

        return res.status(403).json({ 
          error: 'Forbidden',
          message: 'Insufficient permissions' 
        });
      }

      // Log successful access
      await logSecurityEvent(supabase, 'authorized_api_access', {
        userId: user.id,
        tenantId: user.tenantId,
        resource: req.url,
        userRoles: [user.role]
      });

      return handler(req, user, supabase, { session });
    } catch (_error) {
      console.error('Auth middleware error:', error);
      return res.status(500).json({ 
        error: 'Internal Server Error',
        message: 'Authentication check failed' 
      });
    }
  };
}
