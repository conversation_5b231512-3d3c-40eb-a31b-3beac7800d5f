/**
 * Server-Side Permissions and Role Management
 * Handles server-side role checking and unified session access
 */

import { UserRole } from './types';
import { getUnifiedSession, getUserRoles } from './getUnifiedSession';

/**
 * Check if current session has specific roles using unified session
 * This is the server-side version that works with both Supabase and legacy auth
 *
 * @param roles Array of roles to check against
 * @returns True if the current session has one of the specified roles
 */
export async function hasRoleUnified(roles: (UserRole | string)[]): Promise<boolean> {
  try {
    const session = await getUnifiedSession();
    if (!session) return false;

    const userRoles = getUserRoles(session);
    return userRoles.some(role => roles.includes(role));
  } catch (_error) {
    console.error('Error checking unified role:', error);
    return false;
  }
}

/**
 * Check if current session is authenticated using unified session
 * 
 * @returns True if the current session is authenticated
 */
export async function isAuthenticatedUnified(): Promise<boolean> {
  try {
    const session = await getUnifiedSession();
    return !!session;
  } catch (_error) {
    console.error('Error checking unified authentication:', error);
    return false;
  }
}

/**
 * Get current user roles using unified session
 * 
 * @returns Array of user roles
 */
export async function getCurrentUserRoles(): Promise<string[]> {
  try {
    const session = await getUnifiedSession();
    if (!session) return [];

    return getUserRoles(session);
  } catch (_error) {
    console.error('Error getting current user roles:', error);
    return [];
  }
}
