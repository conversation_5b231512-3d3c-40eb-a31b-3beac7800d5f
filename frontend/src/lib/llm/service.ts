import { OpenAI } from 'openai';
import { GoogleGenerativeAI } from '@google/generative-ai';
import NodeCache from 'node-cache';

// Initialize cache with 10-minute TTL and check period of 60 seconds
const cache = new NodeCache({ stdTTL: 600, checkperiod: 60 });

// LLM Provider types
export type LLMProvider = 'openai' | 'google';

// LLM Model configuration
export interface LLMConfig {
  provider: LLMProvider;
  model: string;
  temperature?: number;
  maxTokens?: number;
  topP?: number;
}

// Default configurations
const DEFAULT_OPENAI_MODEL = 'gpt-4o';
const DEFAULT_GOOGLE_MODEL = 'gemini-1.5-flash';
const DEFAULT_TEMPERATURE = 0.2;

// Initialize clients lazily to avoid build-time errors
let openai: OpenAI | null = null;
let googleAI: GoogleGenerativeAI | null = null;

function getOpenAIClient(): OpenAI {
  if (!openai) {
    const apiKey = process.env.OPENAI_API_KEY;
    if (!apiKey) {
      throw new Error('OpenAI API key is not configured');
    }
    openai = new OpenAI({ apiKey });
  }
  return openai;
}

function getGoogleAIClient(): GoogleGenerativeAI {
  if (!googleAI) {
    const apiKey = process.env.GOOGLE_AI_API_KEY;
    if (!apiKey) {
      throw new Error('Google AI API key is not configured');
    }
    googleAI = new GoogleGenerativeAI(apiKey);
  }
  return googleAI;
}

/**
 * Unified LLM service that supports multiple providers with fallback options
 */
export class LLMService {
  private primaryConfig: LLMConfig;
  private fallbackConfig: LLMConfig;
  private enableCaching: boolean;
  private rateLimitPerMinute: number;
  private requestCount: Map<string, number> = new Map();
  private lastResetTime: number = Date.now();

  constructor({
    primaryConfig = {
      provider: 'openai',
      model: DEFAULT_OPENAI_MODEL,
      temperature: DEFAULT_TEMPERATURE,
    },
    fallbackConfig = {
      provider: 'google',
      model: DEFAULT_GOOGLE_MODEL,
      temperature: DEFAULT_TEMPERATURE,
    },
    enableCaching = true,
    rateLimitPerMinute = 10,
  }: {
    primaryConfig?: LLMConfig;
    fallbackConfig?: LLMConfig;
    enableCaching?: boolean;
    rateLimitPerMinute?: number;
  } = {}) {
    this.primaryConfig = primaryConfig;
    this.fallbackConfig = fallbackConfig;
    this.enableCaching = enableCaching;
    this.rateLimitPerMinute = rateLimitPerMinute;
  }

  /**
   * Generate a response using the configured LLM
   */
  async generateResponse(
    prompt: string,
    systemPrompt: string = '',
    options: {
      forceProvider?: LLMProvider;
      cacheKey?: string;
      responseFormat?: 'json' | 'text';
    } = {}
  ): Promise<string> {
    const {
      forceProvider,
      cacheKey = this.generateCacheKey(prompt, systemPrompt),
      responseFormat = 'text',
    } = options;

    // Check rate limits
    this.checkAndUpdateRateLimits();

    // Check cache if enabled
    if (this.enableCaching && cache.has(cacheKey)) {
      console.log('[LLMService] Cache hit for:', cacheKey);
      return cache.get(cacheKey) as string;
    }

    // Determine which provider to use
    const config = forceProvider
      ? forceProvider === 'openai'
        ? this.primaryConfig
        : this.fallbackConfig
      : this.primaryConfig;

    try {
      // Try primary provider
      const response = await this.callProvider(
        config.provider,
        prompt,
        systemPrompt,
        {
          model: config.model,
          temperature: config.temperature,
          responseFormat,
        }
      );

      // Cache the response if caching is enabled
      if (this.enableCaching) {
        cache.set(cacheKey, response);
      }

      return response;
    } catch (_error) {
      console.error(`[LLMService] Error with ${config.provider}:`, error);

      // If not already using fallback, try fallback provider
      if (config.provider !== this.fallbackConfig.provider && !forceProvider) {
        console.log('[LLMService] Falling back to:', this.fallbackConfig.provider);
        try {
          const fallbackResponse = await this.callProvider(
            this.fallbackConfig.provider,
            prompt,
            systemPrompt,
            {
              model: this.fallbackConfig.model,
              temperature: this.fallbackConfig.temperature,
              responseFormat,
            }
          );

          // Cache the fallback response
          if (this.enableCaching) {
            cache.set(cacheKey, fallbackResponse);
          }

          return fallbackResponse;
        } catch (fallbackError) {
          console.error(`[LLMService] Fallback to ${this.fallbackConfig.provider} also failed:`, fallbackError);
          throw new Error('All LLM providers failed to generate a response');
        }
      }

      throw error;
    }
  }

  /**
   * Call a specific LLM provider
   */
  private async callProvider(
    provider: LLMProvider,
    prompt: string,
    systemPrompt: string,
    options: {
      model: string;
      temperature?: number;
      responseFormat?: 'json' | 'text';
    }
  ): Promise<string> {
    const { model, temperature = DEFAULT_TEMPERATURE, responseFormat = 'text' } = options;

    switch (provider) {
      case 'openai':
        return this.callOpenAI(prompt, systemPrompt, model, temperature, responseFormat);
      case 'google':
        return this.callGoogleAI(prompt, systemPrompt, model, temperature, responseFormat);
      default:
        throw new Error(`Unsupported provider: ${provider}`);
    }
  }

  /**
   * Call OpenAI API
   */
  private async callOpenAI(
    prompt: string,
    systemPrompt: string,
    model: string,
    temperature: number,
    responseFormat: 'json' | 'text'
  ): Promise<string> {
    const openaiClient = getOpenAIClient();

    const messages: Array<{ role: 'system' | 'user' | 'assistant'; content: string }> = [];

    if (systemPrompt) {
      messages.push({ role: 'system', content: systemPrompt });
    }

    messages.push({ role: 'user', content: prompt });

    const response = await openaiClient.chat.completions.create({
      model,
      messages,
      temperature,
      response_format: responseFormat === 'json' ? { type: 'json_object' } : undefined,
    });

    return response.choices[0]?.message?.content || '';
  }

  /**
   * Call Google Generative AI
   */
  private async callGoogleAI(
    prompt: string,
    systemPrompt: string,
    model: string,
    temperature: number,
    responseFormat: 'json' | 'text'
  ): Promise<string> {
    const googleAIClient = getGoogleAIClient();

    const geminiModel = googleAIClient.getGenerativeModel({ model });

    // Format prompt based on whether system prompt is provided
    const formattedPrompt = systemPrompt
      ? `${systemPrompt}\n\n${prompt}`
      : prompt;

    // Add JSON formatting instruction if needed
    const finalPrompt = responseFormat === 'json'
      ? `${formattedPrompt}\n\nRespond with a valid JSON object only.`
      : formattedPrompt;

    const result = await geminiModel.generateContent(finalPrompt);
    const text = result.response.text();

    // If JSON is requested, validate the response
    if (responseFormat === 'json') {
      try {
        // Check if the response is valid JSON
        JSON.parse(text);
        return text;
      } catch (_e) {
        // If not valid JSON, try to extract JSON from the text
        const jsonMatch = text.match(/\{[\s\S]*\}/);
        if (jsonMatch) {
          try {
            JSON.parse(jsonMatch[0]);
            return jsonMatch[0];
          } catch (_e) {
            throw new Error('Failed to parse JSON from Google AI response');
          }
        }
        throw new Error('Google AI did not return valid JSON');
      }
    }

    return text;
  }

  /**
   * Generate a cache key from the prompt and system prompt
   */
  private generateCacheKey(prompt: string, systemPrompt: string): string {
    const combinedInput = `${systemPrompt}:${prompt}`;
    // Simple hash function for cache key
    let hash = 0;
    for (let i = 0; i < combinedInput.length; i++) {
      const char = combinedInput.charCodeAt(i);
      hash = (hash << 5) - hash + char;
      hash = hash & hash; // Convert to 32bit integer
    }
    return `llm_${hash}`;
  }

  /**
   * Check and update rate limits
   */
  private checkAndUpdateRateLimits(): void {
    const now = Date.now();
    const minutesPassed = Math.floor((now - this.lastResetTime) / 60000);

    // Reset counters if a minute has passed
    if (minutesPassed >= 1) {
      this.requestCount.clear();
      this.lastResetTime = now;
    }

    // Check current provider's rate limit
    const provider = this.primaryConfig.provider;
    const currentCount = this.requestCount.get(provider) || 0;

    if (currentCount >= this.rateLimitPerMinute) {
      throw new Error(`Rate limit exceeded for ${provider}`);
    }

    // Update count
    this.requestCount.set(provider, currentCount + 1);
  }

  /**
   * Clear the cache
   */
  clearCache(): void {
    cache.flushAll();
  }

  /**
   * Get cache stats
   */
  getCacheStats(): { keys: number; hits: number; misses: number } {
    return {
      keys: cache.keys().length,
      hits: cache.getStats().hits,
      misses: cache.getStats().misses,
    };
  }

  /**
   * Update the primary LLM configuration
   */
  updatePrimaryConfig(config: Partial<LLMConfig>): void {
    this.primaryConfig = { ...this.primaryConfig, ...config };
  }

  /**
   * Update the fallback LLM configuration
   */
  updateFallbackConfig(config: Partial<LLMConfig>): void {
    this.fallbackConfig = { ...this.fallbackConfig, ...config };
  }
}

// Create and export a singleton instance with default configuration
export const llmService = new LLMService();
