/**
 * Helper functions for API routes that use Supabase
 * This file provides utilities for working with Supabase in API routes
 *
 * NOTE: This file is deprecated. Use api-helpers.ts instead.
 * It is kept for backward compatibility.
 */

import { NextRequest, NextResponse } from 'next/server';
import { createServerClient, type CookieOptions } from '@supabase/ssr';
import { cookies } from 'next/headers';
import { Database } from '@/lib/supabase/database.types';
import { createTypedDatabaseClient, TypedDatabaseClient } from './typed-client';
import { AuthUser, UserRole, isValidUserRole } from '@/lib/types/auth';
import { SupabaseClient, Session } from '@supabase/supabase-js';
import { parseJwtPayload, JwtPayload } from './client'; // Use parseJwtPayload
import { logSecurityEvent } from '../security/forensics'; // Import logSecurityEvent instead

/**
 * Creates an AuthUser object from a Supabase Session.
 * Prioritizes information from the JWT access token.
 */
export const createAuthUser = (
  supabase: SupabaseClient<Database>,
  session: Session | null
): AuthUser | null => {
  if (!session?.user || !session.access_token) {
    logSecurityEvent(supabase, 'auth.session_missing', { message: 'No active session or access token provided to createAuthUser.' });
    return null;
  }

  const sessionUser = session.user;
  const token = session.access_token;
  let role: UserRole = UserRole.Client; // Use Client as default
  let tenantId: string | null = null;
  let email: string | undefined | null = sessionUser.email; // Get initial email

  try {
    const decoded: JwtPayload | null = parseJwtPayload(token);
    if (decoded) {
      // Validate required JWT claims
      if (!decoded.sub) throw new Error('JWT missing required claim: sub');
      if (!decoded.email) throw new Error('JWT missing required claim: email');
      if (!decoded.role) throw new Error('JWT missing required claim: role');

      const roleFromToken = decoded.role as string;
      if (isValidUserRole(roleFromToken)) {
        role = roleFromToken as UserRole;
      } else {
        logSecurityEvent(supabase, 'auth.jwt_invalid_role', { message: `Invalid role found in JWT: ${roleFromToken}. Defaulting to ${role}.`, userId: decoded.sub });
        // Optionally throw an error
        // throw new Error('Invalid token claims (role)');
      }
      tenantId = decoded.tenant_id ?? null;
      email = decoded.email; // Prioritize JWT email

      // Construct the AuthUser object
      const authUser: AuthUser = {
        id: decoded.sub,        // Use sub from JWT
        email: email,           // Use email from JWT (validated required)
        role: role,
        tenantId: tenantId,     // Can be null
        metadata: sessionUser.user_metadata ?? {},
      };
      logSecurityEvent(supabase, 'auth.user_created_debug', { message: 'AuthUser created from session in route-helpers', userId: authUser.id, role: authUser.role, tenantId: authUser.tenantId });
      return authUser;
    } else {
      logSecurityEvent(supabase, 'auth.jwt_parse_failed', { message: 'Failed to parse JWT payload in createAuthUser (route-helpers).' });
      return null; // Or throw
    }
  } catch (error: any) {
    logSecurityEvent(supabase, 'auth.session_processing_error', { message: 'Error processing session/JWT in createAuthUser (route-helpers)', error: error.message });
    // throw new Error('Failed to process authentication token.');
    return null;
  }
};

/**
 * Type for a route handler function with authentication and typed database access
 */
export type AuthRouteHandler = (
  req: NextRequest,
  user: AuthUser,
  db: TypedDatabaseClient,
  context: Record<string, any>
) => Promise<Response>;

/**
 * Wraps a route handler with authentication and typed database access
 *
 * @param handler The route handler function
 * @returns A Next.js API route handler
 */
export function withTypedDb(handler: AuthRouteHandler) {
  return async (
    req: NextRequest,
    context: { params: Record<string, string> }
  ): Promise<Response> => {
    try {
      // Create a Supabase client for the API route
      // Use the ReadonlyRequestCookies directly
      const cookieStore = cookies();

      // Create a synchronous cookie store for Supabase
      const cookieObject = {
        get(name: string) {
          // @ts-expect-error - We know this is a ReadonlyRequestCookies
          const cookie = cookieStore.get?.(name);
          return cookie?.value || '';
        },
        set(name: string, value: string, options: any) {
          try {
            // @ts-expect-error - We know this is a ReadonlyRequestCookies
            cookieStore.set?.(name, value, options);
          } catch (_error) {
            console.error('Error setting cookie:', error);
          }
        },
        remove(name: string, options: any) {
          try {
            // @ts-expect-error - We know this is a ReadonlyRequestCookies
            cookieStore.delete?.(name);
          } catch (_error) {
            console.error('Error deleting cookie:', error);
          }
        }
      };

      const supabase = createServerClient<Database>(
        process.env.NEXT_PUBLIC_SUPABASE_URL!,
        process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
        {
          cookies: cookieObject
        }
      );

      // Get the current user
      const { data: { user }, error: userError } = await supabase.auth.getUser();

      if (userError || !user) {
        console.error('Authentication error:', userError);
        return NextResponse.json(
          { error: 'Unauthorized' },
          { status: 401 }
        );
      }

      // Get the user's tenant ID from the JWT claims
      const { data: { session }, error: sessionError } = await supabase.auth.getSession();

      if (sessionError || !session) {
        console.error('Session error:', sessionError);
        return NextResponse.json(
          { error: 'Unauthorized' },
          { status: 401 }
        );
      }

      // Call createAuthUser with the supabase client
      const authUser = createAuthUser(supabase, session);

      if (!authUser) {
        console.error('Failed to create AuthUser');
        return NextResponse.json(
          { error: 'Unauthorized' },
          { status: 401 }
        );
      }

      // Create a typed database client
      const db = createTypedDatabaseClient(supabase);

      // Call the handler with the authenticated user and typed database client
      return await handler(req, authUser, db, context);
    } catch (_error) {
      console.error('Error in route handler:', error);
      return NextResponse.json(
        { error: 'Internal server error' },
        { status: 500 }
      );
    }
  };
}
