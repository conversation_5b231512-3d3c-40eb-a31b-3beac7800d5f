import { createServerClient } from '@supabase/ssr'
import { cookies } from 'next/headers'
import { type Database } from './database.types'
import { type CookieOptions } from '@supabase/ssr'

/**
 * Creates a Supabase client for use in server components
 *
 * This function manages cookies through the Next.js cookies() API.
 *
 * @returns A type-safe Supabase client
 */
export function createClient() {
  // Handle missing environment variables during build time
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || 'https://placeholder.supabase.co';
  const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || 'placeholder-anon-key';

  return createServerClient<Database>(
    supabaseUrl,
    supabaseAnonKey,
    {
      cookies: {
        get(name: string) {
          // Get the cookie store - use synchronous version for SSR
          const cookieStore = cookies();
          // Use type assertion to handle the cookies API
          return (cookieStore as any).get(name)?.value;
        },
        set(name: string, value: string, options: CookieOptions) {
          // Get the cookie store - use synchronous version for SSR
          const cookieStore = cookies();

          // Convert Supabase cookie options to Next.js cookie options
          const cookieOptions: Record<string, unknown> = {
            ...options,
            // Ensure secure is converted correctly
            secure: options.secure
          };

          // Use set method with type assertion
          (cookieStore as any).set(name, value, cookieOptions);
        },
        remove(name: string, options: CookieOptions) {
          // Get the cookie store - use synchronous version for SSR
          const cookieStore = cookies();

          // Convert Supabase cookie options to Next.js cookie options
          const cookieOptions: Record<string, unknown> = {
            ...options,
            // Ensure secure is converted correctly
            secure: options.secure
          };

          // Use delete method with type assertion
          (cookieStore as any).delete(name, cookieOptions);
        },
      },
      global: {
        fetch: async (url, options) => {
          // Add retry logic for fetch operations
          const maxRetries = 3;
          let retries = 0;
          let lastError: Error | unknown;

          while (retries < maxRetries) {
            try {
              return await fetch(url, options);
            } catch (_error) {
              lastError = error;
              retries++;
              console.warn(`Supabase fetch failed, retry attempt ${retries}/${maxRetries}`, error);

              // Exponential backoff: 500ms, 1000ms, 2000ms, etc.
              const delay = Math.min(500 * Math.pow(2, retries - 1), 5000);
              await new Promise(resolve => setTimeout(resolve, delay));
            }
          }

          console.error('All Supabase fetch retry attempts failed:', lastError);
          throw lastError;
        }
      }
    }
  )
}

// Create a client with service role for admin operations
export function createServiceClient() {
  // Handle missing environment variables during build time
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || 'https://placeholder.supabase.co';
  const supabaseServiceKey = process.env.SUPABASE_SERVICE_KEY || 'placeholder-service-key';

  return createServerClient<Database>(
    supabaseUrl,
    supabaseServiceKey,
    {
      cookies: {
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        get(_name: string) {
          return undefined;
        },
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        set(_name: string, _value: string, _options: CookieOptions) {
          // No-op for service client - doesn't need cookies
        },
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        remove(_name: string, _options: CookieOptions) {
          // No-op for service client - doesn't need cookies
        },
      },
      global: {
        fetch: async (url, options) => {
          // Add retry logic for fetch operations
          const maxRetries = 3;
          let retries = 0;
          let lastError: Error | unknown;

          while (retries < maxRetries) {
            try {
              return await fetch(url, options);
            } catch (_error) {
              lastError = error;
              retries++;
              console.warn(`Supabase service fetch failed, retry attempt ${retries}/${maxRetries}`, error);

              // Exponential backoff: 500ms, 1000ms, 2000ms, etc.
              const delay = Math.min(500 * Math.pow(2, retries - 1), 5000);
              await new Promise(resolve => setTimeout(resolve, delay));
            }
          }

          console.error('All Supabase service fetch retry attempts failed:', lastError);
          throw lastError;
        }
      }
    }
  )
}

// Optional: Create a client for server components
export function createServerComponentClient() {
  return createClient();
}

// Optional: Create a client for route handlers
export function createRouteHandlerClient() {
  return createClient();
}

// Create a service role client for route handlers
export function createServiceRouteHandlerClient() {
  return createServiceClient();
}
