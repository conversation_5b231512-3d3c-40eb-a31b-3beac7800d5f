/**
 * API Helpers for Supabase
 * This file provides utilities for working with Supabase in API routes
 */

import { NextRequest, NextResponse } from 'next/server';
import { createServerClient } from '@supabase/ssr';
import { cookies } from 'next/headers';
import { Database } from './database.types';
import { SupabaseClient } from '@supabase/supabase-js';
import { AuthUser, UserRole, isValidUserRole } from '../types/auth';
import { enhanceClientWithSchemas } from './schema-client';
import { Session } from '@supabase/supabase-js';
import { parseJwtPayload, JwtPayload } from './client';
import { logSecurityEvent } from '../security/forensics';

/**
 * Type for a route handler function with authentication and typed database access
 */
export type AuthRouteHandler = (
  req: NextRequest,
  user: AuthUser,
  supabase: SupabaseClient<Database>,
  context: Record<string, any>
) => Promise<Response>;

/**
 * Creates an AuthUser object from a Supabase session
 *
 * @param supabase The Supabase client
 * @param session The Supabase session
 * @returns The AuthUser object or null if session is invalid
 */
export const createAuthUserFromSession = (
  supabase: SupabaseClient<Database>,
  session: Session | null
): AuthUser | null => {
  if (!session?.user || !session.access_token) {
    logSecurityEvent(supabase, 'auth.session_missing', { message: 'No active session or access token found in createAuthUserFromSession.'});
    return null;
  }

  const userFromSession = session.user;
  const token = session.access_token;
  let role: UserRole = UserRole.Client; // Use Client as default
  let tenantId: string | null = null;
  let email: string | undefined | null = userFromSession.email;

  try {
    const decoded: JwtPayload | null = parseJwtPayload(token);
    if (decoded) {
      // Validate required claims from JWT
      if (!decoded.sub) throw new Error('JWT missing required claim: sub');
      if (!decoded.email) throw new Error('JWT missing required claim: email');
      if (!decoded.role) throw new Error('JWT missing required claim: role');

      const roleFromToken = decoded.role as string;
      if (isValidUserRole(roleFromToken)) {
        role = roleFromToken as UserRole;
      } else {
        logSecurityEvent(supabase, 'auth.jwt_invalid_role', { message: `Invalid role found in JWT: ${roleFromToken}. Defaulting to ${role}.`, userId: decoded.sub });
        // Optionally throw an error if role MUST be valid
        // throw new Error('Invalid token claims (role)');
      }
      tenantId = decoded.tenant_id ?? null; // Use null coalescing
      email = decoded.email; // Prioritize email from JWT

      // Construct the AuthUser object
      const authUser: AuthUser = {
        id: decoded.sub, // Use 'sub' from JWT for ID
        email: email, // Already validated as required from JWT
        role: role,
        tenantId: tenantId, // Can be null
        metadata: userFromSession.user_metadata ?? {},
      };
      logSecurityEvent(supabase, 'auth.user_created_debug', { message: 'AuthUser created from session', userId: authUser.id, role: authUser.role, tenantId: authUser.tenantId });
      return authUser;

    } else {
      logSecurityEvent(supabase, 'auth.jwt_parse_failed', { message: 'Failed to parse JWT payload in createAuthUserFromSession.' });
      return null; // Or throw an error if JWT parsing is critical
    }
  } catch (error: any) {
    logSecurityEvent(supabase, 'auth.session_processing_error', { message: 'Error processing session/JWT in createAuthUserFromSession', error: error.message });
    // Depending on policy, either return null or re-throw
    // throw new Error('Failed to process authentication token.');
    return null;
  }
};

/**
 * Wraps a route handler with authentication and typed database access
 *
 * @param handler The route handler function
 * @returns A Next.js API route handler
 */
export function withAuth(handler: AuthRouteHandler) {
  return async (
    req: NextRequest,
    context: { params: Record<string, string> }
  ): Promise<Response> => {
    try {
      // Create a Supabase client for the API route
      // Use the ReadonlyRequestCookies directly
      const cookieStore = cookies();

      // Create a synchronous cookie store for Supabase
      const cookieObject = {
        get(name: string) {
          // @ts-expect-error - We know this is a ReadonlyRequestCookies
          const cookie = cookieStore.get?.(name);
          return cookie?.value || '';
        },
        set(name: string, value: string, options: any) {
          try {
            // @ts-expect-error - We know this is a ReadonlyRequestCookies
            cookieStore.set?.(name, value, options);
          } catch (_error) {
            console.error('Error setting cookie:', error);
          }
        },
        remove(name: string, options: any) {
          try {
            // @ts-expect-error - We know this is a ReadonlyRequestCookies
            cookieStore.delete?.(name);
          } catch (_error) {
            console.error('Error deleting cookie:', error);
          }
        }
      };

      const supabase = createServerClient<Database>(
        process.env.NEXT_PUBLIC_SUPABASE_URL!,
        process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
        {
          cookies: cookieObject
        }
      );

      // Get the current user
      const { data: { user }, error: userError } = await supabase.auth.getUser();

      if (userError || !user) {
        console.error('Authentication error:', userError);
        return NextResponse.json(
          { error: 'Unauthorized' },
          { status: 401 }
        );
      }

      // Get the user's tenant ID from the JWT claims
      const { data: { session }, error: sessionError } = await supabase.auth.getSession();

      if (sessionError || !session) {
        console.error('Session error:', sessionError);
        return NextResponse.json(
          { error: 'Unauthorized' },
          { status: 401 }
        );
      }

      // Call createAuthUserFromSession with the supabase client
      const authUser = createAuthUserFromSession(supabase, session);

      if (!authUser) {
        console.error('Failed to create AuthUser from session');
        return NextResponse.json(
          { error: 'Unauthorized' },
          { status: 401 }
        );
      }

      // Enhance the Supabase client with schema support
      const enhancedClient = enhanceClientWithSchemas(supabase);

      // Call the handler with the authenticated user and enhanced client
      return await handler(req, authUser, enhancedClient, context);
    } catch (_error) {
      console.error('Error in route handler:', error);
      return NextResponse.json(
        { error: 'Internal server error' },
        { status: 500 }
      );
    }
  };
}

/**
 * Creates a Supabase client with service role for admin operations
 *
 * @returns A Supabase client with service role
 */
export function createServiceClient() {
  return createServerClient<Database>(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.SUPABASE_SERVICE_KEY!,
    {
      cookies: {
        get: (_name: string) => undefined,
        set: (_name: string, _value: string, _options: any) => {},
        remove: (_name: string, _options: any) => {}
      }
    }
  );
}

/**
 * Wraps a route handler with service role access
 *
 * @param handler The route handler function
 * @returns A Next.js API route handler
 */
export function withServiceRole(
  handler: (
    req: NextRequest,
    supabase: SupabaseClient<Database>,
    context: Record<string, any>
  ) => Promise<Response>
) {
  return async (
    req: NextRequest,
    context: { params: Record<string, string> }
  ): Promise<Response> => {
    try {
      // Create a service client
      const supabase = createServiceClient();

      // Enhance the Supabase client with schema support
      const enhancedClient = enhanceClientWithSchemas(supabase);

      // Call the handler with the service client
      return await handler(req, enhancedClient, context);
    } catch (_error) {
      console.error('Error in service role handler:', error);
      return NextResponse.json(
        { error: 'Internal server error' },
        { status: 500 }
      );
    }
  };
}
