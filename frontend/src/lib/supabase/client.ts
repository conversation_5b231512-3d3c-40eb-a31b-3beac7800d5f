// frontend/src/lib/supabase/client.ts
import { createBrowserClient, type CookieOptions } from '@supabase/ssr';
import { Database } from './database.types'; // Corrected relative path
import { User as AuthUser } from '@supabase/supabase-js';
import { UserRole } from '../types/auth';
import { TypedSupabaseClient } from './client-types';

// Define the expected structure of the JWT payload
export interface JwtPayload {
  sub: string; // User ID
  exp: number; // Expiration timestamp (seconds since epoch)
  iat?: number; // Issued at timestamp
  role?: string; // Custom user role
  tenant_id?: string; // Custom tenant ID
  email?: string;
  is_super_admin?: boolean; // Super admin flag for platform operations
  // Allow other potential properties added by Supabase Auth or custom hooks
  [key: string]: unknown;
}

// Load Supabase credentials from env
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!;

// Debug: ensure env vars are loaded correctly
console.debug('Supabase environment variables:', { supabaseUrl, supabaseAnonKey });
if (!supabaseUrl || !supabaseAnonKey) {
  console.error('Supabase env vars missing or invalid', { supabaseUrl, supabaseAnonKey });
  // Throw an error or handle appropriately if env vars are missing
  throw new Error('Supabase environment variables are not configured correctly.');
}

// Create a singleton Supabase client for the browser with log messages
// We create the standard client first, then cast it to our extended type
const supabaseClient = createBrowserClient<Database>(
  supabaseUrl,
  supabaseAnonKey,
  {
    auth: {
      persistSession: true,
      autoRefreshToken: false, // Disable auto refresh to prevent multiple refresh attempts
      debug: true, // Enable debug logs for auth
      detectSessionInUrl: false, // Disable URL detection
      flowType: 'pkce', // Use PKCE flow
      storageKey: 'sb-anwefmklplkjxkmzpnva-auth-token', // Explicitly set storage key
    },
    cookies: {
      // Provide minimal implementations to satisfy types
      get(name: string): string | undefined {
        // In a browser context, Supabase client might handle this internally
        // or rely on document.cookie. This is mainly for type compatibility.
        if (typeof document === 'undefined') return undefined;
        const match = document.cookie.match(new RegExp('(^| )' + name + '=([^;]+)'));
        return match ? decodeURIComponent(match[2]) : undefined;
      },
      set(name: string, value: string, options: CookieOptions): void {
        if (typeof document === 'undefined') return;
        // Basic implementation, actual handling might be more complex internally
        let cookie = `${name}=${encodeURIComponent(value)}`;
        if (options.maxAge) cookie += `; Max-Age=${options.maxAge}`;
        // Add other options as needed (path, domain, secure, sameSite)
        document.cookie = cookie;
      },
      remove(name: string, options: CookieOptions): void {
        if (typeof document === 'undefined') return;
        // Set expiry to the past to remove the cookie
        document.cookie = `${name}=; Max-Age=-1; path=${options.path || '/'}`;
      },
    },
    global: {
      headers: {
        'X-Client-Info': 'pi-lawyer-ai-frontend',
      },
    },
    db: {
      schema: 'public'
    }
  }
);

// Cast to our extended type for better TypeScript support
export const supabase = supabaseClient as unknown as TypedSupabaseClient;

console.log("Supabase Client Instance (client.ts - Browser):", supabase); // Debug log

if (!supabase) {
    console.error("Supabase client initialization failed in client.ts!"); // Error log if null
}

// Helper to parse JWT payload
export const parseJwtPayload = (token: string): JwtPayload | null => {
  try {
    // Decode Base64 URL encoded payload
    const base64Payload = token.split('.')[1].replace(/-/g, '+').replace(/_/g, '/');
    const jsonPayload = decodeURIComponent(atob(base64Payload).split('').map(function(c) {
        return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);
    }).join(''));

    const payload = JSON.parse(jsonPayload);

    // Basic validation: Check if it's an object and has essential fields
    if (typeof payload === 'object' && payload !== null && 'exp' in payload && 'sub' in payload) {
      // Explicitly cast to JwtPayload after validation
      return payload as JwtPayload;
    } else {
        console.error('Parsed JWT payload is invalid or missing required fields (exp, sub):', payload);
        return null;
    }
  } catch (_e) {
    console.error('Error parsing JWT:', e);
    return null;
  }
};

/**
 * Get the JWT payload from the current session
 * This is the single source of truth for user claims
 */
export const getJwtPayload = async (): Promise<JwtPayload | null> => {
  if (!supabase) return null;

  try {
    // Get fresh session data
    const { data } = await supabase.auth.getSession();

    if (!data?.session) {
      console.warn('No session found when getting JWT payload');
      return null;
    }

    if (data.session.access_token) {
      const payload: JwtPayload | null = parseJwtPayload(data.session.access_token);

      if (!payload) {
        console.warn('Failed to parse JWT payload from valid token');
        return null;
      }

      // Check for expiration since autoRefreshToken is disabled
      if (payload.exp * 1000 < Date.now()) {
          console.warn('JWT token has expired.');
          // Consider attempting a manual refresh or notifying the user
          // For now, return null as the session is effectively invalid
          return null;
      }

      // Handle missing tenant_id - use user ID as fallback (adjust if needed)
      if (!payload.tenant_id) {
        console.warn(`Missing tenant_id in JWT for user ${payload.sub}. Using user ID as fallback.`);
        payload.tenant_id = payload.sub; // Now safe due to typing
      }

      return payload; // Return the validated and typed payload
    }

    return null;
  } catch (_error) {
    console.error('Error getting JWT payload:', error);
    return null;
  }
};

// Helper to get current tenant ID from JWT
export const getCurrentTenantId = async (): Promise<string | null> => {
  const payload = await getJwtPayload();
  return payload?.tenant_id ?? null; // Accessing typed property
};

// Helper to get current user role from JWT
export const getCurrentUserRole = async (): Promise<string | null> => {
  const payload = await getJwtPayload();
  return payload?.role ?? null; // Accessing typed property
};

// DB helper for common operations with tenant context
export const db = {
  tasks: {
    // Create a new task
    async create(data: TaskData) {
      try {
        // Get session token
        const { data: { session }, error: sessionError } = await supabase.auth.getSession();

        // Prepare headers
        const headers: HeadersInit = {
          'Content-Type': 'application/json',
        };
        if (session?.access_token) {
          headers['Authorization'] = `Bearer ${session.access_token}`;
        } else {
          console.warn('[SupabaseClient] No access token found for creating task.');
          if (sessionError) {
             console.error('[SupabaseClient] Session error:', sessionError.message);
          }
        }

        const response = await fetch('/api/tasks', {
          method: 'POST',
          headers: headers, // Use prepared headers
          body: JSON.stringify(data),
        });

        if (!response.ok) {
          const errorData = await response.json();
          console.error('API error creating task:', errorData);
          throw new Error(`API error: ${response.status}`);
        }

        return await response.json();
      } catch (_error) {
        console.error('Error creating task via API:', error);
        throw error;
      }
    },

    // Get all tasks
    async getAll() {
      try {
        // Get session token
        const { data: { session }, error: sessionError } = await supabase.auth.getSession();

        // Prepare headers
        const headers: HeadersInit = {
          'Content-Type': 'application/json',
        };
        if (session?.access_token) {
          headers['Authorization'] = `Bearer ${session.access_token}`;
        } else {
          console.warn('[SupabaseClient] No access token found for fetching tasks.');
          if (sessionError) {
             console.error('[SupabaseClient] Session error:', sessionError.message);
          }
        }

        const response = await fetch('/api/tasks', {
          method: 'GET',
          headers: headers, // Use prepared headers
        });

        if (!response.ok) {
          let errorData;
          try {
            errorData = await response.json();
          } catch (_error) {
            // Log the parsing error
            console.error('Error parsing error response:', error);
            errorData = { message: 'Could not parse error response' };
          }
          console.error('API error fetching tasks:', errorData);

          // Return empty array instead of throwing to prevent UI from breaking
          return [];
        }

        let data;
        try {
          data = await response.json();
        } catch (_e) {
          console.error('Error parsing tasks API response:', e);
          // Return empty array on parse error
          return [];
        }

        return data.tasks || [];
      } catch (_error) {
        console.error('Error fetching tasks via API:', error);
        // Return empty array instead of throwing to prevent UI from breaking
        return [];
      }
    },

    // Update a task
    async update(id: string, data: Partial<TaskData>) {
      try {
        // Get session token
        const { data: { session }, error: sessionError } = await supabase.auth.getSession();

        // Prepare headers
        const headers: HeadersInit = {
          'Content-Type': 'application/json',
        };
        if (session?.access_token) {
          headers['Authorization'] = `Bearer ${session.access_token}`;
        } else {
          console.warn('[SupabaseClient] No access token found for updating task.');
          if (sessionError) {
             console.error('[SupabaseClient] Session error:', sessionError.message);
          }
        }

        const response = await fetch(`/api/tasks/${id}`, {
          method: 'PUT',
          headers: headers, // Use prepared headers
          body: JSON.stringify(data),
        });

        if (!response.ok) {
          const errorData = await response.json();
          console.error('API error updating task:', errorData);
          throw new Error(`API error: ${response.status}`);
        }

        return await response.json();
      } catch (_error) {
        console.error('Error updating task via API:', error);
        throw error;
      }
    },

    // Delete a task
    async delete(id: string) {
      try {
        // Get session token
        const { data: { session }, error: sessionError } = await supabase.auth.getSession();

        // Prepare headers
        const headers: HeadersInit = {}; // Content-Type not needed for DELETE
        if (session?.access_token) {
          headers['Authorization'] = `Bearer ${session.access_token}`;
        } else {
          console.warn('[SupabaseClient] No access token found for deleting task.');
          if (sessionError) {
             console.error('[SupabaseClient] Session error:', sessionError.message);
          }
        }

        const response = await fetch(`/api/tasks/${id}`, {
          method: 'DELETE',
          headers: headers, // Use prepared headers
        });

        if (!response.ok) {
          const errorData = await response.json();
          console.error('API error deleting task:', errorData);
          throw new Error(`API error: ${response.status}`);
        }

        return true;
      } catch (_error) {
        console.error('Error deleting task via API:', error);
        throw error;
      }
    },

    // Get task history
    async getHistory(taskId: string) {
      try {
        // Get session token
        const { data: { session }, error: sessionError } = await supabase.auth.getSession();

        // Prepare headers
        const headers: HeadersInit = {}; // Content-Type not needed for GET
        if (session?.access_token) {
          headers['Authorization'] = `Bearer ${session.access_token}`;
        } else {
          console.warn('[SupabaseClient] No access token found for fetching task history.');
          if (sessionError) {
             console.error('[SupabaseClient] Session error:', sessionError.message);
          }
        }

        const response = await fetch(`/api/tasks/${taskId}/history`, {
          method: 'GET',
          headers: headers, // Use prepared headers
        });

        if (!response.ok) {
          const errorData = await response.json();
          console.error('API error fetching task history:', errorData);
          throw new Error(`API error: ${response.status}`);
        }

        const data = await response.json();
        return data.history || [];
      } catch (_error) {
        console.error('Error fetching task history via API:', error);
        throw error;
      }
    },

    // Subscribe to task changes - This requires a different approach with WebSockets/SSE
    // For now, we'll return a dummy function that does nothing
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    subscribe(_callback: (payload: Task) => void) {
      // Using underscore prefix to indicate intentionally unused parameter
      console.warn('Real-time subscriptions not implemented via API routes. Use polling instead.');

      // Return a no-op function as the unsubscribe handler
      return () => {
        // No-op unsubscribe
      };
    }
  },
  users: {
    // Get all users
    async getAll() {
      try {
        // Get session token
        const { data: { session }, error: sessionError } = await supabase.auth.getSession();

        // Prepare headers
        const headers: HeadersInit = {}; // Content-Type not needed for GET
        if (session?.access_token) {
          headers['Authorization'] = `Bearer ${session.access_token}`;
        } else {
          console.warn('[SupabaseClient] No access token found for fetching users.');
          if (sessionError) {
             console.error('[SupabaseClient] Session error:', sessionError.message);
          }
        }

        const response = await fetch('/api/users', {
          method: 'GET',
          headers: headers, // Use prepared headers
        });

        if (!response.ok) {
          const errorData = await response.json();
          console.error('API error fetching users:', errorData);
          throw new Error(`API error: ${response.status}`);
        }

        const data = await response.json();

        // Process and normalize the data
        return (data.users || []).map((user: Record<string, unknown>) => ({
          ...user,
          full_name: `${user.first_name || ''} ${user.last_name || ''}`.trim() || 'Unnamed User',
          created_at: user.created_at && typeof user.created_at === 'string' ? new Date(user.created_at).toISOString() : null,
          updated_at: user.updated_at && typeof user.updated_at === 'string' ? new Date(user.updated_at).toISOString() : null
        }));
      } catch (_error) {
        console.error('Error fetching users via API:', error);
        throw error;
      }
    }
  },
  cases: {
    // Get all cases
    async getAll() {
      try {
        // Get session token
        const { data: { session }, error: sessionError } = await supabase.auth.getSession();

        // Prepare headers
        const headers: HeadersInit = {}; // Content-Type not needed for GET
        if (session?.access_token) {
          headers['Authorization'] = `Bearer ${session.access_token}`;
        } else {
          console.warn('[SupabaseClient] No access token found for fetching cases.');
          if (sessionError) {
             console.error('[SupabaseClient] Session error:', sessionError.message);
          }
        }

        const response = await fetch('/api/cases', {
          method: 'GET',
          headers: headers, // Use prepared headers
        });

        if (!response.ok) {
          let errorData;
          try {
            errorData = await response.json();
          } catch (_error) {
            // Log the parsing error
            console.error('Error parsing error response:', error);
            errorData = { message: 'Could not parse error response' };
          }
          console.error('API error fetching cases:', errorData);

          // Return empty array instead of throwing to prevent UI from breaking
          return [];
        }

        let data;
        try {
          data = await response.json();
          // Debug the response structure
          console.log('API response from /api/cases:', data);
        } catch (_error) {
          console.error('Error parsing cases API response:', error);
          // Return empty array on parse error
          return [];
        }

        // Ensure data.cases is an array before trying to map over it
        const casesArray = Array.isArray(data.cases) ? data.cases :
                          (data.cases ? [data.cases] : []);

        // Process and normalize the data with safe handling
        return casesArray.map((cas: Record<string, unknown>) => {
          try {
            return {
              ...cas,
              client_name: cas.client && typeof cas.client === 'object' ?
                `${(cas.client as any).first_name || ''} ${(cas.client as any).last_name || ''}`.trim() : 'Unknown Client',
              attorney_name: cas.assigned_attorney && typeof cas.assigned_attorney === 'object' ?
                `${(cas.assigned_attorney as any).first_name || ''} ${(cas.assigned_attorney as any).last_name || ''}`.trim() : 'Unassigned',
              paralegal_name: cas.assigned_paralegal && typeof cas.assigned_paralegal === 'object' ?
                `${(cas.assigned_paralegal as any).first_name || ''} ${(cas.assigned_paralegal as any).last_name || ''}`.trim() : 'Unassigned',
              created_at: cas.created_at && typeof cas.created_at === 'string' ? new Date(cas.created_at).toISOString() : null,
              updated_at: cas.updated_at && typeof cas.updated_at === 'string' ? new Date(cas.updated_at).toISOString() : null
            };
          } catch (_e) {
            console.warn('Error processing case data:', e);
            // Return a minimal valid case object if processing fails
            return {
              id: cas.id || 'unknown',
              title: cas.title || 'Unknown Case',
              status: cas.status || 'unknown',
              client_name: 'Unknown Client',
              attorney_name: 'Unassigned',
              paralegal_name: 'Unassigned',
              created_at: null,
              updated_at: null
            };
          }
        });
      } catch (_error) {
        console.error('Error fetching cases via API:', error);
        // Return empty array instead of throwing to prevent UI from breaking
        return [];
      }
    }
  },
  clients: {
    // Get all clients
    async getAll(options: {
      page?: number;
      limit?: number;
      status?: string;
      client_type?: string;
      searchTerm?: string;
    } = {}) {
      try {
        // Construct query params
        const params = new URLSearchParams();
        if (options.page) params.append('page', options.page.toString());
        if (options.limit) params.append('limit', options.limit.toString());
        if (options.status) params.append('status', options.status);
        if (options.client_type) params.append('client_type', options.client_type);
        if (options.searchTerm) params.append('searchTerm', options.searchTerm);

        const queryString = params.toString();
        const apiUrl = `/api/clients${queryString ? `?${queryString}` : ''}`;

        // Get session token
        const { data: { session }, error: sessionError } = await supabase.auth.getSession();

        // Prepare headers
        const headers: HeadersInit = {
          'Content-Type': 'application/json',
        };
        if (session?.access_token) {
          headers['Authorization'] = `Bearer ${session.access_token}`;
        } else {
          console.warn('[SupabaseClient] No access token found for fetching clients.');
          if (sessionError) {
             console.error('[SupabaseClient] Session error:', sessionError.message);
          }
        }

        const response = await fetch(apiUrl, { // Use constructed URL
          method: 'GET',
          headers: headers, // Pass prepared headers
        });

        if (!response.ok) {
          const errorData = await response.json();
          console.error('API error fetching clients:', errorData);
          throw new Error(`API error: ${response.status}`);
        }

        return await response.json();
      } catch (_error) {
        console.error('Error fetching clients via API:', error);
        throw error;
      }
    },

    // Get a single client by ID
    async getById(id: string) {
      try {
        // Get session token
        const { data: { session }, error: sessionError } = await supabase.auth.getSession();

        // Prepare headers
        const headers: HeadersInit = {}; // Content-Type not needed for GET
        if (session?.access_token) {
          headers['Authorization'] = `Bearer ${session.access_token}`;
        } else {
          console.warn('[SupabaseClient] No access token found for fetching client by ID.');
          if (sessionError) {
             console.error('[SupabaseClient] Session error:', sessionError.message);
          }
        }

        const response = await fetch(`/api/clients/${id}`, {
          method: 'GET',
          headers: headers, // Use prepared headers
        });

        if (!response.ok) {
          const errorData = await response.json();
          console.error(`API error fetching client ${id}:`, errorData);
          throw new Error(`API error: ${response.status}`);
        }

        const data = await response.json();
        return data.client;
      } catch (_error) {
        console.error(`Error fetching client ${id} via API:`, error);
        throw error;
      }
    },

    // Create a new client
    async create(data: ClientData) {
      try {
        // Get session token
        const { data: { session }, error: sessionError } = await supabase.auth.getSession();

        // Prepare headers
        const headers: HeadersInit = {
          'Content-Type': 'application/json',
        };
        if (session?.access_token) {
          headers['Authorization'] = `Bearer ${session.access_token}`;
        } else {
          console.warn('[SupabaseClient] No access token found for creating client.');
          if (sessionError) {
             console.error('[SupabaseClient] Session error:', sessionError.message);
          }
        }

        const response = await fetch('/api/clients', {
          method: 'POST',
          headers: headers, // Pass prepared headers
          body: JSON.stringify(data),
        });

        if (!response.ok) {
          const errorData = await response.json();
          console.error('API error creating client:', errorData);
          throw new Error(`API error: ${response.status}`);
        }

        return await response.json();
      } catch (_error) {
        console.error('Error creating client via API:', error);
        throw error;
      }
    },

    // Update a client
    async update(id: string, data: Partial<ClientData>) {
      try {
        // Get session token
        const { data: { session }, error: sessionError } = await supabase.auth.getSession();

        // Prepare headers
        const headers: HeadersInit = {
          'Content-Type': 'application/json',
        };
        if (session?.access_token) {
          headers['Authorization'] = `Bearer ${session.access_token}`;
        } else {
          console.warn('[SupabaseClient] No access token found for updating client.');
          if (sessionError) {
             console.error('[SupabaseClient] Session error:', sessionError.message);
          }
        }

        const response = await fetch(`/api/clients/${id}`, {
          method: 'PUT',
          headers: headers, // Use prepared headers
          body: JSON.stringify(data),
        });

        if (!response.ok) {
          const errorData = await response.json();
          console.error(`API error updating client ${id}:`, errorData);
          throw new Error(`API error: ${response.status}`);
        }

        return await response.json();
      } catch (_error) {
        console.error(`Error updating client ${id} via API:`, error);
        throw error;
      }
    },

    // Delete a client
    async delete(id: string) {
      try {
        // Get session token
        const { data: { session }, error: sessionError } = await supabase.auth.getSession();

        // Prepare headers
        const headers: HeadersInit = {}; // Content-Type not needed for DELETE
        if (session?.access_token) {
          headers['Authorization'] = `Bearer ${session.access_token}`;
        } else {
          console.warn('[SupabaseClient] No access token found for deleting client.');
          if (sessionError) {
             console.error('[SupabaseClient] Session error:', sessionError.message);
          }
        }

        const response = await fetch(`/api/clients/${id}`, {
          method: 'DELETE',
          headers: headers, // Use prepared headers
        });

        if (!response.ok) {
          const errorData = await response.json();
          console.error(`API error deleting client ${id}:`, errorData);
          throw new Error(`API error: ${response.status}`);
        }

        return await response.json();
      } catch (_error) {
        console.error(`Error deleting client ${id} via API:`, error);
        throw error;
      }
    }
  }
}

/**
 * Get complete user data with JWT claims
 * This is useful for debugging purposes
 */
export async function getNormalizedUserData() {
  if (!supabase) return null

  try {
    const { data: session, error } = await supabase.auth.getSession()
    if (error || !session) return null

    const { data: userData, error: userError } = await supabase.auth.getUser()
    if (userError || !userData) return null

    const jwtClaims = session.session?.access_token
      ? parseJwtPayload(session.session.access_token)
      : null

    return {
      // Auth data
      id: userData.user?.id,
      email: userData.user?.email,
      emailVerified: userData.user?.email_confirmed_at ? true : false,

      // JWT claims
      role: jwtClaims?.role || null,
      tenantId: jwtClaims?.tenant_id || null,

      // Raw data for debugging
      user: userData.user,
      claims: jwtClaims
    }
  } catch (_error) {
    console.error('Error getting normalized user data:', error)
    return null
  }
}

// Types
export interface Task {
  id: string
  tenant_id: string
  title: string
  description?: string
  due_date?: string
  status: 'todo' | 'in_progress' | 'done'
  assigned_to?: string
  assigned_to_user?: { id: string; email: string }
  related_case_id?: string
  related_case?: { id: string; title: string }
  ai_metadata?: Record<string, unknown>
  created_by: string
  creator?: { id: string; email: string }
  created_at: string
  updated_by?: string
  updater?: { id: string; email: string }
  updated_at: string
}

// Type for creating or updating a task
export interface TaskData {
  title: string
  description?: string
  due_date?: string
  status?: 'todo' | 'in_progress' | 'done'
  assigned_to?: string
  related_case_id?: string
  ai_metadata?: Record<string, unknown>
}

export interface TaskHistory {
  id: string
  task_id: string
  tenant_id: string
  changed_by: string
  changed_at: string
  change_type: 'created' | 'updated' | 'status_changed' | 'assigned' | 'deleted'
  previous_values?: Partial<Task>
  new_values?: Partial<Task>
  metadata?: Record<string, unknown>
}

// Type for client data
export interface ClientData {
  first_name: string
  last_name: string
  email?: string
  phone?: string
  address?: string
  city?: string
  state?: string
  zip?: string
  status?: string
  client_type?: string
  notes?: string
  [key: string]: unknown
}
