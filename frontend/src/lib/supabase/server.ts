import { createServerClient } from '@supabase/ssr'
import { cookies } from 'next/headers'
import { type Database } from './database.types'
import { CookieOptions } from '@supabase/ssr'
import { TypedSupabaseClient } from './client-types'

/**
 * Creates a Supabase client for use in server components
 *
 * This function manages cookies through the Next.js cookies() API.
 *
 * @returns A type-safe Supabase client
 */
export function createClient(): TypedSupabaseClient {
  const client = createServerClient<Database>(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        // Define cookie methods using type assertions to avoid TypeScript errors
        // This approach is safer than upgrading Next.js
        get(name: string) {
          // Get the cookie store - use synchronous version for SSR
          const cookieStore = cookies();
          // Use type assertion to handle the cookies API
          return (cookieStore as any).get(name)?.value;
        },

        // Set cookie method with proper typing
        set(name: string, value: string, options: CookieOptions) {
          // Get the cookie store - use synchronous version for SSR
          const cookieStore = cookies();

          // Normalize cookie options
          const cookieOptions: Record<string, unknown> = {};

          // Only include defined options
          if (options.path) cookieOptions.path = options.path;
          if (options.maxAge) cookieOptions.maxAge = options.maxAge;
          if (options.domain) cookieOptions.domain = options.domain;
          if (options.secure !== undefined) cookieOptions.secure = options.secure;
          if (options.httpOnly !== undefined) cookieOptions.httpOnly = options.httpOnly;
          if (options.sameSite) cookieOptions.sameSite = options.sameSite;
          if (options.expires) cookieOptions.expires = options.expires;

          // Use set method with type assertion
          (cookieStore as any).set(name, value, cookieOptions);
        },

        // Remove cookie method with proper typing
        remove(name: string, options: CookieOptions) {
          // Get the cookie store - use synchronous version for SSR
          const cookieStore = cookies();

          // Normalize cookie options
          const cookieOptions: Record<string, unknown> = {};

          // Only include defined options
          if (options.path) cookieOptions.path = options.path;
          if (options.domain) cookieOptions.domain = options.domain;
          if (options.secure !== undefined) cookieOptions.secure = options.secure;
          if (options.httpOnly !== undefined) cookieOptions.httpOnly = options.httpOnly;
          if (options.sameSite) cookieOptions.sameSite = options.sameSite;

          // Use delete method with type assertion
          (cookieStore as any).delete(name, cookieOptions);
        },
      },
      global: {
        fetch: async (url, options) => {
          // Add retry logic for fetch operations
          const maxRetries = 3;
          let retries = 0;
          let lastError: Error | unknown;

          while (retries < maxRetries) {
            try {
              return await fetch(url, options);
            } catch (_error) {
              lastError = error;
              retries++;
              console.warn(`Supabase fetch failed, retry attempt ${retries}/${maxRetries}`, error);

              // Exponential backoff: 500ms, 1000ms, 2000ms, etc.
              const delay = Math.min(500 * Math.pow(2, retries - 1), 5000);
              await new Promise(resolve => setTimeout(resolve, delay));
            }
          }

          console.error('All Supabase fetch retry attempts failed:', lastError);
          throw lastError;
        }
      }
    }
  );

  // Cast to our extended type for TypeScript support
  return client as unknown as TypedSupabaseClient;
}

// Create a client with service role for admin operations
export function createServiceClient(): TypedSupabaseClient {
  // For service role, we don't need cookies
  const client = createServerClient<Database>(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.SUPABASE_SERVICE_KEY!,
    {
      // Use empty cookie methods since we're using service role
      cookies: {
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        get(_name: string) {
          return undefined
        },
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        set(_name: string, _value: string, _options: CookieOptions) {
          // No-op - intentionally empty
        },
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        remove(_name: string, _options: CookieOptions) {
          // No-op - intentionally empty
        },
      },
      global: {
        fetch: async (url, options) => {
          // Add retry logic for fetch operations
          const maxRetries = 3;
          let retries = 0;
          let lastError: Error | unknown;

          while (retries < maxRetries) {
            try {
              return await fetch(url, options);
            } catch (_error) {
              lastError = error;
              retries++;
              console.warn(`Supabase service fetch failed, retry attempt ${retries}/${maxRetries}`, error);

              // Exponential backoff: 500ms, 1000ms, 2000ms, etc.
              const delay = Math.min(500 * Math.pow(2, retries - 1), 5000);
              await new Promise(resolve => setTimeout(resolve, delay));
            }
          }

          console.error('All Supabase service fetch retry attempts failed:', lastError);
          throw lastError;
        }
      }
    }
  );

  // Cast to our extended type for TypeScript support
  return client as unknown as TypedSupabaseClient;
}

// Optional: Create a client for server components
export function createServerComponentClient(): TypedSupabaseClient {
  return createClient()
}

// Optional: Create a client for route handlers
export function createRouteHandlerClient(): TypedSupabaseClient {
  return createClient()
}

// Create a service role client for route handlers
export function createServiceRouteHandlerClient(): TypedSupabaseClient {
  return createServiceClient()
}
