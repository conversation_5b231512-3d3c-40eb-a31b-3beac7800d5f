/**
 * Utility functions for working with Supabase
 */

import { PostgrestSingleResponse, PostgrestResponse, SupabaseClient } from '@supabase/supabase-js';

/**
 * Helper function to execute Supabase queries that might cause
 * "type instantiation is excessively deep" TypeScript errors.
 * This is a simplified version that avoids complex type juggling.
 *
 * @param queryFn Function that executes the Supabase query
 * @returns The data result or null if an error occurred
 */
export async function execSafeQuery<T>(queryFn: () => any): Promise<T | null> {
  try {
    const { data, error } = await queryFn();
    if (error) throw error;
    return data as T;
  } catch (_err) {
    console.error('Query execution error:', err);
    return null;
  }
}

/**
 * Helper function that returns the count from a Supabase count query
 * Simplified to avoid TypeScript depth issues
 *
 * @param queryFn Function that executes a Supabase count query
 * @returns The count result or 0 if an error occurred
 */
export async function execCountQuery(queryFn: () => any): Promise<number> {
  try {
    const { count, error } = await queryFn();
    if (error) throw error;
    return count || 0;
  } catch (_err) {
    console.error('Count query execution error:', err);
    return 0;
  }
}

/**
 * Helper function for safely working with RPC functions
 * that may not be fully typed in the generated TypeScript definitions
 *
 * @param rpcFn Function that executes an RPC call
 * @returns The data result or null if an error occurred
 */
export async function execRpcQuery<T>(rpcFn: () => any): Promise<T | null> {
  try {
    const { data, error } = await rpcFn();
    if (error) throw error;
    return data as T;
  } catch (_err) {
    console.error('RPC execution error:', err);
    return null;
  }
}
