import type { DocumentTemplate, TemplateVariable, DocumentSection } from '@/lib/documents/templates'

// Define the Supabase template interface
export interface SupabaseTemplate {
  id: string;
  name: string;
  description?: string | null;
  content?: string | null;
  variables?: string | Record<string, TemplateVariable[]>;
  sections?: string | DocumentSection[];
  category?: string | null;
  practice_area?: string | null;
  state?: string | null;
  document_type?: string | null;
  is_active: boolean;
  version?: number;
  created_at: string;
  updated_at?: string | null;
  [key: string]: any;
}

// Mapping function to convert from Supabase schema to our app schema
export function mapSupabaseTemplateToAppTemplate(template: SupabaseTemplate | null): DocumentTemplate {
  if (!template) return null as unknown as DocumentTemplate;

  // Parse variables from JSON string if needed
  let variables: Record<string, TemplateVariable[]> = {};
  if (typeof template.variables === 'string') {
    try {
      variables = JSON.parse(template.variables);
    } catch (_e) {
      console.error('Error parsing template variables:', e);
    }
  } else if (template.variables) {
    variables = template.variables as Record<string, TemplateVariable[]>;
  }

  // Parse sections from JSON string if needed
  let sections: DocumentSection[] = [];
  if (typeof template.sections === 'string') {
    try {
      sections = JSON.parse(template.sections);
    } catch (_e) {
      console.error('Error parsing template sections:', e);
    }
  } else if (template.sections) {
    sections = template.sections as DocumentSection[];
  }

  // Create a document template with type assertions for the enum values
  return {
    id: template.id,
    name: template.name,
    description: template.description || '',
    // Variables should be an array in DocumentTemplate
    variables: Object.values(variables).flat(),
    sections,
    // Type assertions for enum values
    category: (template.category || 'other') as DocumentTemplate['category'],
    documentType: (template.document_type || 'case-specific') as DocumentTemplate['documentType'],
    jurisdiction: template.state || undefined,
    createdAt: template.created_at,
    updatedAt: template.updated_at || ''
  } as DocumentTemplate
}

// Helper to map app schema back to Supabase schema
export function mapAppTemplateToSupabaseTemplate(template: DocumentTemplate): Partial<SupabaseTemplate> {
  // Convert variables array to Record format expected by Supabase
  const variablesRecord: Record<string, TemplateVariable[]> = {
    'default': template.variables
  };

  return {
    name: template.name,
    description: template.description,
    variables: variablesRecord,
    sections: template.sections,
    category: template.category,
    practice_area: template.jurisdiction,
    state: template.jurisdiction,
    document_type: template.documentType,
    is_active: true,
    version: 1
  }
}

// Helper to map template category to UI-friendly name
export function getTemplateCategoryDisplayName(category: string): string {
  const categoryMap: Record<string, string> = {
    'Client Intake & Engagement': 'Client Intake',
    'Pre-Litigation': 'Pre-Litigation',
    'Litigation': 'Litigation',
    'Discovery': 'Discovery',
    'Settlement': 'Settlement',
    'Trial': 'Trial',
    'Post-Trial': 'Post-Trial',
    'General Operations': 'Operations',
    'Marketing': 'Marketing',
    'Finance': 'Finance',
    'HR': 'HR',
    'Other': 'Other'
  }

  return categoryMap[category] || 'Other'
}

// Template service for interacting with templates in Supabase
export const templateService = {
  getAllAvailable: async () => {
    return []
  },
  getById: async () => {
    return null
  },
  create: async () => {
    return null
  },
  update: async () => {
    return null
  },
  delete: async () => {
    return false
  }
}



// Export a client-side React hook version of the template service
// that integrates with UserContext
export function useClientTemplateService() {
  return {
    ...templateService
  }
}
