// Correctly import types using relative paths
import type { Database, Json } from '../../lib/supabase/database.types';
import type { Deadline, DeadlineInput } from '../../types/domain/tenants/Deadline';

// Get the actual database types from Supabase generated types
// Looking at the errors, our table appears to have a different structure
type TaskDeadlineRow = Database['tenants']['Tables']['task_deadlines']['Row'];
type TaskDeadlineInsert = Database['tenants']['Tables']['task_deadlines']['Insert'];

/**
 * Maps a task_deadlines row from the database to our frontend Deadline domain model
 * This isolates all field name transformations in one place
 */
export function mapDeadlineFromDb(row: TaskDeadlineRow): Deadline {
  // Use the rule_citation as legal basis
  // And potentially extract other fields from calc_steps as needed
  const calcSteps = row.calc_steps as Json;

  return {
    id: row.id,
    tenantId: row.tenant_id,
    // Map fields that have different names or don't exist directly
    taskId: row.task_id,
    dueDate: row.deadline_date,         // deadline_date in DB -> dueDate in domain model
    description: extractDescription(calcSteps), // Extract from calc_steps
    jurisdiction: extractJurisdiction(calcSteps), // Extract from calc_steps
    legalBasis: row.rule_citation,      // rule_citation in DB -> legalBasis in domain model
    overridden: row.overridden || false,
    createdAt: row.created_at,
    updatedAt: row.updated_at,

    // These would need to be manually managed or from a different table
    priority: 'medium', // Default value
    validationStatus: 'pending', // Default value
    validationNote: null,
    documentId: null,
    caseId: null,
    validatedBy: null,
    validatedAt: null,
    createdBy: '',  // Not available directly
    updatedBy: null,
    document: null,
    case: null,
    consequences: null
  };
}

/**
 * Extract description from calc_steps JSON if available
 */
function extractDescription(calcSteps: Json | null): string {
  if (!calcSteps || typeof calcSteps !== 'object') {
    return 'Deadline';
  }

  try {
    // Attempt to extract description from calc_steps
    // This will depend on your actual JSON structure
    return (calcSteps as any).description ||
           (calcSteps as any).title ||
           'Deadline';
  } catch (_err) {
    return 'Deadline';
  }
}

/**
 * Extract jurisdiction from calc_steps JSON if available
 */
function extractJurisdiction(calcSteps: Json | null): string {
  if (!calcSteps || typeof calcSteps !== 'object') {
    return 'Unknown';
  }

  try {
    // Attempt to extract jurisdiction from calc_steps
    // This will depend on your actual JSON structure
    return (calcSteps as any).jurisdiction ||
           (calcSteps as any).court ||
           'Unknown';
  } catch (_err) {
    return 'Unknown';
  }
}

/**
 * Maps multiple deadline records from the database
 */
export function mapDeadlinesFromDb(rows: TaskDeadlineRow[]): Deadline[] {
  return rows.map(mapDeadlineFromDb);
}

/**
 * Maps a domain deadline model to a database insert object
 * This prepares the data for insertion into the database
 */
export function mapDeadlineToDb(deadline: DeadlineInput, userId: string, tenantId: string, taskId: string): TaskDeadlineInsert {
  // Store extra metadata in calc_steps JSON
  const calcSteps: Json = {
    description: deadline.description,
    jurisdiction: deadline.jurisdiction,
    consequences: deadline.consequences,
    created_by: userId
  };

  return {
    deadline_date: deadline.dueDate,
    task_id: taskId,
    tenant_id: tenantId,
    rule_citation: deadline.legalBasis || null,
    calc_steps: calcSteps,
    overridden: false
  };
}

/**
 * Maps a domain deadline update to a database update object
 * Only includes fields that can be updated by users
 */
export function mapDeadlineUpdateToDb(
  deadline: Partial<DeadlineInput>,
  userId: string,
  originalDeadline: TaskDeadlineRow
): Partial<TaskDeadlineInsert> {
  const update: Partial<TaskDeadlineInsert> = {};

  // Only update specific fields
  if (deadline.dueDate !== undefined) {
    update.deadline_date = deadline.dueDate;
  }

  if (deadline.legalBasis !== undefined) {
    update.rule_citation = deadline.legalBasis;
  }

  // Update calc_steps JSON with any changed fields
  if (deadline.description !== undefined ||
      deadline.jurisdiction !== undefined ||
      deadline.consequences !== undefined) {

    // Start with original calc_steps
    const calcSteps = { ...originalDeadline.calc_steps as any };

    // Update only the changed fields
    if (deadline.description !== undefined) {
      calcSteps.description = deadline.description;
    }

    if (deadline.jurisdiction !== undefined) {
      calcSteps.jurisdiction = deadline.jurisdiction;
    }

    if (deadline.consequences !== undefined) {
      calcSteps.consequences = deadline.consequences;
    }

    // Add updated_by information
    calcSteps.updated_by = userId;
    calcSteps.updated_at = new Date().toISOString();

    update.calc_steps = calcSteps;
  }

  // Set overridden to true since we're manually updating it
  update.overridden = true;

  return update;
}

/**
 * Maps a validation action to metadata in calc_steps
 * Since we don't have direct validation fields, we'll store in calc_steps
 */
export function mapDeadlineValidationToDb(
  action: 'validate' | 'reject',
  note: string | null,
  userId: string,
  originalDeadline: TaskDeadlineRow
): Partial<TaskDeadlineInsert> {
  // Get current calc_steps
  const calcSteps = { ...originalDeadline.calc_steps as any };

  // Add validation fields to calc_steps
  calcSteps.validation_status = action === 'validate' ? 'validated' : 'rejected';
  calcSteps.validation_note = note;
  calcSteps.validated_by = userId;
  calcSteps.validated_at = new Date().toISOString();

  return {
    calc_steps: calcSteps,
    // Indicate this was manually validated
    overridden: true
  };
}
